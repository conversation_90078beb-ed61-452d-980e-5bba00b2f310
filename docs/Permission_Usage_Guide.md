# HarmonyOS 权限使用指南

## 概述

本文档详细介绍了项目中权限管理的实现方案，包括权限配置、权限管理工具类的使用、以及权限示例页面的功能。

## 1. 权限配置

### 1.1 module.json5 权限声明

项目在 `entry/src/main/module.json5` 中声明了以下权限：

```json5
"requestPermissions": [
  {
    "name": "ohos.permission.INTERNET"
  },
  {
    "name": "ohos.permission.CAMERA",
    "reason": "$string:permission_camera_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.READ_MEDIA",
    "reason": "$string:permission_read_media_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.WRITE_MEDIA",
    "reason": "$string:permission_write_media_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.MEDIA_LOCATION",
    "reason": "$string:permission_media_location_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.READ_IMAGEVIDEO",
    "reason": "$string:permission_read_imagevideo_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.WRITE_IMAGEVIDEO",
    "reason": "$string:permission_write_imagevideo_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.MICROPHONE",
    "reason": "$string:permission_microphone_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  },
  {
    "name": "ohos.permission.LOCATION",
    "reason": "$string:permission_location_reason",
    "usedScene": {
      "abilities": ["DDMainbility"],
      "when": "inuse"
    }
  }
]
```

### 1.2 权限说明文本

在 `entry/src/main/resources/base/element/string.json` 中定义了权限申请的说明文本：

| 权限名称 | 说明文本 |
|---------|---------|
| permission_camera_reason | 需要使用相机权限来拍照和录制视频 |
| permission_read_media_reason | 需要读取媒体文件权限来访问相册中的图片和视频 |
| permission_write_media_reason | 需要写入媒体文件权限来保存拍摄的照片和视频 |
| permission_media_location_reason | 需要媒体位置权限来获取照片的地理位置信息 |
| permission_read_imagevideo_reason | 需要读取图片视频权限来选择和预览媒体文件 |
| permission_write_imagevideo_reason | 需要写入图片视频权限来保存编辑后的媒体文件 |
| permission_microphone_reason | 需要麦克风权限来录制音频和语音消息 |
| permission_location_reason | 需要位置权限来获取当前位置信息 |

## 2. 权限管理工具类

### 2.1 PermissionManager 类

位置：`entry/src/main/ets/util/PermissionManager.ets`

这是一个单例模式的权限管理工具类，提供了完整的权限检查和申请功能。

#### 2.1.1 主要方法

**单例获取**
```typescript
const permissionManager = PermissionManager.getInstance();
```

**检查单个权限**
```typescript
async checkPermission(permission: Permissions): Promise<boolean>
```

**检查多个权限**
```typescript
async checkPermissions(permissions: Permissions[]): Promise<Map<string, boolean>>
```

**申请单个权限**
```typescript
async requestPermission(context: common.UIAbilityContext, permission: Permissions): Promise<boolean>
```

**申请多个权限**
```typescript
async requestPermissions(context: common.UIAbilityContext, permissions: Permissions[]): Promise<Map<string, boolean>>
```

#### 2.1.2 便捷方法

**相机权限**
```typescript
// 检查相机权限
async checkCameraPermission(): Promise<boolean>

// 申请相机权限
async requestCameraPermission(context: common.UIAbilityContext): Promise<boolean>
```

**媒体权限**
```typescript
// 检查媒体权限（包含读取、写入、图片视频权限）
async checkMediaPermissions(): Promise<Map<string, boolean>>

// 申请媒体权限
async requestMediaPermissions(context: common.UIAbilityContext): Promise<Map<string, boolean>>
```

**麦克风权限**
```typescript
// 检查麦克风权限
async checkMicrophonePermission(): Promise<boolean>

// 申请麦克风权限
async requestMicrophonePermission(context: common.UIAbilityContext): Promise<boolean>
```

**位置权限**
```typescript
// 检查位置权限
async checkLocationPermission(): Promise<boolean>

// 申请位置权限
async requestLocationPermission(context: common.UIAbilityContext): Promise<boolean>
```

#### 2.1.3 工具方法

**获取权限状态描述**
```typescript
getPermissionStatusText(permission: string, granted: boolean): string
```

### 2.2 使用示例

```typescript
import { permissionManager } from "../../util/PermissionManager";
import { common } from '@kit.AbilityKit';

// 在组件中使用
export struct MyComponent {
  async requestCameraPermission() {
    try {
      const context = getContext(this) as common.UIAbilityContext;
      const granted = await permissionManager.requestCameraPermission(context);
      
      if (granted) {
        console.log('相机权限申请成功');
        // 执行需要相机权限的操作
      } else {
        console.log('相机权限申请失败');
      }
    } catch (error) {
      console.error('申请相机权限出错:', error);
    }
  }
}
```

## 3. 权限示例页面

### 3.1 页面位置

- 文件路径：`entry/src/main/ets/pages/permission/PermissionDemoPage.ets`
- 页面配置：已添加到 `main_pages.json` 中
- 访问入口：工作台 → 权限示例

### 3.2 页面功能

1. **权限状态展示**
   - 实时显示各种权限的授权状态
   - 支持权限状态刷新

2. **权限申请演示**
   - 相机权限申请
   - 媒体权限批量申请

3. **功能演示**
   - 拍照功能（需要相机权限）
   - 相册选择功能（需要媒体读取权限）

### 3.3 页面特性

- 使用 `@ComponentV2` 和 `@Local` 状态管理
- 集成 `CommonToolbar` 通用工具栏
- 响应式UI设计，支持加载状态显示
- 完整的错误处理和用户提示

## 4. 工作台集成

### 4.1 权限示例工具

在 `WorkbenchViewModel.ets` 中添加了本地工具支持：

```typescript
// 权限示例工具
const permissionDemoTool = new WorkBenchUIData({
  id: 'local_permission_demo',
  name: '权限示例',
  icon: '/resources/base/media/im_newsHome_search.png',
  webUrl: '', // 本地页面不需要webUrl
  type: 'tool',
  key: 'permission_demo'
});
```

### 4.2 页面跳转

点击工作台中的"权限示例"工具时，会跳转到权限示例页面：

```typescript
if (tool.key === 'permission_demo') {
  // 跳转到权限示例页面
  routeTo(context, 'pages/permission/PermissionDemoPage', null);
}
```

## 5. 最佳实践

### 5.1 权限申请时机

1. **按需申请**：只在用户需要使用相关功能时才申请权限
2. **用户友好**：在申请权限前向用户说明权限用途
3. **优雅降级**：权限被拒绝时提供替代方案

### 5.2 错误处理

```typescript
try {
  const granted = await permissionManager.requestCameraPermission(context);
  if (granted) {
    // 权限获取成功，执行相关操作
  } else {
    // 权限被拒绝，提示用户或提供替代方案
    promptAction.showToast({ message: '需要相机权限才能使用此功能' });
  }
} catch (error) {
  // 处理异常情况
  console.error('权限申请异常:', error);
}
```

### 5.3 权限检查

在执行需要权限的操作前，先检查权限状态：

```typescript
const hasPermission = await permissionManager.checkCameraPermission();
if (!hasPermission) {
  // 申请权限
  const granted = await permissionManager.requestCameraPermission(context);
  if (!granted) {
    return; // 权限未获取，终止操作
  }
}
// 执行需要权限的操作
```

## 6. 注意事项

1. **Context 类型**：使用 `common.UIAbilityContext` 而不是 `UIAbilityContext`
2. **导入路径**：使用 `import { common } from '@kit.AbilityKit'`
3. **getContext 使用**：在组件中使用 `getContext(this)`
4. **权限组合**：媒体相关权限通常需要组合申请
5. **用户体验**：避免在应用启动时批量申请所有权限

## 7. 扩展建议

1. **权限缓存**：可以考虑缓存权限状态，避免频繁检查
2. **权限引导**：为重要权限添加引导页面
3. **权限监听**：监听权限状态变化，及时更新UI
4. **权限分组**：根据功能模块对权限进行分组管理

---

本文档涵盖了项目中权限管理的完整实现方案，开发者可以参考此文档进行权限相关功能的开发和维护。
