
import { WebViewParam } from './WebViewHelper';

// 全局对象存储，用于保持类型
class RouteParamStore {
  private static instance: RouteParamStore | null = null;
  private store: Map<string, ESObject> = new Map();

  static getInstance(): RouteParamStore {
    if (!RouteParamStore.instance) {
      RouteParamStore.instance = new RouteParamStore();
    }
    return RouteParamStore.instance;
  }

  setParam(key: string, param: ESObject): void {
    this.store.set(key, param);
  }

  getParam(key: string): ESObject | null {
    const param: ESObject | undefined = this.store.get(key);
    this.store.delete(key); // 使用后删除
    return param ? param : null;
  }
}

/// 增强的路由函数，支持类型保持
export function routeTo(context: UIContext, path: string, param?: ESObject) {
  if (param) {
    // 生成唯一key
    const paramKey = `${path}_${Date.now()}_${Math.random()}`;

    // 将原始对象存储到全局store
    RouteParamStore.getInstance().setParam(paramKey, param);

    // 通过路由传递key和类型信息
    context.getRouter().pushUrl({
      url: path,
      params: {
        _paramKey: paramKey,
        _paramType: param.constructor.name,
        // 同时传递序列化数据作为备份
        _serializedData: param
      }
    });
  } else {
    context.getRouter().pushUrl({
      url: path,
      params: param
    });
  }
}

/// 获取路由参数，保持原类型
export function getRouteParam<T>(context: UIContext): T | null {
  const params = context.getRouter().getParams();

  if (params && typeof params === 'object') {
    const paramObj = params as Record<string, ESObject>;

    // 如果有paramKey，从store获取原始对象
    if (paramObj._paramKey && typeof paramObj._paramKey === 'string') {
      const store = RouteParamStore.getInstance();
      const originalParam: ESObject | null = store.getParam(paramObj._paramKey);
      if (originalParam) {
        return originalParam as T;
      }
    }

    // 如果store中没有，尝试从序列化数据重建
    if (paramObj._serializedData && paramObj._paramType && typeof paramObj._paramType === 'string') {
      // 确保数据是对象类型
      if (typeof paramObj._serializedData === 'object' && paramObj._serializedData !== null) {
        const serializedData: ESObject = paramObj._serializedData as ESObject;
        const paramType: string = paramObj._paramType as string;
        const reconstructed = reconstructObject(serializedData, paramType);
        if (reconstructed) {
          return reconstructed as T;
        }
      }
    }

    // 兜底：返回序列化数据
    if (paramObj._serializedData) {
      return paramObj._serializedData as T;
    }

    return params as T;
  }

  return null;
}

/// 根据类型名重建对象
function reconstructObject(data: ESObject, typeName: string): ESObject | null {
  try {
    if (!data || typeof data !== 'object') {
      return null;
    }

    const dataObj = data as Record<string, ESObject>;

    switch (typeName) {
      case 'WebViewParam':
        if (dataObj.url &&
            typeof dataObj.url === 'string' &&
            dataObj.title !== undefined &&
            typeof dataObj.title === 'string' &&
            dataObj.isWebNavigation !== undefined &&
            typeof dataObj.isWebNavigation === 'number') {
          return new WebViewParam(
            dataObj.url,
            dataObj.title,
            dataObj.isWebNavigation
          );
        }
        break;
      // 可以添加其他类型的重建逻辑
      default:
        return data;
    }
  } catch (error) {
    console.error(`重建对象失败: ${error}`);
  }

  return null;
}