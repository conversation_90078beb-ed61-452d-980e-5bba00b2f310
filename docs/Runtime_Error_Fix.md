# 运行时错误修复

## 🚨 **错误分析**

### 错误信息
```
Error message: is not callable
Stacktrace: at anonymous entry (entry/src/main/ets/pages/webview/WebViewPage.ets:57:14)
```

### 错误原因
```typescript
// ❌ 问题代码
src: this.vm.param?.getParamUrl(),
```

**根本原因**: `this.vm.param` 可能为 `null` 或 `undefined`，或者不是 `WebViewParam` 类型的实例，导致 `getParamUrl()` 方法不存在。

## 🔧 **修复方案**

### 1. **添加安全检查**
```typescript
// ✅ 修复后的代码
src: this.getWebViewUrl(),

// 安全获取WebView URL
private getWebViewUrl(): string {
  try {
    if (this.vm.param && typeof this.vm.param.getParamUrl === 'function') {
      return this.vm.param.getParamUrl();
    } else if (this.vm.param && this.vm.param.url) {
      return this.vm.param.url;
    } else {
      log(`⚠️ WebView参数异常，使用默认URL`);
      return 'about:blank';
    }
  } catch (error) {
    log(`❌ 获取WebView URL失败: ${error}`);
    return 'about:blank';
  }
}
```

### 2. **多层防护机制**
1. **类型检查**: 检查 `this.vm.param` 是否存在
2. **方法检查**: 检查 `getParamUrl` 方法是否存在
3. **降级处理**: 如果方法不存在，使用 `url` 属性
4. **异常处理**: 使用 try-catch 捕获任何异常
5. **默认值**: 提供安全的默认URL

## 🤔 **为什么会出现这个问题？**

### 可能的原因

#### 1. **路由参数传递问题**
```typescript
// 调用时可能没有正确传递参数
routeTo(context, 'pages/webview/WebViewPage', null); // ❌ 传递了null
```

#### 2. **参数类型不匹配**
```typescript
// 传递的参数可能不是WebViewParam实例
routeTo(context, 'pages/webview/WebViewPage', { url: 'xxx' }); // ❌ 普通对象
```

#### 3. **实例化问题**
```typescript
// 正确的调用方式
routeTo(context, 'pages/webview/WebViewPage', new WebViewParam(url, title, 0)); // ✅
```

## 🔍 **调试信息**

修复后会输出详细的调试信息：
```
🔧 WebView 参数: [JSON格式的参数]
🌐 WebView 即将加载URL: [实际URL]
```

如果出现问题，会看到：
```
⚠️ WebView参数异常，使用默认URL
❌ 获取WebView URL失败: [错误信息]
```

## 🎯 **验证修复**

### 1. **正常情况**
```
🔧 WebView 参数: {"url":"http://example.com","title":"测试","isWebNavigation":1}
🌐 WebView 即将加载URL: http://example.com?platform=1
```

### 2. **异常情况**
```
🔧 WebView 参数: null
⚠️ WebView参数异常，使用默认URL
🌐 WebView 即将加载URL: about:blank
```

## 🚀 **最佳实践**

### 1. **调用WebView页面的正确方式**
```typescript
// ✅ 正确方式
const param = new WebViewParam(url, title, isWebNavigation);
routeTo(context, 'pages/webview/WebViewPage', param);
```

### 2. **避免的错误方式**
```typescript
// ❌ 错误方式
routeTo(context, 'pages/webview/WebViewPage', null);
routeTo(context, 'pages/webview/WebViewPage', { url: url });
routeTo(context, 'pages/webview/WebViewPage', undefined);
```

### 3. **防御性编程**
- 总是检查对象是否存在
- 总是检查方法是否存在
- 提供合理的默认值
- 使用try-catch处理异常

## 📋 **检查清单**

修复后请确认：
- [ ] 应用能正常启动
- [ ] WebView页面能正常加载
- [ ] 没有运行时错误
- [ ] 日志输出正常
- [ ] URL获取正确

这个修复确保了即使在参数异常的情况下，应用也不会崩溃，而是优雅地处理错误并提供默认行为。
