import { AAID } from "@kit.PushKit";
import util from '@ohos.util';
import window from '@ohos.window';
import { deviceInfo } from '@kit.BasicServicesKit';
import pasteboard from '@ohos.pasteboard';

export class DeviceUtils {

  /**
   *
   * 【说明】AAID（Anonymous Application Identifier）：应用匿名标识符，标识运行在移动智能终端设备上的应用实例
   * ，只有该应用实例才能访问该标识符，它只存在于应用的安装期，总长度36位。与无法重置的设备级硬件ID相比，
   *  AAID具有更好的隐私权属性。
   *
      AAID具有以下特性：
       匿名化、无隐私风险：AAID和已有的任何标识符都不关联，并且每个应用只能访问自己的AAID。
       同一个设备上，同一个开发者的多个应用，AAID取值不同。
       同一个设备上，不同开发者的应用，AAID取值不同。
       不同设备上，同一个开发者的应用，AAID取值不同。
       不同设备上，不同开发者的应用，AAID取值不同。
   *
   * AAID会在包括但不限于下述场景中发生变化：
   * 应用卸载重装。
     应用调用删除AAID接口。
     用户恢复出厂设置。
     用户清除应用数据。
   * @returns
   */
  static async getDeviceAAID() : Promise<string> {
    try {
      const aaid: string = await AAID.getAAID();
      return aaid;
    } catch (err) {
      return ""
    }
  }

  static getUUID() :string {
    const uuidWithHyphens = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    // 移除横线
    return uuidWithHyphens.replace(/-/g, '');
  }

  static getTimStamp() : number {
    let timestamp: number = new Date().getTime();
    console.info('当前时间戳（毫秒）：' + timestamp);
    return timestamp
  }

  static  getStatusBarHeight():  number {
    try {

      return 44;

    } catch (error) {
      return 44;
    }
  }

  /**
   * 设备信息
   * https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/js-apis-device-info-V5
   * @returns
   */
  static getDeviceInfo() : string {
    try {
      let brandInfo: string = deviceInfo.brand; // HUAWEI
      let marketNameInfo: string = deviceInfo.marketName; // 输出结果：the value of the marketName is :Mate XX
      let productModelInfo: string = deviceInfo.productModel; // 输出结果：the value of the productModel is :TAS-AL00
      let softwareModelInfo: string = deviceInfo.softwareModel;
      return `${brandInfo} ${marketNameInfo} ${softwareModelInfo}`
    }catch (e){
      return ''
    }
  }

  // 复制到剪贴板
  static copy2PasteBoard(txt: string) {
    try {
      const systemPasteboard = pasteboard.getSystemPasteboard();
      let p = pasteboard.createData(pasteboard.MIMETYPE_TEXT_PLAIN , txt)
      systemPasteboard.setData(p)
    }catch (e) {
    }
  }


}
