syntax = "proto3";

//optimize_for 加快解析的速度
//option optimize_for = SPEED;
option optimize_for = CODE_SIZE;
package com.joinu.im.protobuf;
option java_outer_classname = "MsgBean";

import "google/protobuf/any.proto";

message ImMsg {
  string msgId = 1;
  string cmdId = 2;
  int32 type = 16;
  UserInfo senderInfo = 17;
  int64 msgTime = 18;
  bool recordIgnore = 21;
  string msgContent = 22;
  bool selfMsg = 25;
  // 自定义消息
  string customMessage = 26;
  oneof content {
    Exception exception = 3;
    LoginRequest loginRequest = 4;
    LoginResult loginResult = 5;
    C2CMsg c2cMsg = 6;
    C2CMsgRequest c2cMsgRequest = 7;
    C2CMsgResponse c2cMsgResponse = 8;
    GroupMsg groupMsg = 9;
    GroupMsgRequest groupMsgRequest = 10;
    GroupMsgResponse groupMsgResponse = 11;
    PushMsg pushMsg = 12;
    ServerMsg serverMsg = 13;
    NoticeMsg noticeMsg = 14;
    ExtMsg ext = 15;
    MultipleMsg offline = 19;
    OfflineMsgRequest offlineMsgRequest = 20;
    KickMsg kick = 23;
    ImPushMsg imPushMsg = 24;
  }
}

message ImPushMsg {
  oneof content {
    string blacklistAdd = 1;
    string blacklistRemove = 2;
    string singleMuteListAdd = 3;
    string singleMuteListRemove = 4;
    string groupMuteListAdd = 5;
    string groupMuteListRemove = 6;
    WarnMessage warnMessage = 7;
  }
}

message WarnMessage {
  string msg = 1;
  string sessionId = 2;
  enum SessionType {
    NULL = 0;
    C2C = 1;
    GROUP = 2;
  }
  SessionType sessionType = 3;
}

message KickMsg {
  enum KickType {
    NULL = 0;
    OtherDeviceLogin = 1;
  }
  KickType kickType = 1;
}

message OfflineMsgRequest{
  int64 offlineTime = 1; // 下线时间，可选
  repeated string targetUserId = 2;
  repeated string groupId = 3;
}

message MultipleMsg{
  repeated ImMsg msg = 1;
}

// 通知消息
message NoticeMsg{
  string title = 1;
  string subtitle = 2;
  string context = 3;
  string data = 4;
  string ext = 5;
  int32 tempStatus = 6;
}

message ServerMsg{
  ImMsg noticeMsgUpdated = 1; // 系统服务通知更新，内容为更新后通知消息的内容
}

message Exception{
  string msg = 1;
  int32 requestName = 2; // 发生异常的方法名
}

message LoginRequest{
  string token = 1;
  enum ClientTypeEnum{
    NULL = 0;
    IOS = 1;
    ANDROID = 2;
    WEB = 3;
    PC = 4;
    OTHER = 5;
    MOBILE = 6;
  }
  ClientTypeEnum clientType = 2;
}

message LoginResult{
  bool success = 1;
  string imUserId = 2;
}

message ImgMsg{
  bytes thumbnail = 1;
  oneof image{
    string url = 2;
    string imageId = 3;
  }
  string thumbnailUrl = 4;
  float width = 5;
  float height = 6;
  string attach = 7;
}

// 语音消息
message Voice{
  oneof voice{
    string url = 1;
    string voiceId = 3;
  }
  uint32 duration = 2;
  string attach = 4;
}

message Location {
  string title = 1;
  string address = 2;
  double latitude = 3;
  double longitude = 4;
  string uri = 5;
  string attach = 6;
}

message VideoMsg {
  ImgMsg cover = 1; //封面
  uint64 fileSize = 4;
  uint32 duration = 5;
  string attach = 6;
  oneof file {
    string url = 2;
    string fileId = 3;
  }
}

message FileMsg{
  string name = 1;
  uint64 fileSize = 4;
  oneof file {
    string url = 2;
    string fileId = 3;
  }

  string attach = 5;
}

message UserInfo{
  string nickname = 1;
  string userId = 2;
  string avatar = 3;
  string imUserId = 4;
}

message AudioAndVideoCall{
  enum DurationType{
    NULL = 0;
    VOICE = 1;
    VIDEO = 2;
  }

  DurationType durationType = 1;
  string content = 2;
  string metingId = 3;
  string attach = 4;
}

/**
 * 撤回消息
 */
message Withdraw{
  string msgId = 1;
}

message ReadMsg{
  repeated string msgId = 1;
}


/* 接收用户到用户的单聊消息 */
message C2CMsg{
  string from = 1;
  uint32 conversationId = 4;
  string receiver = 16;
  oneof Context{
    string msg = 6;
    ImgMsg image = 7;
    Voice voice = 8;
    Location location = 9;
    VideoMsg video = 10;
    FileMsg file = 11;
    Withdraw withdraw = 12; // 撤回
    ReadMsg read = 13; // 已读
    ExtMsg ext = 14;
    AudioAndVideoCall call = 15;
  }
}

message C2CMsgRequest {
  string receiver = 1;
  oneof Context{
    string msg = 3;
    ImgMsg image = 4;
    Voice voice = 5;
    Location location = 6;
    Withdraw withdraw = 7; // 撤回
    ReadMsg read = 8; // 已读
    ExtMsg ext = 9;
    VideoMsg video = 10;
    FileMsg file = 11;
    AudioAndVideoCall call = 12;
  }
}


/**
 * 消息发送失败的原因列表
 */
enum FailType {
  UNKNOWN = 0;
  IN_BLACKLIST = 1; // 在对象的黑名单中
  TARGET_NOT_LOGIN = 2; // 对象未登录
  MSG_SAVE_FAILURE = 3; // 消息存储失败
  TARGET_IS_EMPTY = 4; // 目标为空
  GROUP_NOT_EXIST = 5; // 群组不存在
  GROUP_MUTED = 6; // 群组被禁言
  GROUP_USER_MUTED = 7; // 当前用户被群组禁言
  USER_NOT_IN_GROUP = 8; // 当前用户不在群组中
}

message C2CMsgResponse{
  string receiver = 1;
  bool success = 2;
  FailType failType = 4;
  string failMessage = 5;
}

message PushMsg{
  string msg = 1;
  int32 type = 2;
}

message GroupMsg{
  string appId = 14;
  string groupId = 5;
  string groupName = 12;
  string groupLogo = 13;
  oneof Context{
    string msg = 1;
    ImgMsg image = 2;
    Voice voice = 3;
    Location location = 4;
    ExtMsg ext = 6;
    Withdraw withdraw = 7; // 撤回
    VideoMsg video = 8;
    FileMsg file = 9;
    AudioAndVideoCall call = 10;
    NoticeMsg noticeMsg = 11;
  }
}

message ExtMsg {
  google.protobuf.Any ext = 1;
  string ext1 = 2;
  string ext2 = 3;
  string ext3 = 4;
}

message GroupMsgRequest{
  string groupId = 5;
  string groupName = 11;
  string groupLogo = 12;
  oneof Context{
    string msg = 1;
    ImgMsg image = 2;
    Voice voice = 3;
    Location location = 4;
    ExtMsg ext = 6;
    Withdraw withdraw = 7; // 撤回
    VideoMsg video = 8;
    FileMsg file = 9;
    AudioAndVideoCall call = 10;
  }
}

message GroupMsgResponse{
  string groupId = 1;
  bool success = 2;
  FailType failType = 4;
  string failMessage = 5;
}