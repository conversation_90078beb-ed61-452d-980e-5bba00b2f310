# WebView JavaScript 调用调试指南

## 问题现象

`client_isAlready` 方法没有被调用，相关日志没有打印。

## 调试步骤

### 1. 检查日志输出

运行应用后，在DevEco Studio的日志窗口中查找以下关键日志：

#### WebView初始化日志
```
WebView 即将加载URL: [你的URL]
🚀 JsProxy 开始初始化...
📱 控制器状态: 已提供
🎯 JavaScript对象名: DDBESOFFICE
📋 JsProxy 可用方法列表:
  1. client_isAlready: ✅ 存在
  2. client_getToken: ✅ 存在
  ...
✅ JsProxy 构造完成
```

#### 页面加载日志
```
WebView页面开始加载: [你的URL]
WebView页面加载完成: [你的URL]
开始检查JavaScript对象注入状态...
JavaScript对象检查结果: SUCCESS 或 NOT_FOUND
```

#### 方法调用日志
```
🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉
📨 接收到的消息: [消息内容]
⏰ 调用时间: [时间戳]
```

### 2. 使用测试页面

#### 加载测试页面
```typescript
// 在WebView中加载测试页面
const testUrl = 'file:///android_asset/test_webview.html';
// 或者使用本地文件路径
const testUrl = 'resource://rawfile/test_webview.html';
```

#### 观察测试页面日志
测试页面会显示详细的调试信息：
- 原生对象检查结果
- 方法存在性验证
- 自动调用测试

### 3. 常见问题排查

#### 问题1: JavaScript对象未注入
**现象**: 日志显示 "❌ DDBESOFFICE 对象不存在"

**可能原因**:
- JavaScriptProxy配置错误
- 页面加载时机问题
- 权限配置问题

**解决方案**:
```typescript
// 确保JavaScriptProxy配置正确
private proxy: JavaScriptProxy = {
  object: this.jsProxy,           // 使用实例而不是类
  name: BASE_WIN_OBJ,            // 'DDBESOFFICE'
  methodList: JSMethodArray,      // 方法列表
  controller: this.controller     // WebView控制器
};

// 确保在Web组件中正确设置
Web({ src: this.vm.param?.url, controller: this.controller })
  .javaScriptAccess(true)         // 启用JavaScript
  .javaScriptProxy(this.proxy)    // 设置代理
```

#### 问题2: 方法存在但未被调用
**现象**: 对象存在，但方法调用无响应

**可能原因**:
- 方法名不匹配
- 参数格式错误
- 调用时机过早

**解决方案**:
```javascript
// 确保方法名正确
window.DDBESOFFICE.client_isAlready('{"title":"test"}');

// 确保在对象准备就绪后调用
window.addEventListener('nativeReady', function() {
  // 在这里调用原生方法
  window.DDBESOFFICE.client_isAlready('{"title":"test"}');
});
```

#### 问题3: 页面加载时机问题
**现象**: 页面加载完成但对象仍未注入

**解决方案**:
```typescript
// 增加延迟时间
.onPageEnd((e) => {
  setTimeout(() => {
    this.checkAndInjectJavaScript();
  }, 1000); // 延迟1秒
})
```

### 4. 手动测试方法

#### 在浏览器控制台测试
```javascript
// 检查对象是否存在
console.log('DDBESOFFICE对象:', typeof window.DDBESOFFICE);

// 检查方法是否存在
console.log('client_isAlready方法:', typeof window.DDBESOFFICE?.client_isAlready);

// 手动调用方法
if (window.DDBESOFFICE && window.DDBESOFFICE.client_isAlready) {
  window.DDBESOFFICE.client_isAlready('{"title":"手动测试","useTitleBar":true}');
}
```

#### 在HarmonyOS中手动注入
```typescript
// 在onPageEnd中手动执行JavaScript
.onPageEnd((e) => {
  const testScript = `
    if (window.DDBESOFFICE && window.DDBESOFFICE.client_isAlready) {
      console.log('手动调用 client_isAlready');
      window.DDBESOFFICE.client_isAlready('{"title":"手动测试"}');
    } else {
      console.log('DDBESOFFICE对象或方法不存在');
    }
  `;
  
  this.controller.runJavaScript(testScript);
})
```

### 5. 权限检查

确保在 `module.json5` 中配置了必要的权限：
```json5
{
  "name": "ohos.permission.INTERNET"
}
```

### 6. 网络和文件访问

确保WebView配置允许必要的访问：
```typescript
Web({ src: url, controller: this.controller })
  .javaScriptAccess(true)      // JavaScript访问
  .domStorageAccess(true)      // DOM存储访问
  .fileAccess(true)            // 文件访问
  .imageAccess(true)           // 图片访问
  .onlineImageAccess(true)     // 在线图片访问
```

### 7. 调试技巧

#### 启用详细日志
```typescript
// 在JsProxy构造函数中添加
log(`🔍 详细调试信息:`);
log(`  - 控制器: ${this.controller}`);
log(`  - 对象名: ${this.currentObj}`);
log(`  - 方法数量: ${JSMethodArray.length}`);
```

#### 使用断点调试
在 `client_isAlready` 方法的第一行设置断点，确认方法是否被调用。

#### 检查WebView状态
```typescript
.onPageEnd((e) => {
  log(`页面加载完成: ${e?.url}`);
  log(`WebView状态: ${this.controller ? '正常' : '异常'}`);
})
```

## 预期结果

修复后，您应该能看到以下日志序列：

1. WebView和JsProxy初始化日志
2. 页面加载开始和完成日志
3. JavaScript对象检查成功日志
4. `client_isAlready` 方法被调用日志
5. 参数解析和响应发送日志

如果仍然没有看到方法调用日志，请按照上述步骤逐一排查，并提供具体的日志输出以便进一步诊断。
