import { router } from '@kit.ArkUI';

/**
 * 工具栏右侧按钮配置
 */
export interface ToolbarAction {
  text?: string;           // 按钮文字
  icon?: string | Resource; // 按钮图标
  onClick: () => void;     // 点击回调
  enabled?: boolean;       // 是否启用，默认true
  visible?: boolean;       // 是否可见，默认true
}

/**
 * 工具栏配置参数
 */
export interface ToolbarConfig {
  title?: string;                    // 标题文字，默认为空
  showBackButton?: boolean;          // 是否显示返回按钮，默认true
  backButtonIcon?: string | Resource; // 返回按钮图标
  onBackClick?: () => void;          // 返回按钮点击回调，默认关闭当前页
  backgroundColor?: string | Resource; // 背景色，默认白色
  titleColor?: string | Resource;    // 标题颜色，默认黑色
  elevation?: boolean;               // 是否显示阴影，默认true
  actions?: ToolbarAction[];         // 右侧操作按钮数组
}

/**
 * 通用工具栏组件
 * 
 * 使用示例：
 * CommonToolbar({
 *   title: "页面标题",
 *   actions: [
 *     { text: "保存", onClick: () => this.save() },
 *     { icon: $r('app.media.ic_more'), onClick: () => this.showMore() }
 *   ]
 * })
 */
@ComponentV2
export struct CommonToolbar {
  // 工具栏配置
  @Param config: ToolbarConfig = {};

  // 默认配置
  private getTitle(): string {
    return this.config.title || '';
  }

  private getShowBackButton(): boolean {
    return this.config.showBackButton !== false; // 默认显示
  }

  private getBackButtonIcon(): string | Resource {
    return this.config.backButtonIcon || $r('app.media.common_back_arrow'); // 默认返回图标
  }

  private getBackgroundColor(): string | Resource {
    return this.config.backgroundColor || '#FFFFFF';
  }

  private getTitleColor(): string | Resource {
    return this.config.titleColor || '#333333';
  }

  private getElevation(): boolean {
    return this.config.elevation !== false; // 默认显示阴影
  }

  private getActions(): ToolbarAction[] {
    return this.config.actions || [];
  }

  // 默认返回方法
  private handleBackClick(): void {
    if (this.config.onBackClick) {
      this.config.onBackClick();
    } else {
      // 默认行为：关闭当前页面
      try {
        router.back();
      } catch (error) {
        console.error('CommonToolbar: 返回失败', error);
      }
    }
  }

  // 渲染右侧操作按钮
  @Builder
  private ActionButtonBuilder(action: ToolbarAction) {
    if (action.visible !== false) {
      Button() {
        if (action.icon) {
          // 图标按钮
          if (typeof action.icon === 'string') {
            Text(action.icon)
              .fontSize(16)
              .fontColor(action.enabled !== false ? '#333333' : '#CCCCCC')
          } else {
            Image(action.icon)
              .width(20)
              .height(20)
              .fillColor(action.enabled !== false ? '#333333' : '#CCCCCC')
          }
        } else if (action.text) {
          // 文字按钮
          Text(action.text)
            .fontSize(14)
            .fontColor(action.enabled !== false ? '#007AFF' : '#CCCCCC')
        }
      }
      .type(ButtonType.Normal)
      .backgroundColor('#00000000')
      .enabled(action.enabled !== false)
      .padding({ left: 8, right: 8, top: 4, bottom: 4 })
      .onClick(() => {
        if (action.enabled !== false) {
          action.onClick();
        }
      })
    }
  }

  build() {
    Row() {
      // 左侧返回按钮
      if (this.getShowBackButton()) {
        Button() {
          if (typeof this.getBackButtonIcon() === 'string') {
            Text(this.getBackButtonIcon() as string)
              .fontSize(18)
              .fontColor('#333333')
          } else {
            Image(this.getBackButtonIcon())
              .width(24)
              .height(24)
              .fillColor('#333333')
          }
        }
        .type(ButtonType.Normal)
        .backgroundColor(Color.Transparent)
        .padding({ left: 4, right: 4, top: 4, bottom: 4 })
        .onClick(() => this.handleBackClick())
      }

      // 中间标题区域
      if (this.getTitle()) {
        Text(this.getTitle())
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor(this.getTitleColor())
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .layoutWeight(1)
          .textAlign(this.getShowBackButton() ? TextAlign.Center : TextAlign.Start)
          .margin({ 
            left: this.getShowBackButton() ? 0 : 16,
            right: this.getActions().length > 0 ? 0 : 16
          })
      } else {
        // 占位空间
        Blank()
          .layoutWeight(1)
      }

      // 右侧操作按钮
      if (this.getActions().length > 0) {
        Row() {
          ForEach(this.getActions(), (action: ToolbarAction, index: number) => {
            this.ActionButtonBuilder(action)
          })
        }
        .margin({ right: 8 })
      }
    }
    .width('100%')
    .height(56) // 标准工具栏高度
    .backgroundColor(this.getBackgroundColor())
    .padding({ left: 16, right: 16 })
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
    .shadow(this.getElevation() ? {
      radius: 4,
      color: '#1A000000',
      offsetX: 0,
      offsetY: 2
    } : undefined)
  }
}

/**
 * 工具栏构建器函数 - 便捷使用方式
 */
@Builder
export function ToolbarBuilder(config: ToolbarConfig) {
  CommonToolbar({ config: config })
}

/**
 * 预设工具栏样式
 */
export class ToolbarPresets {
  /**
   * 简单标题工具栏
   */
  static simple(title: string, onBack?: () => void): ToolbarConfig {
    return {
      title: title,
      onBackClick: onBack
    };
  }

  /**
   * 带保存按钮的工具栏
   */
  static withSave(title: string, onSave: () => void, onBack?: () => void): ToolbarConfig {
    return {
      title: title,
      onBackClick: onBack,
      actions: [
        {
          text: '保存',
          onClick: onSave
        }
      ]
    };
  }

  /**
   * 带更多菜单的工具栏
   */
  static withMore(title: string, onMore: () => void, onBack?: () => void): ToolbarConfig {
    return {
      title: title,
      onBackClick: onBack,
      actions: [
        {
          text: '⋯',
          onClick: onMore
        }
      ]
    };
  }

  /**
   * 编辑页面工具栏
   */
  static edit(title: string, onSave: () => void, onCancel?: () => void): ToolbarConfig {
    return {
      title: title,
      onBackClick: onCancel,
      actions: [
        {
          text: '保存',
          onClick: onSave
        }
      ]
    };
  }

  /**
   * 搜索页面工具栏
   */
  static search(title: string, onSearch: () => void, onBack?: () => void): ToolbarConfig {
    return {
      title: title,
      onBackClick: onBack,
      actions: [
        {
          text: '🔍',
          onClick: onSearch
        }
      ]
    };
  }

  /**
   * 无返回按钮的工具栏
   */
  static noBack(title: string, actions?: ToolbarAction[]): ToolbarConfig {
    return {
      title: title,
      showBackButton: false,
      actions: actions
    };
  }

  /**
   * 透明工具栏
   */
  static transparent(title: string, onBack?: () => void): ToolbarConfig {
    return {
      title: title,
      onBackClick: onBack,
      backgroundColor: '#00000000',
      elevation: false
    };
  }
}
