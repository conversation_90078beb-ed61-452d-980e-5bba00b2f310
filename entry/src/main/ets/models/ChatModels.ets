/**
 * 聊天消息类型
 */
export enum MessageType {
  TEXT = 'text',           // 文本消息
  IMAGE = 'image',         // 图片消息
  VOICE = 'voice',         // 语音消息
  VIDEO = 'video',         // 视频消息
  FILE = 'file',           // 文件消息
  SYSTEM = 'system'        // 系统消息
}

/**
 * 消息发送状态
 */
export enum MessageStatus {
  SENDING = 'sending',     // 发送中
  SENT = 'sent',           // 已发送
  DELIVERED = 'delivered', // 已送达
  READ = 'read',           // 已读
  FAILED = 'failed'        // 发送失败
}

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  id: string;              // 消息ID
  sessionId: string;       // 会话ID
  senderId: string;        // 发送者ID
  senderName: string;      // 发送者名称
  senderAvatar: string;    // 发送者头像
  type: MessageType;       // 消息类型
  content: string;         // 消息内容
  timestamp: number;       // 发送时间戳
  status: MessageStatus;   // 消息状态
  isFromMe: boolean;       // 是否是我发送的
  replyTo?: string;        // 回复的消息ID
  extra?: any;             // 扩展数据
}

/**
 * 聊天消息实现类
 */
export class ChatMessageData implements ChatMessage {
  id: string = '';
  sessionId: string = '';
  senderId: string = '';
  senderName: string = '';
  senderAvatar: string = '';
  type: MessageType = MessageType.TEXT;
  content: string = '';
  timestamp: number = 0;
  status: MessageStatus = MessageStatus.SENDING;
  isFromMe: boolean = false;
  replyTo?: string;
  extra?: any;

  constructor(data?: Partial<ChatMessage>) {
    if (data) {
      Object.assign(this, data);
    }
    
    // 如果没有设置ID，自动生成
    if (!this.id) {
      this.id = this.generateMessageId();
    }
    
    // 如果没有设置时间戳，使用当前时间
    if (!this.timestamp) {
      this.timestamp = Date.now();
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取格式化的时间
   */
  getFormattedTime(): string {
    const date = new Date(this.timestamp);
    const now = new Date();
    
    // 判断是否是今天
    if (date.toDateString() === now.toDateString()) {
      // 今天，显示时:分
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } else {
      // 不是今天，显示月-日 时:分
      return date.toLocaleString('zh-CN', { 
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    }
  }

  /**
   * 是否显示时间
   * 与上一条消息间隔超过5分钟则显示时间
   */
  shouldShowTime(previousMessage?: ChatMessageData): boolean {
    if (!previousMessage) {
      return true;
    }
    
    const timeDiff = this.timestamp - previousMessage.timestamp;
    return timeDiff > 5 * 60 * 1000; // 5分钟
  }

  /**
   * 是否显示头像
   * 连续的同一发送者消息不显示头像
   */
  shouldShowAvatar(nextMessage?: ChatMessageData): boolean {
    if (!nextMessage) {
      return true;
    }
    
    return nextMessage.senderId !== this.senderId;
  }

  /**
   * 获取消息状态图标
   */
  getStatusIcon(): string {
    switch (this.status) {
      case MessageStatus.SENDING:
        return '⏳';
      case MessageStatus.SENT:
        return '✓';
      case MessageStatus.DELIVERED:
        return '✓✓';
      case MessageStatus.READ:
        return '✓✓';
      case MessageStatus.FAILED:
        return '❌';
      default:
        return '';
    }
  }

  /**
   * 获取消息状态颜色
   */
  getStatusColor(): string {
    switch (this.status) {
      case MessageStatus.SENDING:
        return '#999999';
      case MessageStatus.SENT:
        return '#999999';
      case MessageStatus.DELIVERED:
        return '#999999';
      case MessageStatus.READ:
        return '#007AFF';
      case MessageStatus.FAILED:
        return '#FF3B30';
      default:
        return '#999999';
    }
  }

  /**
   * 是否是文本消息
   */
  isTextMessage(): boolean {
    return this.type === MessageType.TEXT;
  }

  /**
   * 是否是图片消息
   */
  isImageMessage(): boolean {
    return this.type === MessageType.IMAGE;
  }

  /**
   * 是否是系统消息
   */
  isSystemMessage(): boolean {
    return this.type === MessageType.SYSTEM;
  }

  /**
   * 获取消息预览文本
   */
  getPreviewText(): string {
    switch (this.type) {
      case MessageType.TEXT:
        return this.content;
      case MessageType.IMAGE:
        return '[图片]';
      case MessageType.VOICE:
        return '[语音]';
      case MessageType.VIDEO:
        return '[视频]';
      case MessageType.FILE:
        return '[文件]';
      case MessageStatus.SYSTEM:
        return this.content;
      default:
        return '[未知消息]';
    }
  }

  /**
   * 复制消息
   */
  copy(): ChatMessageData {
    return new ChatMessageData({
      id: this.id,
      sessionId: this.sessionId,
      senderId: this.senderId,
      senderName: this.senderName,
      senderAvatar: this.senderAvatar,
      type: this.type,
      content: this.content,
      timestamp: this.timestamp,
      status: this.status,
      isFromMe: this.isFromMe,
      replyTo: this.replyTo,
      extra: this.extra
    });
  }

  /**
   * 更新消息状态
   */
  updateStatus(status: MessageStatus): ChatMessageData {
    const newMessage = this.copy();
    newMessage.status = status;
    return newMessage;
  }
}

/**
 * 聊天输入状态
 */
export interface ChatInputState {
  text: string;            // 输入的文本
  isComposing: boolean;    // 是否正在输入
  showEmojiPanel: boolean; // 是否显示表情面板
  showMorePanel: boolean;  // 是否显示更多面板
}

/**
 * 聊天输入状态实现类
 */
export class ChatInputStateData implements ChatInputState {
  text: string = '';
  isComposing: boolean = false;
  showEmojiPanel: boolean = false;
  showMorePanel: boolean = false;

  constructor(data?: Partial<ChatInputState>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  /**
   * 是否可以发送消息
   */
  canSend(): boolean {
    return this.text.trim().length > 0;
  }

  /**
   * 清空输入
   */
  clear(): ChatInputStateData {
    return new ChatInputStateData({
      text: '',
      isComposing: false,
      showEmojiPanel: this.showEmojiPanel,
      showMorePanel: this.showMorePanel
    });
  }

  /**
   * 更新文本
   */
  updateText(text: string): ChatInputStateData {
    return new ChatInputStateData({
      text: text,
      isComposing: this.isComposing,
      showEmojiPanel: this.showEmojiPanel,
      showMorePanel: this.showMorePanel
    });
  }

  /**
   * 切换表情面板
   */
  toggleEmojiPanel(): ChatInputStateData {
    return new ChatInputStateData({
      text: this.text,
      isComposing: this.isComposing,
      showEmojiPanel: !this.showEmojiPanel,
      showMorePanel: false
    });
  }

  /**
   * 切换更多面板
   */
  toggleMorePanel(): ChatInputStateData {
    return new ChatInputStateData({
      text: this.text,
      isComposing: this.isComposing,
      showEmojiPanel: false,
      showMorePanel: !this.showMorePanel
    });
  }
}
