import { PromptAction, promptAction } from '@kit.ArkUI';
import { collections } from '@kit.ArkTS';
import { print } from '@kit.BasicServicesKit';

import { Router, UIContext } from '@ohos.arkui.UIContext'
import { log } from '../util/Log';
import { Country } from '../types/Country';


function getAppRouter(context: UIContext): Router {
  return context.getRouter();
}


@Entry
@Component
struct Index {

  @State mCountries: Country[] = []

  @State countries: Country[] = [
    { name: '中国', capital: '北京' },
    { name: '美国', capital: '华盛顿' },
    { name: '日本', capital: '东京' },
    { name: '英国', capital: '伦敦' },
    { name: '法国', capital: '巴黎' },
    { name: '德国', capital: '柏林' },
    { name: '俄罗斯', capital: '莫斯科' },
    { name: '加拿大', capital: '渥太华' },
    { name: '澳大利亚', capital: '堪培拉' },
    { name: '巴西', capital: '巴西利亚' },
    { name: '印度', capital: '新德里' },
    { name: '韩国', capital: '首尔' },
    { name: '意大利', capital: '罗马' },
    { name: '西班牙', capital: '马德里' },
    { name: '南非', capital: '比勒陀利亚' },
    { name: '墨西哥', capital: '墨西哥城' },
    { name: '土耳其', capital: '安卡拉' },
    { name: '新加坡', capital: '新加坡' },
    { name: '阿根廷', capital: '布宜诺斯艾利斯' },
    { name: '埃及', capital: '开罗' },
    { name: '泰国', capital: '曼谷' },
    { name: '瑞典', capital: '斯德哥尔摩' },
    { name: '瑞士', capital: '伯尔尼' },
    { name: '荷兰', capital: '阿姆斯特丹' },
    { name: '比利时', capital: '布鲁塞尔' },
    { name: '奥地利', capital: '维也纳' },
    { name: '希腊', capital: '雅典' },
    { name: '芬兰', capital: '赫尔辛基' },
    { name: '丹麦', capital: '哥本哈根' },
    { name: '挪威', capital: '奥斯陆' }
  ];

  // 新增数据源
  private moreCountries: Country[] = [
    { name: '葡萄牙', capital: '里斯本' },
    { name: '爱尔兰', capital: '都柏林' },
    { name: '冰岛', capital: '雷克雅未克' },
    { name: '卢森堡', capital: '卢森堡' },
    { name: '马耳他', capital: '瓦莱塔' },
    { name: '塞浦路斯', capital: '尼科西亚' },

  ];

  @State isLoading: boolean = false;
  @State loadCount: number = 0;
  @State hasMoreData: boolean = true;
  @State isRefreshing: boolean = false;

  // 页面生命周期 - 页面每次显示时调用
  onPageShow() {
    log('index: onPageShow.......')

    // 获取从其他页面返回时传递的参数
    const params = this.getUIContext().getRouter().getParams() as Record<string, string | Country>;
    if (params && params.returnData) {
      const returnData = params.returnData as string;
      const country = params.country as Country;

      if (returnData.trim() !== '') {
        promptAction.openToast({
          message: `收到来自${country?.name || '详情页'}的备注：${returnData}`,
          duration: 3000
        });
      }

      // 清除参数，避免重复显示
      this.getUIContext().getRouter().clear();
    }
  }

  // 下拉刷新
  onRefresh() {
    this.isRefreshing = true;
    // 模拟网络请求延迟
    setTimeout(() => {
      this.countries = [
        { name: '中国', capital: '北京' },
        { name: '美国', capital: '华盛顿' },
        { name: '日本', capital: '东京' },
        { name: '英国', capital: '伦敦' },
        { name: '法国', capital: '巴黎' },
        { name: '德国', capital: '柏林' },
        { name: '俄罗斯', capital: '莫斯科' },
        { name: '加拿大', capital: '渥太华' },
        { name: '澳大利亚', capital: '堪培拉' },
        { name: '巴西', capital: '巴西利亚' },
        { name: '印度', capital: '新德里' },
        { name: '韩国', capital: '首尔' },
        { name: '意大利', capital: '罗马' },
        { name: '西班牙', capital: '马德里' },
        { name: '南非', capital: '比勒陀利亚' },
        { name: '墨西哥', capital: '墨西哥城' },
        { name: '土耳其', capital: '安卡拉' },
        { name: '新加坡', capital: '新加坡' },
        { name: '阿根廷', capital: '布宜诺斯艾利斯' },
        { name: '埃及', capital: '开罗' },
        { name: '泰国', capital: '曼谷' },
        { name: '瑞典', capital: '斯德哥尔摩' },
        { name: '瑞士', capital: '伯尔尼' },
        { name: '荷兰', capital: '阿姆斯特丹' },
        { name: '比利时', capital: '布鲁塞尔' },
        { name: '奥地利', capital: '维也纳' },
        { name: '希腊', capital: '雅典' },
        { name: '芬兰', capital: '赫尔辛基' },
        { name: '丹麦', capital: '哥本哈根' },
        { name: '挪威', capital: '奥斯陆' }
      ];
      this.loadCount = 0;
      this.hasMoreData = true;
      this.isRefreshing = false;
      promptAction.openToast({ message: '数据已刷新', duration: 2000 });
    }, 1000);
  }

  // 上拉加载更多
  onLoadMore() {
    if (!this.hasMoreData || this.isLoading) {
      return;
    }

    this.isLoading = true;
    // 模拟网络请求延迟
    setTimeout(() => {
      if (this.loadCount < 3) {
        // 每次加载6条数据
        const startIndex = this.loadCount * 6;
        const endIndex = Math.min(startIndex + 6, this.moreCountries.length);
        const newData = this.moreCountries.slice(startIndex, endIndex);

        this.countries = [...this.countries, ...newData];
        this.loadCount++;

        if (this.loadCount >= 3) {
          this.hasMoreData = false;
          promptAction.openToast({ message: '没有更多数据了', duration: 2000 });
        }
      }
      this.isLoading = false;
    }, 1000);
  }

  // 自定义刷新指示器
  @Builder
  refreshBuilder() {
    Stack() {
      Row() {
        LoadingProgress()
          .width(32)
          .height(32)
        Text('下拉刷新')
          .fontSize(16)
          .margin({ left: 12 })
      }
      .justifyContent(FlexAlign.Center)
    }
    .align(Alignment.Center)
    .width('100%')
    .height(64)
  }

  build() {
    Column() {
      Text('国家与首都列表')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 24, bottom: 16 })
        .align(Alignment.Center)

      Button("点我")
        .onClick((event?: ClickEvent) => {
          promptAction.openToast({ message: "点了！", duration: 3000 })
        })

      Refresh({ refreshing: $$this.isRefreshing, builder: this.refreshBuilder }) {
        List({ space: 8 }) {
          ForEach(this.countries, (country: Country, index) => {
            ListItem() {
              Row() {
                Text(country.name)
                  .fontSize(18)
                  .width('40%')
                Text(country.capital)
                  .fontSize(16)
                  .width('60%')
                  .textAlign(TextAlign.End)
              }
              .padding({ left: 16, right: 16, top: 8, bottom: 8 })
              .onClick((event?: ClickEvent) => {
                this.getUIContext().getRouter().pushUrl({
                  url: 'pages/Page',
                  params: {
                    country: country
                  }
                });

              })
            }
          }, (country: Country) => country.name)

          // 加载更多提示
          if (this.isLoading) {
            ListItem() {
              Row() {
                LoadingProgress()
                  .width(20)
                  .height(20)
                Text('加载中...')
                  .fontSize(14)
                  .margin({ left: 8 })
              }
              .width('100%')
              .justifyContent(FlexAlign.Center)
              .padding({ top: 16, bottom: 16 })
            }
          }

          if (!this.hasMoreData && this.loadCount >= 3) {
            ListItem() {
              Text('没有更多数据了')
                .fontSize(14)
                .fontColor('#999999')
                .width('100%')
                .textAlign(TextAlign.Center)
                .padding({ top: 16, bottom: 16 })
            }
          }
        }
        .width('100%')
        .height('100%')
        .onReachEnd(() => {
          this.onLoadMore();
        })
      }
      .onStateChange((refreshStatus: RefreshStatus) => {
        console.info('Refresh onStateChange state is ' + refreshStatus)
      })
      .onRefreshing(() => {
        this.onRefresh()
      })
      .width('100%')
    }
    .width('100%')
    .height('100%')
    .padding(16)
  }


  aboutToDisappear(): void {

  }

}

