# 图片浏览器使用指南

## 🎯 **功能概述**

已实现了一个全屏图片浏览器，类似Android的ViewPager，支持：
- 全屏显示图片
- 左右滑动切换
- 双击缩放
- 手势平移和缩放
- 图片计数显示
- 底部指示器

## 📱 **页面功能特性**

### 1. **手势操作**
- **左右滑动**: 切换图片
- **单击**: 显示/隐藏工具栏和指示器
- **双击**: 缩放图片（1x ↔ 2x）
- **双指缩放**: 自由缩放（0.5x - 3x）
- **拖拽**: 缩放后可平移图片

### 2. **界面元素**
- **顶部工具栏**: 返回按钮 + 图片计数
- **底部指示器**: 圆点显示当前位置
- **加载状态**: 加载动画和错误提示
- **全屏显示**: 黑色背景，沉浸式体验

### 3. **智能功能**
- **URL验证**: 自动过滤无效图片链接
- **索引调整**: 自动处理索引超出范围的情况
- **错误处理**: 完善的异常捕获和用户提示

## 🔧 **使用方式**

### 1. **JavaScript调用（推荐）**
```javascript
// 在Web页面中调用
const imageData = {
  "index": 1,
  "list": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg",
    "https://example.com/image3.jpg"
  ]
};

// 调用原生方法
window.DDBESOFFICE.client_previewPics(JSON.stringify(imageData));
```

### 2. **原生代码调用**
```typescript
import { ImageViewerUtil } from '../../util/ImageViewerUtil';

// 方式1: 直接调用
ImageViewerUtil.openImageViewer(1, [
  "https://example.com/image1.jpg",
  "https://example.com/image2.jpg",
  "https://example.com/image3.jpg"
]);

// 方式2: 从JSON调用
const jsonStr = '{"index":1,"list":["image1.jpg","image2.jpg"]}';
ImageViewerUtil.openImageViewerFromJson(jsonStr);

// 方式3: 从对象调用
ImageViewerUtil.openImageViewerFromObject({
  index: 0,
  list: ["image1.jpg", "image2.jpg"]
});

// 方式4: 预览单张图片
ImageViewerUtil.previewSingleImage("https://example.com/image.jpg");

// 方式5: 预览图片列表（从第一张开始）
ImageViewerUtil.previewImageList([
  "image1.jpg", "image2.jpg", "image3.jpg"
]);
```

## 📋 **参数格式**

### JSON参数格式
```json
{
  "index": 1,
  "list": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg", 
    "https://example.com/image3.jpg"
  ]
}
```

### 参数说明
- **index**: `number` - 当前显示的图片索引（从0开始）
- **list**: `string[]` - 图片链接数组

### 支持的图片格式
- **网络图片**: `http://` 或 `https://` 开头
- **本地文件**: `file://` 开头
- **资源文件**: `resource://` 开头

## 🎨 **界面展示**

### 1. **正常浏览状态**
```
┌─────────────────────────────────┐
│ ← 返回           2/5            │ ← 顶部工具栏
├─────────────────────────────────┤
│                                 │
│                                 │
│         [图片内容]              │ ← 全屏图片
│                                 │
│                                 │
├─────────────────────────────────┤
│        ● ○ ● ○ ○               │ ← 底部指示器
└─────────────────────────────────┘
```

### 2. **隐藏工具栏状态**
```
┌─────────────────────────────────┐
│                                 │
│                                 │
│                                 │
│         [图片内容]              │ ← 纯图片显示
│                                 │
│                                 │
│                                 │
└─────────────────────────────────┘
```

## 🧪 **测试示例**

### 1. **Web页面测试**
```html
<!DOCTYPE html>
<html>
<head>
    <title>图片浏览器测试</title>
</head>
<body>
    <h2>图片浏览器测试</h2>
    
    <button onclick="previewImages()">预览图片（从第2张开始）</button>
    <button onclick="previewSingleImage()">预览单张图片</button>
    
    <script>
        function previewImages() {
            const imageData = {
                "index": 1,
                "list": [
                    "https://picsum.photos/800/600?random=1",
                    "https://picsum.photos/800/600?random=2",
                    "https://picsum.photos/800/600?random=3",
                    "https://picsum.photos/800/600?random=4"
                ]
            };
            
            if (window.DDBESOFFICE && window.DDBESOFFICE.client_previewPics) {
                window.DDBESOFFICE.client_previewPics(JSON.stringify(imageData));
            } else {
                alert('原生方法不可用');
            }
        }
        
        function previewSingleImage() {
            const imageData = {
                "index": 0,
                "list": ["https://picsum.photos/800/600?random=5"]
            };
            
            if (window.DDBESOFFICE && window.DDBESOFFICE.client_previewPics) {
                window.DDBESOFFICE.client_previewPics(JSON.stringify(imageData));
            } else {
                alert('原生方法不可用');
            }
        }
    </script>
</body>
</html>
```

### 2. **原生代码测试**
```typescript
// 在任意页面中测试
Button('测试图片浏览器')
  .onClick(() => {
    const testImages = [
      'https://picsum.photos/800/600?random=1',
      'https://picsum.photos/800/600?random=2',
      'https://picsum.photos/800/600?random=3'
    ];
    
    ImageViewerUtil.openImageViewer(1, testImages);
  })
```

## 🔍 **调试信息**

### 日志输出示例
```
🔔🔔🔔 === client_previewPics 方法被调用 === 🔔🔔🔔
📨 接收到的JSON: {"index":1,"list":["image1.jpg","image2.jpg","image3.jpg"]}
📋 解析结果: index=1, list长度=3
📸 准备打开图片浏览器: 索引=1, 有效图片数=3
📸 图片列表: image1.jpg, image2.jpg, image3.jpg
图片浏览器初始化: 当前索引=1, 总数=3
图片切换到: 2/3
```

### 错误处理日志
```
❌ 图片列表为空
❌ 索引超出范围: 5, 列表长度: 3
❌ 没有有效的图片URL
⚠️ 索引调整: 3 -> 0
```

## ✅ **功能验证清单**

- [ ] **参数解析**: JSON格式正确解析
- [ ] **图片显示**: 图片正常加载和显示
- [ ] **滑动切换**: 左右滑动正常切换图片
- [ ] **手势操作**: 缩放、平移手势正常工作
- [ ] **界面交互**: 工具栏和指示器正常显示/隐藏
- [ ] **错误处理**: 无效参数和网络错误正常处理
- [ ] **返回功能**: 返回按钮正常工作

## 🚀 **扩展功能建议**

### 1. **可能的增强功能**
- 图片保存到本地
- 图片分享功能
- 图片旋转功能
- 缩略图预览
- 图片信息显示

### 2. **性能优化**
- 图片预加载
- 内存管理优化
- 大图片处理
- 缓存机制

## 📝 **总结**

图片浏览器功能已完整实现：

1. ✅ **全屏浏览** - 沉浸式图片查看体验
2. ✅ **手势操作** - 完整的触摸手势支持
3. ✅ **JavaScript接口** - Web页面可直接调用
4. ✅ **参数验证** - 智能的参数处理和验证
5. ✅ **错误处理** - 完善的异常处理机制
6. ✅ **调试支持** - 详细的日志输出

现在Web页面可以通过 `client_previewPics` 方法打开全屏图片浏览器了！
