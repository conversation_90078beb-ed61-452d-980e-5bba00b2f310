import router from '@ohos.router';
import web_webview from '@ohos.web.webview'
import { CommonToolbar, ToolbarPresets } from '../../components/CommonToolbar';
import { WebViewViewModel } from './WebViewViewModel';
import { WebViewParam } from '../../util/WebViewHelper';
import { BASE_WIN_OBJ, JSMethodArray, JsProxy } from './WebJS';
import { log } from '../../util/Log';

// 定义页面分析结果接口
interface PageAnalysisResult {
  hasClientIsAlready: boolean;
  hasDDBESOFFICE: boolean;
  hasDdbesWeb: boolean;
  nativeObjectExists: boolean;
  readyState: string;
  scriptCount: number;
}

// 定义深度分析结果接口
interface DeepAnalysisResult {
  scriptCount: number;
  isVueApp: boolean;
  isReactApp: boolean;
  isAngularApp: boolean;
  hasWebpack: boolean;
  currentRoute: string;
  foundPatterns: string[];
  globalVarsCount: number;
  htmlLength: number;
}


/// 通用 web 加载组件
@Entry
@Component
struct WebViewPage {

  private controller = new web_webview.WebviewController();
  private vm = new WebViewViewModel();
  private jsProxy: JsProxy = new JsProxy(this.controller);

  // 用于控制进度条的状态变量
  @State progress: number = 0;
  @State isProgressVisible: boolean = false;

  aboutToAppear() {
    const params = this.getUIContext().getRouter().getParams() as WebViewParam;
    this.vm.param = params;
    log(`🌐 WebView 即将加载URL: ${this.vm.param?.url}`);
    log(`🔧 WebView 参数: ${JSON.stringify(params)}`);

    // 初始化JavaScript代理
    this.initJavaScriptProxy();
  }

  // 初始化JavaScript代理
  private initJavaScriptProxy() {
    log(`🚀 初始化JavaScript代理`);
    log(`📋 对象名: ${BASE_WIN_OBJ}`);
    log(`📋 方法列表: ${JSMethodArray.join(', ')}`);
    log(`📋 JsProxy实例: ${this.jsProxy ? '已创建' : '未创建'}`);
    log(`📋 Controller: ${this.controller ? '已创建' : '未创建'}`);

    // 验证代理配置
    log(`📋 代理配置检查:`);
    log(`  - object: ${this.proxy.object ? '已设置' : '未设置'}`);
    log(`  - name: ${this.proxy.name}`);
    log(`  - methodList: ${this.proxy.methodList?.join(', ')}`);
    log(`  - controller: ${this.proxy.controller ? '已设置' : '未设置'}`);
  }

  private proxy: JavaScriptProxy = {
    object: this.jsProxy,
    name: BASE_WIN_OBJ,
    methodList: JSMethodArray,
    controller: this.controller
  };

  build() {
    Column() {
      // 工具栏
      if ((this.vm.param?.isWebNavigation || 0) > 0) {
        CommonToolbar({ config: ToolbarPresets.simple(`${this.vm.title}`) });
      }
      // 顶部的加载进度条组件
      Progress({
        value: this.progress,
        total: 100,
        type: ProgressType.Linear
      })
        .width('100%')
        .height(1)
        .color(Color.Blue)
        .visibility(this.isProgressVisible ? Visibility.Visible : Visibility.Hidden)
        .align(Alignment.TopStart);

      Web({
        src: this.vm.param?.url,
        controller: this.controller
      })
        .javaScriptAccess(true)
        .domStorageAccess(true)
        .fileAccess(true)
        .imageAccess(true)
        .onlineImageAccess(true)
        .geolocationAccess(true)
        .cacheMode(CacheMode.Online)
        .mixedMode(MixedMode.All)
        .databaseAccess(true)
        .javaScriptProxy(this.proxy)  // 🔥 关键修复：注册JavaScript代理
        .onTitleReceive((e) => {
          if (e) this.vm.title = e.title;
        })
        .onPageBegin((e) => {
          log(`🚀 WebView页面开始加载: ${e?.url}`);
          log(`📊 当前时间: ${new Date().toLocaleString()}`);
        })
        .onPageEnd((e) => {
          log(`✅ WebView页面加载完成: ${e?.url}`);
          log(`📊 加载完成时间: ${new Date().toLocaleString()}`);

          // 简化测试：只检查对象注入状态，不做任何手动调用
          this.simpleObjectCheck();
        })
        .onErrorReceive((e) => {
          log(`❌ WebView加载错误: ${e?.error?.getErrorInfo()}`);
        })
        .javaScriptProxy(this.proxy)
        .onProgressChange((e) => {
          if (e) {
            this.progress = e.newProgress;
            // 当开始加载且进度未满100时，显示进度条
            if (this.progress > 0 && this.progress < 100) {
              this.isProgressVisible = true;
            }
            // 当加载完成时，延迟一小段时间再隐藏，以优化视觉效果
            else if (this.progress === 100) {
              setTimeout(() => {
                this.isProgressVisible = false;
              }, 300);
            }
          }
        });
    }
    .width('100%')
    .height('100%')
    .onAppear(() => {

    })
  }

  // 检查并注入JavaScript对象
  private checkAndInjectJavaScript() {
    try {
      log(`开始检查JavaScript对象注入状态...`);

      // 延迟一小段时间确保页面完全加载
      setTimeout(() => {
        // 详细的JavaScript对象检查
        const checkScript = `
          (function() {
            console.log('=== JavaScript对象检查开始 ===');
            console.log('window对象:', typeof window);
            console.log('查找对象: ${BASE_WIN_OBJ}');
            console.log('window.${BASE_WIN_OBJ}:', typeof window.${BASE_WIN_OBJ});

            if (typeof window.${BASE_WIN_OBJ} !== 'undefined') {
              console.log('✅ ${BASE_WIN_OBJ} 对象已存在');
              console.log('对象内容:', window.${BASE_WIN_OBJ});

              // 检查方法是否存在
              var methods = ['client_isAlready', 'client_getToken', 'client_getUserInfo', 'client_goBack', 'client_popBack'];
              methods.forEach(function(method) {
                if (typeof window.${BASE_WIN_OBJ}[method] === 'function') {
                  console.log('✅ 方法 ' + method + ' 存在');
                } else {
                  console.log('❌ 方法 ' + method + ' 不存在');
                }
              });

              return 'SUCCESS';
            } else {
              console.log('❌ ${BASE_WIN_OBJ} 对象不存在');
              console.log('window对象的所有属性:');
              for (var key in window) {
                if (key.indexOf('DD') !== -1 || key.indexOf('client') !== -1) {
                  console.log('  - ' + key + ':', typeof window[key]);
                }
              }
              return 'NOT_FOUND';
            }
          })();
        `;

        this.controller.runJavaScript(checkScript)
          .then((result) => {
            log(`JavaScript对象检查结果: ${result}`);
            if (result === 'SUCCESS') {
              log(`✅ JavaScript对象注入成功，开始触发页面准备就绪事件`);
            } else {
              log(`❌ JavaScript对象注入失败，但仍尝试触发页面准备就绪事件`);
            }
            this.triggerPageReady();

            // 额外的手动测试
            this.manualTestJavaScriptCall();

            // 如果是业务页面，添加额外的调试
            this.debugBusinessPage();
          })
          .catch((error: Error) => {
            log(`❌ JavaScript对象检查失败: ${error.message}`);
            this.triggerPageReady();
            this.manualTestJavaScriptCall();
            this.debugBusinessPage();
          });
      }, 1000); // 增加延迟时间到1秒
    } catch (error) {
      log(`❌ 注入JavaScript对象失败: ${error}`);
    }
  }

  // 触发页面准备就绪事件
  private triggerPageReady() {
    try {
      // 通知页面原生对象已准备就绪
      const readyScript = `
        (function() {
          // 触发自定义事件通知页面原生对象已准备就绪
          if (typeof window.dispatchEvent === 'function') {
            var event = new CustomEvent('nativeReady', {
              detail: { platform: 'harmonyos', object: '${BASE_WIN_OBJ}' }
            });
            window.dispatchEvent(event);
            console.log('已触发 nativeReady 事件');
          }

          // 如果页面有特定的初始化方法，也可以直接调用
          if (typeof window.onNativeReady === 'function') {
            window.onNativeReady('${BASE_WIN_OBJ}');
            console.log('已调用 onNativeReady 方法');
          }

          return true;
        })();
      `;

      this.controller.runJavaScript(readyScript)
        .then((result) => {
          log(`页面准备就绪事件触发成功: ${result}`);
        })
        .catch((error: Error) => {
          log(`页面准备就绪事件触发失败: ${error.message}`);
        });
    } catch (error) {
      log(`触发页面准备就绪事件失败: ${error}`);
    }
  }

  // 手动测试JavaScript调用
  private manualTestJavaScriptCall() {
    setTimeout(() => {
      log(`🧪 开始手动测试JavaScript调用...`);

      const testScript = `
        (function() {
          console.log('🧪 手动测试开始');

          if (typeof window.${BASE_WIN_OBJ} !== 'undefined') {
            console.log('✅ 对象存在，尝试调用 client_isAlready');

            if (typeof window.${BASE_WIN_OBJ}.client_isAlready === 'function') {
              console.log('✅ 方法存在，开始调用');

              var testData = {
                title: '手动测试标题',
                subTitle: '手动测试副标题',
                objName: '${BASE_WIN_OBJ}',
                useTitleBar: true
              };

              console.log('📤 发送测试数据:', JSON.stringify(testData));
              window.${BASE_WIN_OBJ}.client_isAlready(JSON.stringify(testData));

              return 'MANUAL_CALL_SUCCESS';
            } else {
              console.log('❌ client_isAlready 方法不存在');
              return 'METHOD_NOT_FOUND';
            }
          } else {
            console.log('❌ ${BASE_WIN_OBJ} 对象不存在');
            return 'OBJECT_NOT_FOUND';
          }
        })();
      `;

      this.controller.runJavaScript(testScript)
        .then((result) => {
          log(`🧪 手动测试结果: ${result}`);
        })
        .catch((error: Error) => {
          log(`🧪 手动测试失败: ${error.message}`);
        });
    }, 2000); // 2秒后执行手动测试
  }

  // 调试业务页面
  private debugBusinessPage() {
    const currentUrl = this.vm.param?.url || '';
    const isTestPage = currentUrl.includes('test_webview.html');

    if (!isTestPage) {
      log(`🔍 开始调试业务页面: ${currentUrl}`);

      setTimeout(() => {
        const businessDebugScript = `
          (function() {
            console.log('🔍 业务页面调试开始');
            console.log('当前URL:', window.location.href);
            console.log('页面标题:', document.title);

            // 检查页面是否有自己的JavaScript调用逻辑
            console.log('页面脚本数量:', document.scripts.length);

            // 尝试查找页面中是否有调用原生方法的代码
            var pageContent = document.documentElement.outerHTML;
            var hasClientIsAlready = pageContent.indexOf('client_isAlready') !== -1;
            var hasDDBESOFFICE = pageContent.indexOf('DDBESOFFICE') !== -1;

            console.log('页面包含 client_isAlready:', hasClientIsAlready);
            console.log('页面包含 DDBESOFFICE:', hasDDBESOFFICE);

            // 检查页面是否有DOMContentLoaded或window.onload事件
            console.log('DOMContentLoaded 事件监听器数量:',
              document.addEventListener ? '支持addEventListener' : '不支持addEventListener');

            // 强制调用一次 client_isAlready 来测试
            if (typeof window.${BASE_WIN_OBJ} !== 'undefined' &&
                typeof window.${BASE_WIN_OBJ}.client_isAlready === 'function') {

              console.log('🚀 业务页面强制调用 client_isAlready');

              var businessTestData = {
                title: '业务页面调试测试',
                subTitle: '来自业务页面的调用',
                objName: '${BASE_WIN_OBJ}', // 'ddbes_web'
                useTitleBar: true,
                debugMode: true,
                url: window.location.href
              };

              window.${BASE_WIN_OBJ}.client_isAlready(JSON.stringify(businessTestData));

              return 'BUSINESS_DEBUG_CALL_SUCCESS';
            } else {
              console.log('❌ 业务页面中原生对象或方法不可用');
              return 'BUSINESS_DEBUG_CALL_FAILED';
            }
          })();
        `;

        this.controller.runJavaScript(businessDebugScript)
          .then((result) => {
            log(`🔍 业务页面调试结果: ${result}`);
          })
          .catch((error: Error) => {
            log(`🔍 业务页面调试失败: ${error.message}`);
          });
      }, 3000); // 3秒后执行业务页面调试
    }
  }

  // 调试业务页面内容
  private debugBusinessPageContent(url: string) {
    log(`🔍 开始分析业务页面内容: ${url}`);

    setTimeout(() => {
      const contentAnalysisScript = `
        (function() {
          console.log('🔍 === 业务页面内容分析开始 ===');

          // 1. 基础信息
          console.log('页面URL:', window.location.href);
          console.log('页面标题:', document.title);
          console.log('页面就绪状态:', document.readyState);

          // 2. 检查页面内容
          var htmlContent = document.documentElement.outerHTML;
          var hasClientIsAlready = htmlContent.indexOf('client_isAlready') !== -1;
          var hasDdbesWeb = htmlContent.indexOf('ddbes_web') !== -1;
          var hasDDBESOFFICE = htmlContent.indexOf('DDBESOFFICE') !== -1;

          console.log('页面包含 client_isAlready:', hasClientIsAlready);
          console.log('页面包含 ddbes_web:', hasDdbesWeb);
          console.log('页面包含 DDBESOFFICE:', hasDDBESOFFICE);

          // 检查原生对象状态（两个对象名都检查）
          console.log('window.DDBESOFFICE 状态:', typeof window.DDBESOFFICE);
          console.log('window.ddbes_web 状态:', typeof window.ddbes_web);

          // 3. 检查脚本标签
          var scripts = document.getElementsByTagName('script');
          console.log('页面脚本数量:', scripts.length);

          for (var i = 0; i < scripts.length; i++) {
            var script = scripts[i];
            if (script.src) {
              console.log('外部脚本 ' + i + ':', script.src);
            } else if (script.innerHTML) {
              var content = script.innerHTML;
              if (content.indexOf('client_isAlready') !== -1 ||
                  content.indexOf('ddbes_web') !== -1 ||
                  content.indexOf('DDBESOFFICE') !== -1) {
                console.log('发现相关脚本 ' + i + ':', content.substring(0, 200) + '...');
              }
            }
          }

          // 4. 检查事件监听器
          console.log('DOMContentLoaded 支持:', typeof document.addEventListener === 'function');
          console.log('window.onload 状态:', typeof window.onload);

          // 5. 检查原生对象状态
          console.log('window.ddbes_web 状态:', typeof window.ddbes_web);
          if (typeof window.ddbes_web !== 'undefined') {
            console.log('ddbes_web 对象内容:', Object.keys(window.ddbes_web));
            console.log('client_isAlready 方法:', typeof window.ddbes_web.client_isAlready);
          }

          // 6. 检查是否有延迟执行的代码
          console.log('setTimeout 调用数量:', window.setTimeout.toString().indexOf('[native code]') !== -1 ? '原生' : '被重写');

          // 7. 尝试查找可能的调用代码
          var possibleCalls = [];
          if (htmlContent.indexOf('client_isAlready') !== -1) {
            var matches = htmlContent.match(/[^\\w]client_isAlready[^\\w]/g);
            if (matches) {
              possibleCalls = matches;
            }
          }
          console.log('可能的调用:', possibleCalls);

          return {
            hasClientIsAlready: hasClientIsAlready,
            hasDdbesWeb: hasDdbesWeb,
            hasDDBESOFFICE: hasDDBESOFFICE,
            scriptCount: scripts.length,
            nativeObjectExists: typeof window.ddbes_web !== 'undefined',
            readyState: document.readyState
          };
        })();
      `;

      this.controller.runJavaScript(contentAnalysisScript)
        .then((result) => {
          log(`🔍 业务页面内容分析结果: ${result}`);

          // 如果页面没有调用代码，进行更深入的分析
          const resultObj = JSON.parse(result) as PageAnalysisResult;
          if (!resultObj.hasClientIsAlready && !resultObj.hasDDBESOFFICE && !resultObj.hasDdbesWeb) {
            log(`⚠️ 页面没有发现原生调用代码，进行深度分析...`);
            this.deepAnalyzePageContent();
          }

          // 延迟5秒后再次检查，看是否有延迟加载的脚本
          setTimeout(() => {
            this.checkDelayedScripts();
          }, 5000);
        })
        .catch((error: Error) => {
          log(`🔍 业务页面内容分析失败: ${error.message}`);
        });
    }, 2000); // 页面加载完成2秒后分析
  }

  // 检查延迟脚本
  private checkDelayedScripts() {
    log(`🕐 检查延迟加载的脚本...`);

    const delayedCheckScript = `
      (function() {
        console.log('🕐 === 延迟脚本检查 ===');

        // 再次检查原生对象
        console.log('延迟检查 - window.ddbes_web:', typeof window.ddbes_web);

        // 检查是否有新的脚本被加载
        var scripts = document.getElementsByTagName('script');
        console.log('延迟检查 - 脚本数量:', scripts.length);

        // 检查页面是否有异步加载完成的标志
        console.log('延迟检查 - 页面就绪状态:', document.readyState);

        // 尝试手动触发可能的初始化函数
        var initFunctions = ['init', 'onReady', 'pageReady', 'documentReady'];
        for (var i = 0; i < initFunctions.length; i++) {
          var funcName = initFunctions[i];
          if (typeof window[funcName] === 'function') {
            console.log('发现初始化函数:', funcName);
          }
        }

        return 'DELAYED_CHECK_COMPLETE';
      })();
    `;

    this.controller.runJavaScript(delayedCheckScript)
      .then((result) => {
        log(`🕐 延迟脚本检查结果: ${result}`);
      })
      .catch((error: Error) => {
        log(`🕐 延迟脚本检查失败: ${error.message}`);
      });
  }

  // 深度分析页面内容
  private deepAnalyzePageContent() {
    log(`🔬 开始深度分析页面内容...`);

    const deepAnalysisScript = `
      (function() {
        console.log('🔬 === 深度页面分析开始 ===');

        // 1. 获取页面的完整HTML内容（前1000字符）
        var htmlContent = document.documentElement.outerHTML;
        var htmlPreview = htmlContent.substring(0, 1000);
        console.log('页面HTML预览:', htmlPreview);

        // 2. 检查所有脚本的详细内容
        var scripts = document.getElementsByTagName('script');
        var scriptDetails = [];

        for (var i = 0; i < scripts.length; i++) {
          var script = scripts[i];
          var detail = {
            index: i,
            src: script.src || 'inline',
            hasContent: !!script.innerHTML,
            contentPreview: script.innerHTML ? script.innerHTML.substring(0, 200) : ''
          };
          scriptDetails.push(detail);
          console.log('脚本 ' + i + ':', detail);
        }

        // 3. 检查是否是单页应用(SPA)
        var isVueApp = typeof window.Vue !== 'undefined' ||
                       document.querySelector('[data-v-]') !== null ||
                       htmlContent.indexOf('vue') !== -1;
        var isReactApp = typeof window.React !== 'undefined' ||
                        htmlContent.indexOf('react') !== -1;
        var isAngularApp = typeof window.angular !== 'undefined' ||
                          htmlContent.indexOf('angular') !== -1;

        console.log('Vue应用:', isVueApp);
        console.log('React应用:', isReactApp);
        console.log('Angular应用:', isAngularApp);

        // 4. 检查路由信息
        console.log('当前路由:', window.location.hash);
        console.log('路径名:', window.location.pathname);

        // 5. 检查是否有异步模块加载
        var hasWebpack = htmlContent.indexOf('webpack') !== -1;
        var hasRequireJS = typeof window.require !== 'undefined';
        var hasSystemJS = typeof window.System !== 'undefined';

        console.log('Webpack:', hasWebpack);
        console.log('RequireJS:', hasRequireJS);
        console.log('SystemJS:', hasSystemJS);

        // 6. 检查全局变量
        var globalVars = [];
        for (var key in window) {
          if (key.indexOf('ddbes') !== -1 ||
              key.indexOf('client') !== -1 ||
              key.indexOf('native') !== -1 ||
              key.indexOf('app') !== -1) {
            globalVars.push(key + ': ' + typeof window[key]);
          }
        }
        console.log('相关全局变量:', globalVars);

        // 7. 尝试查找可能的原生调用模式
        var possiblePatterns = [
          'window.webkit',
          'window.android',
          'window.harmony',
          'bridge',
          'jsbridge',
          'postMessage',
          'callNative',
          'invokeNative'
        ];

        var foundPatterns = [];
        for (var j = 0; j < possiblePatterns.length; j++) {
          var pattern = possiblePatterns[j];
          if (htmlContent.indexOf(pattern) !== -1) {
            foundPatterns.push(pattern);
          }
        }
        console.log('发现的可能调用模式:', foundPatterns);

        return {
          scriptCount: scripts.length,
          isVueApp: isVueApp,
          isReactApp: isReactApp,
          isAngularApp: isAngularApp,
          hasWebpack: hasWebpack,
          currentRoute: window.location.hash,
          foundPatterns: foundPatterns,
          globalVarsCount: globalVars.length,
          htmlLength: htmlContent.length
        };
      })();
    `;

    this.controller.runJavaScript(deepAnalysisScript)
      .then((result) => {
        log(`🔬 深度分析结果: ${result}`);

        // 根据分析结果给出建议
        const analysisResult = JSON.parse(result) as DeepAnalysisResult;
        this.provideSuggestions(analysisResult);
      })
      .catch((error: Error) => {
        log(`🔬 深度分析失败: ${error.message}`);
      });
  }

  // 根据分析结果提供建议
  private provideSuggestions(analysis: DeepAnalysisResult): void {
    log(`💡 === 分析建议 ===`);

    if (analysis.isVueApp) {
      log(`💡 检测到Vue应用，可能需要在Vue组件中调用原生方法`);
    }

    if (analysis.isReactApp) {
      log(`💡 检测到React应用，可能需要在React组件中调用原生方法`);
    }

    if (analysis.hasWebpack) {
      log(`💡 检测到Webpack打包，原生调用代码可能在异步模块中`);
    }

    if (analysis.foundPatterns.length > 0) {
      log(`💡 发现可能的调用模式: ${analysis.foundPatterns.join(', ')}`);
    } else {
      log(`💡 页面可能是纯H5应用，不需要调用原生方法`);
    }

    if (analysis.currentRoute) {
      log(`💡 当前路由: ${analysis.currentRoute}，可能需要在特定路由下才调用原生方法`);
    }
  }

  // 简单的对象检查，不做任何手动调用
  private simpleObjectCheck() {
    log(`🔍 开始简单对象检查...`);

    // 多次检查，观察对象注入的时机
    const checkTimes = [500, 1000, 2000, 3000, 5000]; // 0.5s, 1s, 2s, 3s, 5s

    checkTimes.forEach((delay, index) => {
      setTimeout(() => {
        const checkScript = `
          (function() {
            var result = {
              timestamp: new Date().toLocaleTimeString(),
              checkIndex: ${index + 1},
              ddbesofficeExists: typeof window.DDBESOFFICE !== 'undefined',
              ddbesofficeType: typeof window.DDBESOFFICE,
              ddbeswebExists: typeof window.ddbes_web !== 'undefined',
              ddbeswebType: typeof window.ddbes_web,
              clientIsAlreadyExists: false,
              windowKeys: Object.keys(window).filter(key =>
                key.indexOf('ddbes') !== -1 ||
                key.indexOf('DDBES') !== -1 ||
                key.indexOf('client') !== -1
              )
            };

            if (window.DDBESOFFICE && typeof window.DDBESOFFICE.client_isAlready === 'function') {
              result.clientIsAlreadyExists = true;
            }

            console.log('=== 对象检查结果 ===', result);
            return JSON.stringify(result);
          })();
        `;

        this.controller.runJavaScript(checkScript)
          .then((result) => {
            log(`🔍 第${index + 1}次检查(${delay}ms后): ${result}`);
          })
          .catch((error: Error) => {
            log(`🔍 第${index + 1}次检查失败: ${error.message}`);
          });
      }, delay);
    });
  }

  aboutToDisappear(): void {}

  onPageHide(): void {}
}