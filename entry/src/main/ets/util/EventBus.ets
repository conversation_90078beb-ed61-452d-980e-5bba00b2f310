

import common from '@ohos.app.ability.common';

/// eventHub ： 事件总线：通信

export function sendEvent<T>(context : UIContext ,key:string ,data?:T) {
  const mContext = getContext(context) as common.UIAbilityContext;
  const eventHub = mContext.eventHub;
  eventHub.emit(key ,data)
}


export function onEvent(context : UIContext ,key:string , callback: (data: object) => void)  {
  const mContext = getContext(context) as common.UIAbilityContext;
  const eventHub = mContext.eventHub;
  eventHub.on(key , callback)
}

export function unSubscribe(context : UIContext ,key:string ,) {
  const mContext = getContext(context) as common.UIAbilityContext;
  const eventHub = mContext.eventHub;
  eventHub.off(key)
}