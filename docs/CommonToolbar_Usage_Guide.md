# CommonToolbar 通用工具栏组件使用指南

## 概述

CommonToolbar是一个高度可定制的通用工具栏组件，支持返回按钮、标题显示和右侧功能按钮。组件提供了灵活的配置选项和多种预设样式，满足不同页面的需求。

## 核心特性

### ✅ **功能特性**
- 🔙 **智能返回**：默认关闭当前页面，支持自定义返回逻辑
- 📝 **标题显示**：支持自定义标题文字和样式
- 🎯 **操作按钮**：支持多个右侧功能按钮
- 🎨 **样式定制**：支持自定义颜色、背景、阴影等
- 📱 **响应式设计**：自适应不同屏幕尺寸

### ✅ **易用特性**
- 🚀 **预设样式**：提供多种常用样式预设
- 🔧 **灵活配置**：通过配置对象控制所有功能
- 📦 **开箱即用**：无需复杂配置即可使用
- 🎯 **类型安全**：完整的TypeScript类型支持

## 接口定义

### ToolbarAction 操作按钮配置
```typescript
export interface ToolbarAction {
  text?: string;           // 按钮文字
  icon?: string | Resource; // 按钮图标
  onClick: () => void;     // 点击回调
  enabled?: boolean;       // 是否启用，默认true
  visible?: boolean;       // 是否可见，默认true
}
```

### ToolbarConfig 工具栏配置
```typescript
export interface ToolbarConfig {
  title?: string;                    // 标题文字
  showBackButton?: boolean;          // 是否显示返回按钮，默认true
  backButtonIcon?: string | Resource; // 返回按钮图标
  onBackClick?: () => void;          // 返回按钮点击回调
  backgroundColor?: string | Resource; // 背景色
  titleColor?: string | Resource;    // 标题颜色
  elevation?: boolean;               // 是否显示阴影，默认true
  actions?: ToolbarAction[];         // 右侧操作按钮数组
}
```

## 基础使用

### 1. 最简单的使用
```typescript
@Entry
@ComponentV2
struct MyPage {
  build() {
    Column() {
      // 基础工具栏
      CommonToolbar({
        config: {
          title: "页面标题"
        }
      })
      
      // 页面内容
      Text("页面内容")
        .layoutWeight(1)
    }
  }
}
```

### 2. 带操作按钮的工具栏
```typescript
CommonToolbar({
  config: {
    title: "编辑页面",
    actions: [
      {
        text: "保存",
        onClick: () => {
          // 保存逻辑
          this.saveData();
        }
      },
      {
        text: "⋯",
        onClick: () => {
          // 显示更多菜单
          this.showMoreMenu();
        }
      }
    ]
  }
})
```

### 3. 自定义返回逻辑
```typescript
CommonToolbar({
  config: {
    title: "编辑文档",
    onBackClick: () => {
      // 自定义返回逻辑
      if (this.hasUnsavedChanges) {
        this.showSaveConfirmDialog();
      } else {
        router.back();
      }
    }
  }
})
```

## 预设样式使用

### 1. 简单标题工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.simple("页面标题")
})
```

### 2. 带保存按钮的工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.withSave(
    "编辑页面", 
    () => this.save()
  )
})
```

### 3. 带更多菜单的工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.withMore(
    "列表页面", 
    () => this.showMoreMenu()
  )
})
```

### 4. 编辑页面工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.edit(
    "编辑模式",
    () => this.save(),
    () => this.cancel()
  )
})
```

### 5. 搜索页面工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.search(
    "搜索结果",
    () => this.openSearch()
  )
})
```

### 6. 无返回按钮的工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.noBack("主页面", [
    { text: "设置", onClick: () => this.openSettings() }
  ])
})
```

### 7. 透明工具栏
```typescript
CommonToolbar({
  config: ToolbarPresets.transparent("透明标题")
})
```

## 高级配置

### 1. 完全自定义样式
```typescript
CommonToolbar({
  config: {
    title: "自定义样式",
    backgroundColor: "#4A90E2",
    titleColor: "#FFFFFF",
    elevation: true,
    backButtonIcon: "←",
    actions: [
      {
        text: "完成",
        onClick: () => this.finish(),
        enabled: this.isValid
      }
    ]
  }
})
```

### 2. 动态按钮状态
```typescript
@State isEditing: boolean = false;
@State hasChanges: boolean = false;

CommonToolbar({
  config: {
    title: this.isEditing ? "编辑模式" : "查看模式",
    actions: [
      {
        text: this.isEditing ? "完成" : "编辑",
        onClick: () => {
          if (this.isEditing) {
            this.finishEditing();
          } else {
            this.startEditing();
          }
        }
      },
      {
        text: "保存",
        onClick: () => this.save(),
        visible: this.isEditing,
        enabled: this.hasChanges
      }
    ]
  }
})
```

### 3. 图标按钮
```typescript
CommonToolbar({
  config: {
    title: "图片查看器",
    actions: [
      {
        icon: $r('app.media.ic_favorite'),
        onClick: () => this.toggleFavorite()
      },
      {
        icon: $r('app.media.ic_share'),
        onClick: () => this.share()
      },
      {
        icon: $r('app.media.ic_delete'),
        onClick: () => this.delete()
      }
    ]
  }
})
```

## 实际应用场景

### 1. 详情页面
```typescript
@Entry
@ComponentV2
struct DetailPage {
  @State item: DetailItem | null = null;

  build() {
    Column() {
      CommonToolbar({
        config: {
          title: this.item?.title || "详情",
          actions: [
            {
              text: "编辑",
              onClick: () => this.editItem()
            },
            {
              text: "⋯",
              onClick: () => this.showMoreOptions()
            }
          ]
        }
      })

      // 详情内容
      if (this.item) {
        this.DetailContentBuilder()
      }
    }
  }
}
```

### 2. 表单页面
```typescript
@Entry
@ComponentV2
struct FormPage {
  @State formData: FormData = new FormData();
  @State isValid: boolean = false;

  build() {
    Column() {
      CommonToolbar({
        config: ToolbarPresets.edit(
          "编辑信息",
          () => this.submitForm(),
          () => this.confirmCancel()
        )
      })

      // 表单内容
      this.FormContentBuilder()
    }
  }

  private confirmCancel(): void {
    if (this.formData.hasChanges()) {
      // 显示确认对话框
      this.showCancelConfirmDialog();
    } else {
      router.back();
    }
  }
}
```

### 3. 列表页面
```typescript
@Entry
@ComponentV2
struct ListPage {
  @State searchMode: boolean = false;

  build() {
    Column() {
      CommonToolbar({
        config: {
          title: this.searchMode ? "搜索" : "列表",
          showBackButton: !this.searchMode,
          actions: this.searchMode ? [
            {
              text: "取消",
              onClick: () => this.exitSearchMode()
            }
          ] : [
            {
              text: "🔍",
              onClick: () => this.enterSearchMode()
            },
            {
              text: "筛选",
              onClick: () => this.showFilter()
            }
          ]
        }
      })

      // 列表内容
      this.ListContentBuilder()
    }
  }
}
```

## 最佳实践

### 1. 标题设计
- ✅ **简洁明了**：标题应该简洁地描述页面功能
- ✅ **层级清晰**：体现页面在应用中的层级关系
- ✅ **动态更新**：根据页面状态动态更新标题

### 2. 按钮设计
- ✅ **功能明确**：按钮文字或图标应该清晰表达功能
- ✅ **数量适中**：右侧按钮不宜过多，建议不超过3个
- ✅ **状态反馈**：合理使用enabled和visible属性

### 3. 返回逻辑
- ✅ **保存提醒**：编辑页面返回时提醒保存未保存的更改
- ✅ **确认操作**：重要操作前进行确认
- ✅ **状态清理**：返回时清理页面状态

### 4. 样式一致性
- ✅ **统一风格**：在应用中保持工具栏样式的一致性
- ✅ **主题适配**：支持深色模式等主题切换
- ✅ **品牌色彩**：使用符合品牌的颜色方案

## 注意事项

### 1. 性能考虑
- 避免在actions数组中创建过多的匿名函数
- 合理使用visible属性控制按钮显示，而不是动态修改数组

### 2. 用户体验
- 确保返回按钮的行为符合用户预期
- 重要操作按钮应该有明确的视觉反馈
- 考虑不同屏幕尺寸下的显示效果

### 3. 无障碍支持
- 为图标按钮提供合适的无障碍标签
- 确保按钮有足够的点击区域
- 支持键盘导航

## 总结

CommonToolbar组件提供了：

✅ **完整的功能**：返回、标题、操作按钮一应俱全
✅ **灵活的配置**：支持各种自定义需求
✅ **丰富的预设**：常用样式开箱即用
✅ **优秀的体验**：符合移动端设计规范
✅ **类型安全**：完整的TypeScript支持

通过使用这个通用工具栏组件，可以快速构建一致性强、用户体验好的页面头部，提高开发效率和代码复用性。
