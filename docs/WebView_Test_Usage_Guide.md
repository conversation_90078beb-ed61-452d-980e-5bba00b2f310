# WebView 测试页面使用指南

## 概述

项目中现在提供了多种方式来测试 WebView JavaScript 调用功能，帮助您验证 `client_isAlready` 等方法是否正常工作。

## 测试方式

### 方式1：通过工作台访问（推荐）

1. **启动应用**
2. **进入工作台页面**
3. **找到测试工具**：
   - `WebView测试` - 在WebView中加载测试HTML页面
   - `JS调用测试` - 专用的测试页面，包含测试控制和结果显示

#### 工具说明

| 工具名称 | 功能描述 | 页面类型 |
|---------|---------|---------|
| WebView测试 | 在普通WebView中加载test_webview.html | WebViewPage |
| JS调用测试 | 专用测试页面，包含测试控制界面 | WebViewTestPage |

### 方式2：直接修改URL测试

如果您想在现有的WebView页面中测试，可以临时修改URL：

```typescript
// 在WebViewPage中临时修改
const testUrl = 'resource://rawfile/test_webview.html';

// 或者在调用时传入测试URL
routeTo(context, 'pages/webview/WebViewPage', 
  new WebViewParam('resource://rawfile/test_webview.html', 'JavaScript测试', 0));
```

### 方式3：使用专用测试页面

直接导航到测试页面：

```typescript
// 在任何地方调用
routeTo(context, 'pages/webview/WebViewTestPage');
```

## 测试页面功能

### WebViewTestPage 特性

1. **自动测试**：页面加载完成后自动运行基础测试
2. **手动测试**：提供"运行基础测试"按钮
3. **实时结果**：显示测试结果和日志
4. **详细日志**：记录每个步骤的执行情况

### 测试内容

#### 基础测试包括：
- ✅ 检查 `DDBESOFFICE` 对象是否存在
- ✅ 检查 `client_isAlready` 方法是否存在
- ✅ 调用 `client_isAlready` 方法
- ✅ 验证参数传递和响应

#### 测试数据：
```json
{
  "title": "基础测试",
  "subTitle": "测试副标题", 
  "objName": "DDBESOFFICE",
  "useTitleBar": true
}
```

## 查看测试结果

### 1. 应用内结果

在 `JS调用测试` 页面中，您可以看到：
- 页面加载状态
- 对象检查结果
- 方法调用结果
- 详细的时间戳日志

### 2. DevEco Studio 日志

在 DevEco Studio 的日志窗口中查找：

```
🚀 JsProxy 开始初始化...
📱 控制器状态: 已提供
🎯 JavaScript对象名: DDBESOFFICE
📋 JsProxy 可用方法列表:
  1. client_isAlready: ✅ 存在

测试页面开始加载: resource://rawfile/test_webview.html
测试页面加载完成: resource://rawfile/test_webview.html

🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉
📨 接收到的消息: {"title":"基础测试","subTitle":"测试副标题","objName":"DDBESOFFICE","useTitleBar":true}
⏰ 调用时间: [时间戳]
```

### 3. 浏览器控制台

在测试页面中，打开浏览器控制台（如果支持），可以看到：

```javascript
=== 基础测试开始 ===
✅ DDBESOFFICE 对象存在
✅ client_isAlready 方法存在
📤 调用 client_isAlready
```

## 故障排除

### 问题1：工作台中看不到测试工具

**解决方案**：
1. 确保应用已重新编译
2. 检查 `WorkbenchViewModel.ets` 中的 `addLocalTools` 方法
3. 重启应用

### 问题2：测试页面无法加载

**可能原因**：
- 文件路径错误
- 权限配置问题

**解决方案**：
```typescript
// 确保文件存在于正确位置
entry/src/main/resources/rawfile/test_webview.html

// 检查WebView配置
.fileAccess(true)
.javaScriptAccess(true)
```

### 问题3：JavaScript对象不存在

**检查项目**：
1. JavaScriptProxy 配置是否正确
2. 方法列表是否包含所需方法
3. 页面加载时机是否合适

### 问题4：方法调用无响应

**调试步骤**：
1. 检查 DevEco Studio 日志
2. 确认对象和方法存在
3. 验证参数格式
4. 检查调用时机

## 自定义测试

### 添加新的测试方法

在 `WebViewTestPage.ets` 中添加：

```typescript
private runCustomTest() {
  const testScript = `
    // 您的自定义测试代码
    if (window.DDBESOFFICE && window.DDBESOFFICE.client_getToken) {
      window.DDBESOFFICE.client_getToken('{"requestId":"test123"}');
      return 'CUSTOM_TEST_SUCCESS';
    }
    return 'CUSTOM_TEST_FAILED';
  `;
  
  this.controller.runJavaScript(testScript)
    .then((result) => {
      this.addTestResult(`自定义测试结果: ${result}`);
    });
}
```

### 修改测试数据

在测试脚本中修改 `testData` 对象：

```javascript
var testData = {
  title: '您的标题',
  subTitle: '您的副标题',
  objName: 'DDBESOFFICE',
  useTitleBar: false,
  // 添加其他字段
  customField: '自定义值'
};
```

## 最佳实践

1. **先使用专用测试页面**：`JS调用测试` 提供最完整的测试环境
2. **观察日志输出**：重点关注带有表情符号的关键日志
3. **逐步测试**：先测试基础功能，再测试复杂场景
4. **保留测试工具**：在开发阶段保留这些测试入口

通过这些测试方式，您应该能够快速定位和解决 WebView JavaScript 调用的问题。
