import { UserManager } from "../user_manage/UserManage";

/**
 * 网络请求配置
 */
export class NetworkConfig {
  // 基础URL


  // 请求超时时间（毫秒）
  static readonly TIMEOUT = 10000;
  
  // 默认token（实际项目中应该从用户登录信息获取）
  static readonly DEFAULT_TOKEN = UserManager.getUserToken();
  static readonly IM_TOKEN = UserManager.getImToken();

  // 请求头
  static readonly CONTENT_TYPE: string = 'application/json';
  static readonly ACCEPT: string = 'application/json';

  // 日志配置
  static readonly ENABLE_REQUEST_LOG: boolean = true;
  static readonly ENABLE_RESPONSE_LOG: boolean = true;
  static readonly LOG_MAX_BODY_SIZE: number = 20000;
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = Object> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 网络请求错误类型
 */
export enum NetworkErrorType {
  TIMEOUT = 'TIMEOUT',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 网络请求错误
 */
export class NetworkError extends Error {
  public type: NetworkErrorType;
  public code?: number;

  constructor(
    type: NetworkErrorType,
    code?: number,
    message?: string
  ) {
    super(message || type);
    this.name = 'NetworkError';
    this.type = type;
    this.code = code;
  }
}

/**
 * 请求参数接口
 */
export interface RequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, Object>;
  data?: Object;
  headers?: Record<string, string>;
  timeout?: number;
  token?: string;
  imToken?: string;
}
