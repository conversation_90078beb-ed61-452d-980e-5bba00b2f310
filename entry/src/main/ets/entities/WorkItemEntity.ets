import { Entity, Columns, Id, NotNull, Index, ColumnType } from '@ohos/dataorm';
import { WorkItem } from '../data_source/SessionBody';

/**
 * WorkItem实体类 - 工作台应用数据
 * @deprecated 弃用 !!
 */
@Entity('WORK_ITEMS')
export class WorkItemEntity {
  @Id()
  @Columns({ columnName: 'ID', types: ColumnType.str })
  id: string;

  @NotNull()
  @Index()
  @Columns({ columnName: 'NAME', types: ColumnType.str })
  name: string;

  @Columns({ columnName: 'ICON', types: ColumnType.str })
  icon: string;

  @Columns({ columnName: 'DESCRIPTION', types: ColumnType.str })
  description: string;

  @Columns({ columnName: 'BADGE', types: ColumnType.num })
  badge: number;

  @Columns({ columnName: 'CREATE_TIME', types: ColumnType.num })
  createTime: number;

  @Columns({ columnName: 'UPDATE_TIME', types: ColumnType.num })
  updateTime: number;

  constructor(
    id?: string,
    name?: string,
    icon?: string,
    description?: string,
    badge?: number,
    createTime?: number,
    updateTime?: number
  ) {
    this.id = id || '';
    this.name = name || '';
    this.icon = icon || '';
    this.description = description || '';
    this.badge = badge || 0;
    this.createTime = createTime || Math.floor(Date.now() / 1000);
    this.updateTime = updateTime || Math.floor(Date.now() / 1000);
  }

  // Getter和Setter方法
  getId(): string {
    return this.id;
  }

  setId(id: string): void {
    this.id = id;
  }

  getName(): string {
    return this.name;
  }

  setName(name: string): void {
    this.name = name;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getIcon(): string {
    return this.icon;
  }

  setIcon(icon: string): void {
    this.icon = icon;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getDescription(): string {
    return this.description;
  }

  setDescription(description: string): void {
    this.description = description;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getBadge(): number {
    return this.badge;
  }

  setBadge(badge: number): void {
    this.badge = badge;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getCreateTime(): number {
    return this.createTime;
  }

  setCreateTime(createTime: number): void {
    this.createTime = createTime;
  }

  getUpdateTime(): number {
    return this.updateTime;
  }

  setUpdateTime(updateTime: number): void {
    this.updateTime = updateTime;
  }

  // 转换为WorkItem接口的方法
  toWorkItem(): WorkItem {
    return {
      id: this.id,
      name: this.name,
      icon: this.icon,
      description: this.description,
      badge: this.badge > 0 ? this.badge : undefined
    };
  }

  // 从WorkItem接口创建实体的静态方法
  static fromWorkItem(workItem: WorkItem): WorkItemEntity {
    return new WorkItemEntity(
      workItem.id,
      workItem.name,
      workItem.icon,
      workItem.description,
      workItem.badge || 0,
      Math.floor(Date.now() / 1000),
      Math.floor(Date.now() / 1000)
    );
  }
}


