

import { window } from '@kit.ArkUI'; // 导入ArkUI的window模块
import { common } from '@kit.AbilityKit'; // 导入AbilityKit的common模块

export class AppUtil {
  private static windowStage: window.WindowStage; // 静态变量，用于存储窗口管理器
  private static context: common.UIAbilityContext; // 静态变量，用于存储UIAbility的上下文信息

  /**
   * 初始化方法，缓存全局变量，在UIAbility的onWindowStageCreate方法中调用该方法进行初始化。
   * Initialization method, caches global variables, call this method in the onWindowStageCreate method of UIAbility for initialization.
   * @param context 上下文
   * @param windowStage 窗口管理器
   */
  static init(context: common.UIAbilityContext, windowStage: window.WindowStage) {
    AppUtil.context = context; // 初始化上下文
    AppUtil.windowStage = windowStage; // 初始化窗口管理器
  }

  /**
   * 获取主窗口
   * Get the main window
   */
  static getMainWindow(): window.Window {
    if (!AppUtil.windowStage) { // 如果窗口管理器未初始化
      console.error("windowStage为空，请在UIAbility的onWindowStageCreate方法中调用AppUtil的init方法进行初始化！WindowStage is null, please call the init method of AppUtil in the onWindowStageCreate method of UIAbility for initialization!");
    }
    return AppUtil.windowStage.getMainWindowSync(); // 同步获取主窗口
  }
  /**
   * 获取状态栏的高度，单位为px。
   * Get the height of the status bar, with the unit in pixels.
   * @returns 返回状态栏的高度，单位为px。
   *          Returns the height of the status bar, in pixels.
   */
  static getStatusBarHeight(): number {
    try {
      // 获取主窗口
      let windowClass = AppUtil.getMainWindow();
      // 获取系统避免区域，通常包含状态栏
      let avoidArea = windowClass.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
      // 返回状态栏的高度
      return avoidArea.topRect.height;
    } catch (err) {
      // 捕获异常并使用日志工具打印错误信息
      console.error(JSON.stringify(err));
      // 发生异常时返回0
      return 0;
    }
  }
}