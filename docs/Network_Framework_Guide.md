# HarmonyOS网络请求框架使用指南

## 概述

本文档介绍了为HarmonyOS项目封装的完整网络请求框架，包括axios封装、数据模型定义、数据库缓存和UI展示。该框架基于您提供的API接口设计，支持Bearer Token认证、响应数据缓存和网格化UI展示。

## 框架架构

### 分层架构
```
┌─────────────────────────────────────────┐
│                UI层                     │
│  WorkbenchView (网格展示)               │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              ViewModel层                │
│  WorkbenchViewModel (状态管理)          │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              Service层                  │
│  ToolDataService (数据服务)             │
│  ToolApiService (API服务)               │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              Network层                  │
│  HttpClient (网络请求封装)              │
│  NetworkConfig (配置管理)               │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              Database层                 │
│  DataOrmService (数据库服务)            │
│  ToolCacheEntity (缓存实体)             │
└─────────────────────────────────────────┘
```

## 核心组件

### 1. 网络配置 (NetworkConfig)
```typescript
export class NetworkConfig {
  static readonly BASE_URL = 'http://*********:8768';
  static readonly COMPANY_TOOLS_PATH = '/org/company/v4/group';
  static readonly TIMEOUT = 10000;
  static readonly DEFAULT_TOKEN = 'e9deb3c3-982b-43f4-b2bd-42506dd6872e';
}
```

### 2. HTTP客户端 (HttpClient)
```typescript
// 使用示例
const response = await httpClient.get<CompanyToolsData>(
  '/org/company/v4/group/8888',
  undefined,
  'your-token'
);
```

### 3. 数据模型 (ToolModels)
```typescript
export interface ToolGridItem {
  id: string;
  name: string;
  icon: string;
  badge?: number;
  webUrl: string;
  type: 'tool' | 'management';
  key: string;
}
```

### 4. 数据服务 (ToolDataService)
```typescript
// 获取工具列表（自动缓存）
const tools = await toolDataService.getTools('8888');

// 强制刷新
const tools = await toolDataService.refreshTools('8888');

// 更新徽章
await toolDataService.updateToolBadge('tool_id', '8888', 5);
```

## API接口说明

### 请求示例
```
GET http://*********:8768/org/company/v4/group/8888
Authorization: Bearer e9deb3c3-982b-43f4-b2bd-42506dd6872e
Content-Type: application/json
```

### 响应格式
```json
{
  "code": 1,
  "msg": "请求成功",
  "data": {
    "toolsList": [...],
    "managementOrgtList": [...]
  }
}
```

## 缓存机制

### 缓存策略
1. **优先缓存**：首次请求后自动缓存到本地数据库
2. **网络降级**：网络失败时自动使用缓存数据
3. **手动刷新**：支持强制刷新忽略缓存
4. **数据同步**：缓存数据与API数据结构一致

### 缓存实体
```typescript
@Entity('TOOL_CACHE')
export class ToolCacheEntity {
  @Id({ autoincrement: true })
  id?: number;
  
  @NotNull()
  toolId: string;
  
  @NotNull()
  name: string;
  
  // ... 其他字段
}
```

## UI展示

### 网格布局
- **企业应用**：4列网格，紧凑布局
- **本地应用**：3列网格，详细信息
- **徽章显示**：支持未读数徽章
- **错误处理**：网络错误时显示友好提示

### 交互功能
- **点击事件**：工具点击处理
- **下拉刷新**：手动刷新数据
- **徽章更新**：点击后自动清除徽章
- **错误重试**：网络错误时提供重试按钮

## 使用方法

### 1. 初始化数据库
```typescript
// 在EntryAbility中
await dataOrmService.initialize(this.context);
```

### 2. 在ViewModel中使用
```typescript
export class WorkbenchViewModel {
  async loadTools(): Promise<void> {
    try {
      const tools = await toolDataService.getTools('8888');
      this.setTools(tools);
    } catch (error) {
      this.setError('加载失败');
    }
  }
}
```

### 3. 在View中展示
```typescript
@ComponentV2
export struct WorkbenchView {
  build() {
    Grid() {
      ForEach(this.viewModel.toolsData, (tool: ToolGridItem) => {
        GridItem() {
          this.ToolGridItemBuilder(tool)
        }
        .onClick(() => {
          this.viewModel.onToolClick(tool);
        })
      })
    }
    .columnsTemplate('1fr 1fr 1fr 1fr')
  }
}
```

## 配置说明

### 网络配置
- **BASE_URL**: API服务器地址
- **TIMEOUT**: 请求超时时间（10秒）
- **DEFAULT_TOKEN**: 默认认证令牌

### 公司ID配置
```typescript
// 在ViewModel中配置
private readonly companyId: string = '8888';
```

### Token配置
```typescript
// 可以从用户登录信息获取
const token = await getUserToken();
const tools = await toolDataService.getTools(companyId, false, token);
```

## 错误处理

### 网络错误类型
```typescript
export enum NetworkErrorType {
  TIMEOUT = 'TIMEOUT',           // 请求超时
  NETWORK_ERROR = 'NETWORK_ERROR', // 网络连接错误
  SERVER_ERROR = 'SERVER_ERROR',   // 服务器错误
  PARSE_ERROR = 'PARSE_ERROR',     // 数据解析错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'  // 未知错误
}
```

### 错误处理策略
1. **自动重试**：网络错误时自动使用缓存
2. **用户提示**：显示友好的错误信息
3. **手动重试**：提供重试按钮
4. **日志记录**：详细的错误日志

## 性能优化

### 缓存优化
- **按需加载**：只缓存当前公司的工具
- **增量更新**：支持单个工具的徽章更新
- **过期清理**：支持手动清除缓存

### 网络优化
- **请求合并**：避免重复请求
- **超时控制**：10秒超时保证响应性
- **错误降级**：网络失败时使用缓存

### UI优化
- **虚拟滚动**：大量数据时的性能优化
- **图片缓存**：工具图标的缓存机制
- **状态管理**：V2状态管理提升渲染性能

## 扩展指南

### 添加新的API接口
1. 在`NetworkConfig`中添加新的路径常量
2. 在`ToolApiService`中添加新的请求方法
3. 在`ToolDataService`中添加缓存逻辑
4. 更新数据模型和实体类

### 自定义错误处理
```typescript
// 自定义错误处理器
class CustomErrorHandler {
  static handle(error: NetworkError): string {
    switch (error.type) {
      case NetworkErrorType.TIMEOUT:
        return '网络超时，请检查网络连接';
      case NetworkErrorType.SERVER_ERROR:
        return `服务器错误(${error.code}): ${error.message}`;
      default:
        return '网络请求失败，请重试';
    }
  }
}
```

### 添加新的缓存策略
```typescript
// 自定义缓存策略
export class CacheStrategy {
  static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟
  
  static shouldRefresh(lastUpdateTime: number): boolean {
    return Date.now() - lastUpdateTime > this.CACHE_DURATION;
  }
}
```

## 总结

该网络请求框架提供了完整的解决方案：

✅ **完整的网络层**：axios封装、错误处理、类型安全
✅ **智能缓存机制**：自动缓存、网络降级、手动刷新
✅ **MVVM架构**：清晰的分层、状态管理、数据绑定
✅ **用户友好的UI**：网格展示、错误提示、交互反馈
✅ **高性能优化**：缓存策略、请求优化、渲染优化

框架已经过完整测试，编译通过，可以直接在生产环境中使用。
