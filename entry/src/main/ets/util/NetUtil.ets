
import wifiManager from '@ohos.wifiManager';
import connection from '@ohos.net.connection'
import { log } from './Log';


/**
 * 网络状态工具
 */
export class NetUtil {

  private static instance: NetUtil;

  private currentNet: connection.NetConnection;

  private constructor() {
    this.currentNet = connection.createNetConnection();
  }

  public static getInstance(): NetUtil {
    if (!NetUtil.instance) {
      NetUtil.instance = new NetUtil();
    }
    return NetUtil.instance;
  }

  // 网络是否连接
  public hasDefaultNet() :boolean {
    return connection.hasDefaultNetSync()
  }

  // 网络状态变化监听
  async initNetWorkListener() {
    this.currentNet.register((error) => {
      if (error) {
        log("订阅失败" + JSON.stringify(error) + "\r\n")
      } else {
        log("订阅成功\r\n")
      }
    })
    this.currentNet.on("netCapabilitiesChange" , (data) => {
      let netType = "无网络"
      if (data.netCap.bearerTypes.includes(connection.NetBearType.BEARER_CELLULAR)) {
        netType = "蜂窝网络"
      } else if (data.netCap.bearerTypes.includes(connection.NetBearType.BEARER_WIFI)) {
        netType = "Wi-Fi网络"
      } else if (data.netCap.bearerTypes.includes(connection.NetBearType.BEARER_ETHERNET)) {
        netType = "以太网网络"
      }
      log(`当前网络状态： ${netType}`)
    })
  }

  public unregisterNet() {
    this.currentNet.unregister(() => {})
  }

  /**
   * 获取当前WiFi信息
   */
  static async getCurrentWifiInfo(): Promise<WifiInfo> {
    try {
      // 检查WiFi是否启用 - 使用正确的API
      const isWifiActive = wifiManager.isWifiActive();

      if (!isWifiActive) {
        log('⚠️ WiFi未启用');
        return {
          bssid: '',
          ssid: '',
          wifiState: 0  // 未获取到
        };
      }

      // 获取当前连接的WiFi信息
      const wifiLinkedInfo = await wifiManager.getLinkedInfo();
      log(`📋 WiFi连接信息: ${JSON.stringify(wifiLinkedInfo)}`);

      if (wifiLinkedInfo) {
        let ip = wifiLinkedInfo.ipAddress
        let ssid = wifiLinkedInfo.ssid
        let bssid = wifiLinkedInfo.bssid
        return {
          bssid: bssid,
          ssid: ssid,
          wifiState: 1  // 未获取到
        };
      }
      log('⚠️ 未连接到有效的WiFi网络');
      return {
        bssid: '',
        ssid: '',
        wifiState: 0  // 未获取到
      };

    } catch (error) {
      log(`❌ 获取WiFi信息异常: ${error}`);
      return {
        bssid: '',
        ssid: '',
        wifiState: 0  // 未获取到
      };
    }
  }
}

// 定义WiFi信息接口
export interface WifiInfo {
  bssid: string;        // WiFi设备地址
  ssid: string;         // WiFi名称
  wifiState: number;    // WiFi状态：1-获取到wifi信息，0-未获取到，-1-无权限
}