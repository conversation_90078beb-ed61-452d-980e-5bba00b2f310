# 业务页面 WebView JavaScript 调用调试指南

## 问题现状

✅ **测试页面正常**：在 `WebViewTestPage.ets` 中能成功调用 `client_isAlready`
❓ **业务页面异常**：实际业务页面中 `client_isAlready` 没有被调用

## 可能的原因分析

### 1. 页面加载时机差异

**测试页面**：
- 页面简单，加载快速
- JavaScript立即执行
- 原生对象注入及时

**业务页面**：
- 页面复杂，加载较慢
- 可能有异步资源加载
- JavaScript执行时原生对象可能还未注入

### 2. JavaScript调用方式差异

**测试页面**：
```javascript
// 有多重保护机制
window.addEventListener('nativeReady', function() {
  window.DDBESOFFICE.client_isAlready(JSON.stringify(params));
});

// 定时检查
setInterval(() => {
  if (checkNativeObject()) {
    // 调用方法
  }
}, 1000);
```

**业务页面**：
```javascript
// 可能直接调用，没有检查机制
window.DDBESOFFICE.client_isAlready(data);
```

### 3. 页面内容和结构差异

- 业务页面可能有复杂的框架（React、Vue等）
- 可能有其他JavaScript库冲突
- 可能有异步加载的内容

## 调试步骤

### 第一步：查看增强日志

现在 `WebViewPage.ets` 已经增加了详细的调试日志，运行业务页面时查看：

```
🌐 WebView 即将加载URL: [业务页面URL]
🚀 WebView页面开始加载: [业务页面URL]
✅ WebView页面加载完成: [业务页面URL]
🧪 是否为测试页面: false
🔍 开始调试业务页面: [业务页面URL]
🔍 业务页面调试结果: BUSINESS_DEBUG_CALL_SUCCESS 或 BUSINESS_DEBUG_CALL_FAILED
```

### 第二步：强制调用测试

系统会自动在业务页面中执行强制调用测试，查看是否出现：

```
🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉
📨 接收到的消息: {"title":"业务页面调试测试",...}
```

### 第三步：分析页面内容

调试脚本会检查：
- 页面是否包含 `client_isAlready` 关键字
- 页面是否包含 `DDBESOFFICE` 关键字
- 页面的脚本数量和结构

## 解决方案

### 方案1：修改业务页面JavaScript（推荐）

在业务页面中添加安全调用机制：

```javascript
// 安全的调用方式
function callNativeMethodSafely(methodName, params) {
  // 检查对象是否存在
  if (typeof window.DDBESOFFICE === 'undefined') {
    console.log('等待原生对象注入...');
    
    // 延迟重试
    setTimeout(() => {
      callNativeMethodSafely(methodName, params);
    }, 500);
    return;
  }
  
  // 检查方法是否存在
  if (typeof window.DDBESOFFICE[methodName] !== 'function') {
    console.error(`方法 ${methodName} 不存在`);
    return;
  }
  
  // 安全调用
  try {
    console.log(`调用原生方法: ${methodName}`);
    window.DDBESOFFICE[methodName](JSON.stringify(params));
  } catch (error) {
    console.error(`调用 ${methodName} 失败:`, error);
  }
}

// 页面加载完成后调用
document.addEventListener('DOMContentLoaded', function() {
  // 延迟调用，确保原生对象已注入
  setTimeout(() => {
    callNativeMethodSafely('client_isAlready', {
      title: '业务页面标题',
      subTitle: '业务页面副标题',
      objName: 'DDBESOFFICE',
      useTitleBar: true
    });
  }, 1000);
});

// 监听原生对象准备就绪事件
window.addEventListener('nativeReady', function(event) {
  console.log('收到原生对象准备就绪事件');
  callNativeMethodSafely('client_isAlready', {
    title: '业务页面标题',
    subTitle: '业务页面副标题', 
    objName: 'DDBESOFFICE',
    useTitleBar: true
  });
});
```

### 方案2：增加原生端延迟

在 `WebViewPage.ets` 中增加更长的延迟：

```typescript
.onPageEnd((e) => {
  // 对于复杂的业务页面，增加更长的延迟
  const isBusinessPage = !e?.url?.includes('test_webview.html');
  const delay = isBusinessPage ? 3000 : 1000; // 业务页面延迟3秒
  
  setTimeout(() => {
    this.checkAndInjectJavaScript();
  }, delay);
})
```

### 方案3：多次重试机制

```typescript
private retryInjectJavaScript(retryCount: number = 0) {
  if (retryCount >= 5) {
    log('JavaScript对象注入重试次数已达上限');
    return;
  }
  
  setTimeout(() => {
    this.checkAndInjectJavaScript();
    
    // 如果仍然失败，继续重试
    this.retryInjectJavaScript(retryCount + 1);
  }, 1000 * (retryCount + 1)); // 递增延迟
}
```

## 验证步骤

1. **运行业务页面**
2. **查看DevEco Studio日志**，确认：
   - 页面加载日志
   - 对象检查日志
   - 强制调用测试结果
3. **查看是否出现方法调用日志**：
   ```
   🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉
   ```

## 常见问题

### Q1: 强制调用测试成功，但页面自身调用失败
**A**: 说明原生对象正常，问题在页面的JavaScript调用时机或方式

### Q2: 强制调用测试也失败
**A**: 说明原生对象注入有问题，需要检查WebView配置或增加延迟

### Q3: 页面内容检查显示没有相关代码
**A**: 可能页面使用了动态加载或框架，需要检查实际的JavaScript执行

## 下一步行动

1. **运行业务页面**，查看新增的调试日志
2. **根据日志结果**确定具体问题
3. **选择合适的解决方案**进行修复
4. **验证修复效果**

通过这个增强的调试机制，我们应该能够快速定位业务页面中JavaScript调用失败的具体原因。
