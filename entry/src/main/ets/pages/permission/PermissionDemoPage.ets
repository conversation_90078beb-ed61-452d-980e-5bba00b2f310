import { CommonToolbar, ToolbarPresets } from "../../components/CommonToolbar";
import { permissionManager } from "../../util/PermissionManager";
import { log } from "../../util/Log";
import { promptAction } from '@kit.ArkUI';
import { common } from '@kit.AbilityKit';


@Entry
@ComponentV2
export struct PermissionDemoPage {
  @Local permissionStatus: Map<string, boolean> = new Map();
  @Local isLoading: boolean = false;
  @Local capturedImageUri: string = '';
  @Local selectedImageUri: string = '';

  aboutToAppear(): void {
    this.checkAllPermissions();
  }

  // 检查所有权限状态
  async checkAllPermissions() {
    this.isLoading = true;
    try {
      const cameraGranted = await permissionManager.checkCameraPermission();
      const mediaPermissions = await permissionManager.checkMediaPermissions();
      const microphoneGranted = await permissionManager.checkMicrophonePermission();
      const locationGranted = await permissionManager.checkLocationPermission();

      this.permissionStatus.set('camera', cameraGranted);
      this.permissionStatus.set('microphone', microphoneGranted);
      this.permissionStatus.set('location', locationGranted);
      
      mediaPermissions.forEach((granted, permission) => {
        this.permissionStatus.set(permission, granted);
      });

      log(`权限状态检查完成: ${JSON.stringify(Array.from(this.permissionStatus.entries()))}`);
    } catch (error) {
      log(`检查权限状态失败: ${error}`);
    } finally {
      this.isLoading = false;
    }
  }

  // 申请相机权限
  async requestCameraPermission() {
    this.isLoading = true;
    try {
      const context = getContext(this) as common.UIAbilityContext;
      const granted = await permissionManager.requestCameraPermission(context);
      this.permissionStatus.set('camera', granted);

      if (granted) {
        promptAction.showToast({ message: '相机权限申请成功' });
      } else {
        promptAction.showToast({ message: '相机权限申请失败' });
      }
    } catch (error) {
      log(`申请相机权限失败: ${error}`);
      promptAction.showToast({ message: '申请相机权限出错' });
    } finally {
      this.isLoading = false;
    }
  }

  // 申请媒体权限
  async requestMediaPermissions() {
    this.isLoading = true;
    try {
      const context = getContext(this) as common.UIAbilityContext;
      const results = await permissionManager.requestMediaPermissions(context);

      results.forEach((granted, permission) => {
        this.permissionStatus.set(permission, granted);
      });

      const allGranted = Array.from(results.values()).every(granted => granted);
      if (allGranted) {
        promptAction.showToast({ message: '媒体权限申请成功' });
      } else {
        promptAction.showToast({ message: '部分媒体权限申请失败' });
      }
    } catch (error) {
      log(`申请媒体权限失败: ${error}`);
      promptAction.showToast({ message: '申请媒体权限出错' });
    } finally {
      this.isLoading = false;
    }
  }

  // 拍照功能
  async takePhoto() {
    try {
      // 检查相机权限
      const hasPermission = await permissionManager.checkCameraPermission();
      if (!hasPermission) {
        const context = getContext(this) as common.UIAbilityContext;
        const granted = await permissionManager.requestCameraPermission(context);
        if (!granted) {
          promptAction.showToast({ message: '需要相机权限才能拍照' });
          return;
        }
      }

      // TODO: 实现相机拍照功能
      // 这里需要使用CameraKit的相关API
      promptAction.showToast({ message: '拍照功能开发中...' });
      log('开始拍照');

    } catch (error) {
      log(`拍照失败: ${error}`);
      promptAction.showToast({ message: '拍照失败' });
    }
  }

  // 选择相册图片
  async selectFromGallery() {
    try {
      // 检查媒体权限
      const mediaPermissions = await permissionManager.checkMediaPermissions();
      const hasReadPermission = mediaPermissions.get('ohos.permission.READ_IMAGEVIDEO') || false;

      if (!hasReadPermission) {
        const context = getContext(this) as common.UIAbilityContext;
        const results = await permissionManager.requestMediaPermissions(context);
        const granted = results.get('ohos.permission.READ_IMAGEVIDEO') || false;
        if (!granted) {
          promptAction.showToast({ message: '需要读取图片权限才能选择相册' });
          return;
        }
      }

      // TODO: 实现相册选择功能
      // 这里需要使用PhotoAccessHelper的相关API
      promptAction.showToast({ message: '相册选择功能开发中...' });
      log('开始选择相册图片');

    } catch (error) {
      log(`选择相册图片失败: ${error}`);
      promptAction.showToast({ message: '选择相册图片失败' });
    }
  }

  // 权限状态组件
  @Builder
  PermissionStatusItem(title: string, permission: string) {
    Row() {
      Text(title)
        .fontSize(16)
        .layoutWeight(1)
      
      Text(this.permissionStatus.get(permission) ? '已授权' : '未授权')
        .fontSize(14)
        .fontColor(this.permissionStatus.get(permission) ? '#07C160' : '#FF6B6B')
        .margin({ right: 8 })
      
      if (this.permissionStatus.get(permission)) {
        Image('/resources/base/media/im_newsHome_search.png')
          .width(20)
          .height(20)
          .fillColor('#07C160')
      } else {
        Image('/resources/base/media/im_newsHome_search.png')
          .width(20)
          .height(20)
          .fillColor('#FF6B6B')
      }
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
    .borderRadius(8)
    .margin({ bottom: 8 })
  }

  build() {
    Column() {
      CommonToolbar({ config: ToolbarPresets.simple('权限使用示例') })
      
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
          Text('检查权限中...')
            .fontSize(14)
            .margin({ top: 8 })
        }
        .justifyContent(FlexAlign.Center)
        .layoutWeight(1)
      } else {
        Scroll() {
          Column() {
            // 权限状态展示
            Text('权限状态')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ bottom: 16 })
              .alignSelf(ItemAlign.Start)

            this.PermissionStatusItem('相机权限', 'camera')
            this.PermissionStatusItem('麦克风权限', 'microphone')
            this.PermissionStatusItem('位置权限', 'location')
            this.PermissionStatusItem('读取媒体文件', 'ohos.permission.READ_MEDIA')
            this.PermissionStatusItem('读取图片视频', 'ohos.permission.READ_IMAGEVIDEO')

            // 权限申请按钮
            Text('权限申请')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 24, bottom: 16 })
              .alignSelf(ItemAlign.Start)

            Button('申请相机权限')
              .width('100%')
              .height(44)
              .backgroundColor('#1976D2')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.requestCameraPermission())

            Button('申请媒体权限')
              .width('100%')
              .height(44)
              .backgroundColor('#1976D2')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.requestMediaPermissions())

            // 功能演示
            Text('功能演示')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 24, bottom: 16 })
              .alignSelf(ItemAlign.Start)

            Button('拍照')
              .width('100%')
              .height(44)
              .backgroundColor('#07C160')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.takePhoto())

            Button('选择相册图片')
              .width('100%')
              .height(44)
              .backgroundColor('#07C160')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.selectFromGallery())

            Button('刷新权限状态')
              .width('100%')
              .height(44)
              .backgroundColor('#FF9500')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.checkAllPermissions())
          }
          .padding(16)
        }
        .layoutWeight(1)
      }
    }
    .backgroundColor('#F5F6FA')
    .width('100%')
    .height('100%')
  }
}
