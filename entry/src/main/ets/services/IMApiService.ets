import { HttpClient, httpClient } from '../network/HttpClient';
import { NetworkConfig, ApiResponse } from '../network/NetworkConfig';
import { CompanyToolsData, ToolGridItem, ToolDataConverter } from '../models/ToolModels';
import { APIConfig } from '../network/API';
import { IMTokenResp, SocketLinkResp } from '../models/IMModels';

/**
 * 工具API服务类
 */
export class IMApiService {
  private static instance: IMApiService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): IMApiService {
    if (!IMApiService.instance) {
      IMApiService.instance = new IMApiService();
    }
    return IMApiService.instance;
  }

  /**
   * 获取公司工具列表
   * @param token 访问令牌（可选，默认使用配置中的token）
   * @returns Promise<ToolGridItem[]>
   */
  public async getSocketUrl(token?: string): Promise<SocketLinkResp> {
    try {

      const url = `${APIConfig.SOCKET_LINK}`;
      const response: ApiResponse<SocketLinkResp> = await HttpClient.getInstance(APIConfig.ROUTER_HOST).get(
        url,
        undefined,
        token
      );

      return response.data;
    } catch (error) {
      console.error('getSocketUrl: 失败', error);
      throw new Error(`getSocketUrl: ${error}`);
    }
  }

  public async getImToken(token?: string): Promise<IMTokenResp> {
    try {

      const url = `${APIConfig.FETCH_IM_TOKEN}`;
      const response: ApiResponse<IMTokenResp> = await HttpClient.getInstance(APIConfig.ROUTER_HOST).get(
        url,
        undefined,
        token
      );

      return response.data;
    } catch (error) {
      console.error('getImToken: 失败', error);
      throw new Error(`getImToken: ${error}`);
    }
  }


}

// 导出单例实例
export const imApiService = IMApiService.getInstance();
