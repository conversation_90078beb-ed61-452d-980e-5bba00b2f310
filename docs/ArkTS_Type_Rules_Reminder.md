# ArkTS 类型规范提醒

## 🚨 **严格禁止的类型**

### ❌ **绝对不能使用**
```typescript
// 这些都会导致编译错误
const data = JSON.parse(result) as any;           // ❌ 禁止any
const result: unknown = someFunction();           // ❌ 禁止unknown
function process(param: any) { }                  // ❌ 禁止any参数
const obj = { data: someValue as any };          // ❌ 禁止any断言
```

## ✅ **正确的类型声明方式**

### 1. **JSON.parse 必须明确类型**
```typescript
// ❌ 错误方式
const data = JSON.parse(result) as any;

// ✅ 正确方式
interface ExpectedResult {
  checkCount: number;
  timestamp: string;
  windowKeys: string[];
}
const data = JSON.parse(result) as ExpectedResult;
```

### 2. **函数参数必须明确类型**
```typescript
// ❌ 错误方式
function process(data: any) { }

// ✅ 正确方式
interface ProcessData {
  id: string;
  value: number;
}
function process(data: ProcessData): void { }
```

### 3. **对象字面量必须有接口**
```typescript
// ❌ 错误方式
const config = {
  name: 'test',
  settings: { enabled: true }  // 嵌套对象未类型化
};

// ✅ 正确方式
interface Settings {
  enabled: boolean;
}
interface Config {
  name: string;
  settings: Settings;
}
const settings: Settings = { enabled: true };
const config: Config = { name: 'test', settings: settings };
```

### 4. **数组必须明确元素类型**
```typescript
// ❌ 错误方式
const items = [];  // 推断为any[]

// ✅ 正确方式
const items: string[] = [];
const complexItems: Array<MyInterface> = [];
```

## 📋 **常见场景的正确写法**

### 场景1: 处理API响应
```typescript
// 定义响应接口
interface ApiResponse {
  success: boolean;
  data: ResponseData;
  message: string;
}

interface ResponseData {
  id: string;
  name: string;
  items: string[];
}

// 使用
const response = JSON.parse(apiResult) as ApiResponse;
```

### 场景2: 事件处理
```typescript
// 定义事件数据接口
interface CustomEventData {
  type: string;
  payload: EventPayload;
}

interface EventPayload {
  userId: string;
  action: string;
}

// 使用
function handleEvent(eventData: CustomEventData): void {
  // 处理逻辑
}
```

### 场景3: 配置对象
```typescript
// 定义配置接口
interface WebViewConfig {
  url: string;
  title: string;
  options: WebViewOptions;
}

interface WebViewOptions {
  enableJavaScript: boolean;
  allowFileAccess: boolean;
}

// 使用
const options: WebViewOptions = {
  enableJavaScript: true,
  allowFileAccess: true
};
const config: WebViewConfig = {
  url: 'https://example.com',
  title: 'Test Page',
  options: options
};
```

## 🔧 **修复策略**

### 步骤1: 识别需要类型的地方
- JSON.parse() 调用
- 函数参数
- 对象字面量
- 数组声明

### 步骤2: 定义接口
```typescript
// 为每个数据结构定义接口
interface DataStructure {
  // 明确每个属性的类型
  property1: string;
  property2: number;
  property3: boolean;
  property4: SubInterface;
}

interface SubInterface {
  subProperty: string;
}
```

### 步骤3: 应用类型
```typescript
// 使用定义的接口
const data = JSON.parse(jsonString) as DataStructure;
function processData(input: DataStructure): void {
  // 处理逻辑
}
```

## 🎯 **检查清单**

在编写代码时，确保：

- [ ] 没有使用 `any` 类型
- [ ] 没有使用 `unknown` 类型  
- [ ] 所有 JSON.parse() 都有明确的类型断言
- [ ] 所有函数参数都有类型声明
- [ ] 所有函数都有返回值类型声明
- [ ] 所有对象字面量都对应明确的接口
- [ ] 所有数组都有明确的元素类型

## 🚨 **记住这个原则**

**在ArkTS中，每个值都必须有明确的类型！**

- 如果不确定类型，先定义接口
- 如果数据结构复杂，拆分成多个接口
- 如果是临时数据，也要定义临时接口
- 宁可多写几行接口定义，也不要使用any

这样可以：
1. 避免编译错误
2. 提高代码质量
3. 获得更好的IDE支持
4. 减少运行时错误
