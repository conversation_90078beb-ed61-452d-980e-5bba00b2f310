import { Session } from "../../data_source/SessionBody";
import { getAllSessions } from "../../data_source/SessionDataSource";
import { dataOrmService } from "../../database/DataOrmService";
import { log } from "../../util/Log";

/**
 * SessionViewModel - 会话页面的视图模型
 * 负责管理会话数据状态和业务逻辑
 */
@ObservedV2
export class SessionViewModel {
  // 数据状态
  private _totalUnreadCount: number = 0;


  @Trace currentMessageTabIndex: number = 0;
  @Trace sessions: Session[] = [];
  @Trace isLoading: boolean = true;

  // 回调函数
  private _onUnreadCountChanged?: (newCount: number) => void;

  // 设置回调函数
  setOnUnreadCountChanged(callback: (newCount: number) => void): void {
    this._onUnreadCountChanged = callback;
  }

  setSessions(value: Session[]): void {
    this.sessions = value;
    this.calculateTotalUnreadCount();
  }

  setIsLoading(value: boolean): void {
    this.isLoading = value;
  }

  // 初始化数据
  async initialize(): Promise<void> {
    await this.loadSessionData();
  }

  // 加载会话数据
  private async loadSessionData(): Promise<void> {
    try {
      this.setIsLoading(true);
      log('SessionViewModel: 开始加载会话数据');

      // 添加延迟，确保数据库完全初始化
      await new Promise<void>(resolve => setTimeout(resolve, 500));

      const loadedSessions = await getAllSessions();
      log(`SessionViewModel: 数据库加载会话数据成功，数量: ${loadedSessions.length}`);
      log(`SessionViewModel: 会话数据: ${JSON.stringify(loadedSessions)}`);

      this.setSessions([...loadedSessions]);

    } catch (error) {
      log(`SessionViewModel: 加载会话数据失败: ${error}`);
      // 如果数据库加载失败，使用空数组
      this.setSessions([]);
    } finally {
      this.setIsLoading(false);
    }
  }

  // 计算总未读数
  private calculateTotalUnreadCount(): void {
    const newTotal = this.sessions.reduce((total, session) => total + (session.unread ?? 0), 0);
    if (this._totalUnreadCount !== newTotal) {
      this._totalUnreadCount = newTotal;
      this._onUnreadCountChanged?.(newTotal);
      log(`SessionViewModel: 总未读数更新为: ${newTotal}`);
    }
  }

  // 更新Session的未读数
  async updateSessionUnread(sessionName: string, newUnread: number): Promise<void> {
    try {
      log(`SessionViewModel: 更新会话 ${sessionName} 的未读数为 ${newUnread}`);

      // 1. 更新数据库
      await dataOrmService.updateSessionUnread(sessionName, newUnread);

      // 2. 更新本地状态
      this.updateSessionInState(sessionName, (session) => {
        return {
          name: session.name,
          avatar: session.avatar,
          message: session.message,
          time: session.time,
          unread: newUnread,
          tag: session.tag,
          tagColor: session.tagColor,
          dot: session.dot,
          sessionType: session.sessionType,
          isTop: session.isTop
        };
      });

      log(`SessionViewModel: 会话 ${sessionName} 未读数更新成功`);
    } catch (error) {
      log(`SessionViewModel: 更新会话 ${sessionName} 未读数失败: ${error}`);

    }
  }

  // 更新Session的置顶状态
  async updateSessionTop(sessionName: string, isTop: boolean): Promise<void> {
    try {
      log(`SessionViewModel: 更新会话 ${sessionName} 的置顶状态为 ${isTop}`);

      // 更新数据库中的置顶状态
      await dataOrmService.setSessionTop(sessionName, isTop);

      // 更新本地状态
      this.updateSessionInState(sessionName, (session) => {
        return {
          name: session.name,
          avatar: session.avatar,
          message: session.message,
          time: session.time,
          unread: session.unread,
          tag: session.tag,
          tagColor: session.tagColor,
          dot: session.dot,
          sessionType: session.sessionType,
          isTop: isTop
        };
      });

      log(`SessionViewModel: 会话 ${sessionName} 置顶状态更新成功`);
    } catch (error) {
      log(`SessionViewModel: 更新会话 ${sessionName} 置顶状态失败: ${error}`);
      // throw error;
    }
  }

  // 通用的Session更新方法
  private updateSessionInState(sessionName: string, updateFn: (session: Session) => Session): void {
    const updatedSessions = this.sessions.map(session =>
      session.name === sessionName ? updateFn(session) : session
    );
    this.setSessions(updatedSessions);
  }

  // 处理Session点击事件
  async handleSessionClick(session: Session): Promise<void> {
    log(`SessionViewModel: 点击了会话 ${session.name}，当前未读数: ${session.unread ?? 0}`);

    // 如果有未读消息，减一
    if ((session.unread ?? 0) > 0) {
      const newUnread = (session.unread ?? 0) - 1;
      await this.updateSessionUnread(session.name, newUnread);
    }
  }

  // 处理Session长按事件（用于置顶/取消置顶）
  async handleSessionLongPress(session: Session): Promise<boolean> {
    log(`SessionViewModel: 长按了会话 ${session.name}，当前置顶状态: ${session.isTop ?? false}`);

    const newTopStatus = !(session.isTop ?? false);
    await this.updateSessionTop(session.name, newTopStatus);
    
    return newTopStatus;
  }

  // 过滤会话列表
  filterSessions(msgType: number): Session[] {
    log(`SessionViewModel: filterSessions - msgType: ${msgType}, 总数据量: ${this.sessions.length}`);

    let filteredData: Session[] = [];
    switch (msgType) {
      case 0: // 消息
        filteredData = this.sessions;
        break;
      case 1: // 未读
        filteredData = this.sessions.filter((x) => (x.unread ?? 0) > 0);
        break;
      case 2: // 单聊
        filteredData = this.sessions.filter((x) => x.sessionType === 1);
        break;
      case 3: // 群聊
        filteredData = this.sessions.filter((x) => x.sessionType === 2);
        break;
      default:
        filteredData = this.sessions;
        break;
    }

    log(`SessionViewModel: filterSessions - 过滤后数据量: ${filteredData.length}`);
    return filteredData;
  }

  // 获取置顶会话列表
  getTopSessions(): Session[] {
    return this.sessions.filter((s) => s.isTop === true);
  }

  // 刷新数据
  async refreshData(): Promise<void> {
    await this.loadSessionData();
  }

  // 清理资源
  dispose(): void {
    this._onUnreadCountChanged = undefined;
  }
} 