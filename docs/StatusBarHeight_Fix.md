# 状态栏高度获取方法修复

## 🚨 **修复的编译错误**

### 1. **类型声明问题**
```typescript
// ❌ 错误：缺少明确类型声明
const mainWindow = await window.getLastWindow(getContext());
const avoidArea = await mainWindow.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);

// ✅ 修复：添加明确类型声明
const mainWindow: window.Window = await window.getLastWindow(getContext());
const avoidArea: window.AvoidArea = await mainWindow.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
const statusBarHeight: number = avoidArea.topRect.height;
```

### 2. **同步方法问题**
```typescript
// ❌ 错误：getLastWindow是异步的，无法在同步方法中使用
static getStatusBarHeight(): number {
  const mainWindow = window.getLastWindow(getContext()); // 返回Promise
  // ...
}

// ✅ 修复：同步方法只返回默认值，建议使用异步方法
static getStatusBarHeight(): number {
  console.warn('同步方法无法获取真实状态栏高度，返回默认值。建议使用getStatusBarHeightAsync()');
  return 44;
}
```

## 🔧 **修复后的实现**

### 1. **同步方法（简化版）**
```typescript
static getStatusBarHeight(): number {
  try {
    // 由于getLastWindow是异步的，同步方法只能返回默认值
    console.warn('同步方法无法获取真实状态栏高度，返回默认值。建议使用getStatusBarHeightAsync()');
    return 44; // 默认状态栏高度
  } catch (error) {
    console.error(`获取状态栏高度失败: ${error}`);
    return 44;
  }
}
```

### 2. **异步方法（完整实现）**
```typescript
static async getStatusBarHeightAsync(): Promise<number> {
  try {
    // 明确类型声明
    const mainWindow: window.Window = await window.getLastWindow(getContext());
    
    if (mainWindow) {
      // 明确类型声明
      const avoidArea: window.AvoidArea = await mainWindow.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
      
      if (avoidArea && avoidArea.topRect) {
        const statusBarHeight: number = avoidArea.topRect.height;
        console.info(`异步获取状态栏高度成功: ${statusBarHeight}px`);
        return statusBarHeight;
      }
    }
    
    return 44; // 默认值
  } catch (error) {
    console.error(`异步获取状态栏高度失败: ${error}`);
    return 44;
  }
}
```

## 📋 **ArkTS类型要求**

### 1. **明确类型声明**
```typescript
// ✅ 正确：所有变量都有明确类型
const mainWindow: window.Window = await window.getLastWindow(getContext());
const avoidArea: window.AvoidArea = await mainWindow.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
const statusBarHeight: number = avoidArea.topRect.height;
```

### 2. **避免any/unknown类型**
```typescript
// ❌ 错误：会被推断为any类型
const result = await someAsyncFunction();

// ✅ 正确：明确指定类型
const result: ExpectedType = await someAsyncFunction();
```

### 3. **异步方法的正确使用**
```typescript
// ❌ 错误：在同步方法中使用异步API
static syncMethod(): number {
  const result = await asyncAPI(); // 编译错误
  return result;
}

// ✅ 正确：异步方法处理异步API
static async asyncMethod(): Promise<number> {
  const result: number = await asyncAPI();
  return result;
}
```

## 🎯 **使用建议**

### 1. **优先使用异步方法**
```typescript
// ✅ 推荐：获取真实状态栏高度
const statusBarHeight = await DeviceUtils.getStatusBarHeightAsync();

// 在WebView中使用
const clientInfo: ClientInfo = {
  statusHeight: await DeviceUtils.getStatusBarHeightAsync(),
  company: companyInfo,
  user: userInfo,
  token: "Bearer xxx"
};
```

### 2. **同步方法的使用场景**
```typescript
// ✅ 适用：需要立即返回值的场景
const defaultHeight = DeviceUtils.getStatusBarHeight(); // 返回44

// 适用于UI组件中需要同步值的情况
.content {
  margin-top: ${DeviceUtils.getStatusBarHeight()}px; // 使用默认值
}
```

### 3. **在组件中的使用**
```typescript
@Component
struct MyComponent {
  @State statusBarHeight: number = 44;

  async aboutToAppear() {
    // 异步获取真实高度
    this.statusBarHeight = await DeviceUtils.getStatusBarHeightAsync();
  }

  build() {
    Column() {
      // 使用真实的状态栏高度
    }
    .margin({ top: this.statusBarHeight })
  }
}
```

## ✅ **修复验证**

### 编译检查
- ✅ 没有类型错误
- ✅ 没有any/unknown类型警告
- ✅ 所有API调用正确
- ✅ 异步方法正确实现

### 功能测试
```typescript
// 测试异步方法
DeviceUtils.getStatusBarHeightAsync().then(height => {
  console.log(`真实状态栏高度: ${height}px`);
});

// 测试同步方法
const defaultHeight = DeviceUtils.getStatusBarHeight();
console.log(`默认状态栏高度: ${defaultHeight}px`);
```

## 🚀 **最佳实践**

### 1. **在应用初始化时获取**
```typescript
// 在EntryAbility中
async onCreate() {
  const statusBarHeight = await DeviceUtils.getStatusBarHeightAsync();
  AppStorage.setOrCreate('statusBarHeight', statusBarHeight);
}

// 在组件中使用
@StorageLink('statusBarHeight') statusBarHeight: number = 44;
```

### 2. **缓存结果避免重复调用**
```typescript
class StatusBarCache {
  private static cachedHeight: number | null = null;
  
  static async getHeight(): Promise<number> {
    if (this.cachedHeight === null) {
      this.cachedHeight = await DeviceUtils.getStatusBarHeightAsync();
    }
    return this.cachedHeight;
  }
}
```

### 3. **错误处理**
```typescript
try {
  const height = await DeviceUtils.getStatusBarHeightAsync();
  // 使用真实高度
} catch (error) {
  console.error('获取状态栏高度失败，使用默认值');
  const height = 44; // 使用默认值
}
```

## 📝 **总结**

修复后的实现：
1. ✅ **类型安全**: 所有变量都有明确类型声明
2. ✅ **API正确**: 正确使用HarmonyOS的窗口API
3. ✅ **异步处理**: 异步方法获取真实高度
4. ✅ **降级机制**: 同步方法提供默认值
5. ✅ **错误处理**: 完善的异常捕获

现在可以安全地使用这两个方法来获取状态栏高度了！
