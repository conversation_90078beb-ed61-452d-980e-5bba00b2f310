# ArkTS 语法修复说明

## 修复的语法问题

### 1. 禁止使用 `any` 和 `unknown` 类型

**问题**: ArkTS 不允许使用 `any` 和 `unknown` 类型
**错误**: `Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)`

#### 修复前:
```typescript
private sendResponseToJS(methodName: string, response: any) {
  // ...
}
```

#### 修复后:
```typescript
// 定义明确的接口类型
interface ResponseData {
  success: boolean;
  message: string;
  data?: Record<string, Object>;
}

interface UserInfo {
  userId: string;
  userName: string;
  avatar: string;
}

interface TokenInfo {
  token: string;
  expireTime: number;
}

private sendResponseToJS(methodName: string, response: ResponseData): void {
  // ...
}
```

### 2. 函数参数和返回值类型声明

**问题**: 函数缺少明确的类型声明

#### 修复前:
```typescript
export function callJS(controller: web_webview.WebviewController , obj:string ,method: string) {
  controller.runJavaScript(`javascript:window.${obj}.${method}()`);
}
```

#### 修复后:
```typescript
export function callJS(controller: web_webview.WebviewController, obj: string, method: string): void {
  controller.runJavaScript(`javascript:window.${obj}.${method}()`);
}
```

### 3. 数组类型声明

**问题**: 数组缺少明确的类型声明

#### 修复前:
```typescript
export let JSMethodArray = [
  "client_isAlready",
  "client_getToken",
  // ...
]
```

#### 修复后:
```typescript
export const JSMethodArray: string[] = [
  "client_isAlready",
  "client_getToken",
  "client_getUserInfo",
  "client_goBack",
  "client_popBack",
];
```

### 4. 错误处理中的类型声明

**问题**: catch 块中的 error 参数缺少类型声明

#### 修复前:
```typescript
.catch((error) => {
  log(`JavaScript对象检查失败: ${error}`);
});
```

#### 修复后:
```typescript
.catch((error: Error) => {
  log(`JavaScript对象检查失败: ${error.message}`);
});
```

### 5. 对象字面量的类型安全

**问题**: 直接使用对象字面量作为参数
**错误**: `Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)`

#### 修复前:
```typescript
this.sendResponseToJS('client_isAlready', { success: true, message: '数据接收成功' });

// 嵌套对象字面量问题
const response: ResponseData = {
  success: true,
  message: 'Token获取成功',
  data: { token: tokenData.token, expireTime: tokenData.expireTime } // 错误：未类型化的对象字面量
};
```

#### 修复后:
```typescript
const successResponse: ResponseData = { success: true, message: '数据接收成功' };
this.sendResponseToJS('client_isAlready', successResponse);

// 为嵌套对象定义明确类型
const responseData: TokenData = {
  token: 'your_token_here',
  expireTime: Date.now() + 3600000
};

const response: ResponseData = {
  success: true,
  message: 'Token获取成功',
  data: responseData
};

// 复杂嵌套对象的类型定义
const userInfo: ClientUserInfo = {
  userId: 'harmony_user_123',
  userName: 'HarmonyOS用户',
  avatar: 'https://example.com/avatar.jpg'
};

const companyInfo: ClientCompanyInfo = {
  companyId: 'harmony_company_456',
  companyName: 'HarmonyOS公司',
  orgId: 'harmony_org_789'
};

const clientInfo: ClientInfo = {
  token: 'harmony_test_token_' + Date.now(),
  user: userInfo,
  company: companyInfo,
  statusHeight: 44
};
```

## 完整的类型定义

### TokenData 接口
```typescript
interface TokenData {
  token: string;
  expireTime: number;
}
```

### UserData 接口
```typescript
interface UserData {
  userId: string;
  userName: string;
  avatar: string;
}
```

### ClientInfo 相关接口
```typescript
interface ClientUserInfo {
  userId: string;
  userName: string;
  avatar: string;
}

interface ClientCompanyInfo {
  companyId: string;
  companyName: string;
  orgId: string;
}

interface ClientInfo {
  token: string;
  user: ClientUserInfo;
  company: ClientCompanyInfo;
  statusHeight: number;
}
```

### ResponseData 接口
```typescript
interface ResponseData {
  success: boolean;
  message: string;
  data?: TokenData | UserData;
}
```

## 修复后的方法示例

### client_isAlready 方法
```typescript
public client_isAlready(message: string): void {
  log(`=== client_isAlready 方法被调用 ===`);
  log(`接收到的消息: ${message}`);
  
  try {
    let o = JSON.parse(message) as ClientIsAlreadyBody;
    log(`解析后的数据: ${JSON.stringify(o)}`);
    
    if (o) {
      // 处理数据...
      
      const successResponse: ResponseData = { success: true, message: '数据接收成功' };
      this.sendResponseToJS('client_isAlready', successResponse);
    }
  } catch (error) {
    log(`client_isAlready 解析消息失败: ${error}`);
    const errorResponse: ResponseData = { success: false, message: '数据解析失败' };
    this.sendResponseToJS('client_isAlready', errorResponse);
  }
}
```

### client_getToken 方法
```typescript
public client_getToken(message: string): void {
  log(`=== client_getToken 方法被调用 ===`);
  log(`接收到的消息: ${message}`);
  
  const tokenData: TokenInfo = {
    token: 'your_token_here',
    expireTime: Date.now() + 3600000
  };
  
  const response: ResponseData = { 
    success: true, 
    message: 'Token获取成功',
    data: { token: tokenData.token, expireTime: tokenData.expireTime }
  };
  this.sendResponseToJS('client_getToken', response);
}
```

### sendResponseToJS 方法
```typescript
private sendResponseToJS(methodName: string, response: ResponseData): void {
  try {
    if (this.controller) {
      const script = `
        (function() {
          // 回调处理...
          console.log('${methodName} 响应已发送:', ${JSON.stringify(response)});
        })();
      `;
      
      this.controller.runJavaScript(script)
        .then(() => {
          log(`${methodName} 响应发送成功`);
        })
        .catch((error: Error) => {
          log(`${methodName} 响应发送失败: ${error.message}`);
        });
    }
  } catch (error) {
    log(`发送响应到JavaScript失败: ${error}`);
  }
}
```

## ArkTS 语法规范要点

1. **明确类型声明**: 所有变量、参数、返回值都必须有明确的类型声明
2. **禁用 any/unknown**: 不能使用 `any` 或 `unknown` 类型
3. **接口定义**: 使用接口定义复杂对象的结构
4. **错误处理**: catch 块中的 error 参数需要类型声明
5. **常量声明**: 使用 `const` 而不是 `let` 声明不变的数组和对象
6. **函数签名**: 函数必须声明参数类型和返回值类型

## 编译验证

修复后的代码应该能够通过 ArkTS 编译器的检查，不再出现以下错误：
- `arkts-no-any-unknown`
- 类型推断错误
- 参数类型缺失错误

这些修复确保了代码符合 HarmonyOS ArkTS 的严格类型检查要求，提高了代码的类型安全性和可维护性。
