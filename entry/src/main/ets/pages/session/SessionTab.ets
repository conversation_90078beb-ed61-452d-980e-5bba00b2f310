import { Session } from "../../data_source/SessionBody"
import { getUser, insertUser } from "../../data_source/SessionDataSource";
import { toast } from "../../util/Toast";
import { UserEntity } from "../../entities/UserEntity";
import { SessionViewModel } from "./SessionViewModel";
import { routeTo } from "../../util/RouteHelper";
import { onEvent } from "../../util/EventBus";
import { log } from "../../util/Log";

// 会话tab组件 - MVVM架构
@ComponentV2
export struct SessionTabView {
  // ViewModel实例
  private viewModel: SessionViewModel = new SessionViewModel();


  // 未读数变化回调函数
  @Param onUnreadCountChanged: (newCount: number) => void = () => {};

  // 组件生命周期 - 页面即将出现
  aboutToAppear(): void {
    this.initializeViewModel();

    // 获取路由参数
    onEvent(this.getUIContext() , "a" , (x)=> {
      log(`会话列表： ${x}`)
      this.testReceiveEvent()
    })

  }

  testReceiveEvent = ()=> {
    log('~~~~')
  }

  // 初始化ViewModel
  private async initializeViewModel(): Promise<void> {
    // 设置回调函数
    this.viewModel.setOnUnreadCountChanged((newCount: number) => {
      this.onUnreadCountChanged?.(newCount);
    });

    // 初始化数据
    await this.viewModel.initialize();
  }

  // 处理搜索点击
  private handleSearchClick(): void {
    toast('搜索');
    getUser().then((user) => {
      console.info(`打印 User `, JSON.stringify(user));
    });
  }

  // 处理添加点击
  private handleAddClick(): void {
    toast('添加');
    insertUser(new UserEntity("1", "ddd", "dggg"));
  }

  // 处理会话点击
  private async handleSessionClick(session: Session): Promise<void> {

    // 跳转聊天页面
    routeTo(this.getUIContext() , 'pages/chat/ChatPage' , session)

    // 未读数 修改
    try {
      await this.viewModel.handleSessionClick(session);
      const newUnread = (session.unread ?? 0) > 0 ? (session.unread ?? 0) - 1 : 0;
      toast(`${session.name} 未读数: ${session.unread ?? 0} → ${newUnread}`);
    } catch (error) {
      toast(`操作失败: ${error}`);
    }
  }

  // 处理会话长按
  private async handleSessionLongPress(session: Session): Promise<void> {
    try {
      const newTopStatus = await this.viewModel.handleSessionLongPress(session);
      toast(`${session.name} ${newTopStatus ? '已置顶' : '已取消置顶'}`);
    } catch (error) {
      toast(`操作失败: ${error}`);
    }
  }

  build() {
    this.MessageTabContent()
  }

  // 消息Tab内容
  @Builder
  MessageTabContent() {
    Column() {
      // 顶部标题栏
      this.HeaderBuilder()

      // 分割线
      Line()
        .backgroundColor('#F2F6FF')
        .width('100%')
        .height(1)
        .margin({ top: 10, bottom: 5 })

      // 置顶会话
      this.TopSessionsBuilder()

      // 消息分类Tab
      this.MessageTabsBuilder()
    }
    .width('100%')
    .height('100%')
  }

  // 顶部标题栏构建器
  @Builder
  HeaderBuilder() {
    Row() {
      Blank().layoutWeight(1)

      Row() {
        Text('消息')
          .fontSize(17)
          .fontWeight(FontWeight.Bold)
          .align(Alignment.Center)

        if (this.viewModel.isLoading) {
          LoadingProgress()
            .width(30)
            .margin({ left: 5 })
        }
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)

      Row() {
        Image('/resources/base/media/im_newsHome_search.png')
          .width(24)
          .height(24)
          .margin({ right: 10 })
          .onClick(() => {
            this.handleSearchClick();
          })

        Image('/resources/base/media/im_newsHome_add.png')
          .width(24)
          .height(24)
          .onClick(() => {
            this.handleAddClick();
          })
      }
      .justifyContent(FlexAlign.End)
      .layoutWeight(1)
    }
    .margin({ top: 16, left: 16, right: 16 })
    .padding({ left: 16, right: 16 })
    .width('100%')
  }

  // 置顶会话构建器
  @Builder
  TopSessionsBuilder() {
    Row({ space: 16 }) {
      ForEach(this.viewModel.sessions.filter((s) => s.isTop === true), (item: Session, idx) => {
        Column() {
          Stack() {
            Image(item.avatar)
              .width(40)
              .height(40)
              .borderRadius(10)
            if (item.unread) {
              Text(item.unread.toString())
                .fontSize(12)
                .fontColor('#fff')
                .backgroundColor('#FF3B30')
                .borderRadius(10)
                .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                .position({ x: 32, y: -8 })
            }
            if (item.dot) {
              Circle()
                .width(8)
                .height(8)
                .backgroundColor('#FF9800')
                .position({ x: 40, y: 0 })
            }
          }
          Text(item.name)
            .fontSize(12)
            .margin({ top: 4 })
        }
        .margin({ right: 8 })
      })
    }
    .width('100%')
    .justifyContent(FlexAlign.Start)
    .padding({ top: 16, left: 16, right: 16 })
  }

  // 消息分类Tab构建器
  @Builder
  MessageTabsBuilder() {
    Tabs({ index: this.viewModel.currentMessageTabIndex }) {
      // 消息Tab
      TabContent() {
        this.MessageListComponent(0)
      }
      .tabBar(this.MessageTabBarBuilder(0, '消息'))
      .width('100%')
      .backgroundColor('#fff')
      .height(38)
      .layoutWeight(1)

      // 未读Tab
      TabContent() {
        this.MessageListComponent(1)
      }
      .tabBar(this.MessageTabBarBuilder(1, '未读'))
      .width('100%')
      .backgroundColor('#fff')
      .height(38)
      .layoutWeight(1)

      // 单聊Tab
      TabContent() {
        this.MessageListComponent(2)
      }
      .tabBar(this.MessageTabBarBuilder(2, '单聊'))
      .width('100%')
      .backgroundColor('#fff')
      .height(38)
      .layoutWeight(1)

      // 群聊Tab
      TabContent() {
        this.MessageListComponent(3)
      }
      .tabBar(this.MessageTabBarBuilder(3, '群聊'))
      .height(38)
      .backgroundColor('#fff')
      .layoutWeight(1)
    }
    .layoutWeight(1)
    .vertical(false)
    .scrollable(true)
    .barMode(BarMode.Fixed)
    .barPosition(BarPosition.Start)
    .borderRadius(16)
    .backgroundColor('#F2F6FF')
    .margin({ top: 16, left: 16, right: 16, bottom: 16 })
    .onChange((index: number) => {
      this.viewModel.currentMessageTabIndex = index;
    })
  }

  // 消息列表组件
  @Builder
  MessageListComponent(msgType: number) {
    Column() {
      List({ space: 0 }) {
        ForEach(this.viewModel.filterSessions(msgType), (user: Session, idx) => {
          ListItem() {
            Row() {
              // 头像
              Image(user.avatar)
                .width(48)
                .height(48)
                .borderRadius(12)

              // 中间内容区域
              Column() {
                // 第一行：姓名、标签、时间
                Row() {
                  Text(user.name)
                    .fontSize(16)
                    .fontWeight(FontWeight.Bold)
                    .flexShrink(0)

                  if (user.tag) {
                    Text(user.tag)
                      .fontSize(12)
                      .fontColor(user.tagColor ?? '#1976D2')
                      .backgroundColor('#F2F6FF')
                      .borderRadius(8)
                      .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                      .margin({ left: 8 })
                      .flexShrink(0)
                  }

                  Blank()

                  if (user.time) {
                    Text(user.time)
                      .fontSize(12)
                      .fontColor('#888')
                      .flexShrink(0)
                  }
                }
                .width('100%')
                .alignItems(VerticalAlign.Center)

                // 第二行：消息内容
                Text(user.message ?? '')
                  .fontSize(14)
                  .fontColor('#888')
                  .maxLines(1)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .margin({ top: 4 })
                  .width('100%')
              }
              .layoutWeight(1)
              .margin({ left: 12, right: 12 })
              .alignItems(HorizontalAlign.Start)

              if ((user.unread ?? 0) > 0) {
                Column() {
                  Text((user.unread ?? 0) > 99 ? '99+' : (user.unread ?? 0).toString())
                    .fontSize(12)
                    .fontColor('#fff')
                    .backgroundColor('#FF3B30')
                    .borderRadius(10)
                    .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                    .textAlign(TextAlign.Center)
                    .constraintSize({ minWidth: 20 })
                }
                .width(30)
                .alignItems(HorizontalAlign.Center)
                .justifyContent(FlexAlign.Start)
              }
            }
            .padding({ top: 12, bottom: 12, left: 8, right: 8 })
            .width('100%')
            .alignItems(VerticalAlign.Top)
          }
          .onClick(() => {
            this.handleSessionClick(user);
          })
          .gesture(
            LongPressGesture({ repeat: false })
              .onAction(() => {
                this.handleSessionLongPress(user);
              })
          )
        }, (user: Session) => `${user.name}_${user.time}_${user.unread}`)
      }
      .width('100%')
      .layoutWeight(1)
      .backgroundColor('#fff')
      .borderRadius(16)
    }
    .width('100%')
    .height('100%')
    .margin({ top: 12 })
  }

  // 消息分类TabBar构建器
  @Builder
  MessageTabBarBuilder(index: number, title: string) {
    Text(title)
      .fontSize(16)
      .height(30)
      .fontWeight(this.viewModel.currentMessageTabIndex === index ? FontWeight.Bold : FontWeight.Normal)
      .fontColor(this.viewModel.currentMessageTabIndex === index ? '#1976D2' : '#888')
      .backgroundColor(this.viewModel.currentMessageTabIndex !== index ? '#F2F6FF' : '#fff')
      .borderRadius(16)
      .padding({ left: 16, right: 16, top: 4, bottom: 4 })
      .margin({ left: 4, right: 4 })
  }

  // 组件销毁时清理资源
  aboutToDisappear(): void {
    this.viewModel.dispose();
  }
}

