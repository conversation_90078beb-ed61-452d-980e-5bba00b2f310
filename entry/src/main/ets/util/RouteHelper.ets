
import { WebViewParam } from './WebViewHelper';

// 全局对象存储，用于保持类型
class RouteParamStore {
  private static instance: RouteParamStore;
  private store: Map<string, ESObject> = new Map();

  static getInstance(): RouteParamStore {
    if (!RouteParamStore.instance) {
      RouteParamStore.instance = new RouteParamStore();
    }
    return RouteParamStore.instance;
  }

  setParam(key: string, param: ESObject): void {
    this.store.set(key, param);
  }

  getParam(key: string): ESObject | undefined {
    const param = this.store.get(key);
    this.store.delete(key); // 使用后删除
    return param;
  }
}

/// 增强的路由函数，支持类型保持
export function routeTo(context: UIContext, path: string, param?: ESObject) {
  if (param) {
    // 生成唯一key
    const paramKey = `${path}_${Date.now()}_${Math.random()}`;

    // 将原始对象存储到全局store
    RouteParamStore.getInstance().setParam(paramKey, param);

    // 通过路由传递key和类型信息
    context.getRouter().pushUrl({
      url: path,
      params: {
        _paramKey: paramKey,
        _paramType: param.constructor.name,
        // 同时传递序列化数据作为备份
        _serializedData: param
      }
    });
  } else {
    context.getRouter().pushUrl({
      url: path,
      params: param
    });
  }
}

/// 获取路由参数，保持原类型
export function getRouteParam<T>(context: UIContext): T | null {
  const params = context.getRouter().getParams();

  if (params && typeof params === 'object') {
    const paramObj = params as Record<string, ESObject>;

    // 如果有paramKey，从store获取原始对象
    if (paramObj._paramKey) {
      const originalParam = RouteParamStore.getInstance().getParam(paramObj._paramKey as string);
      if (originalParam) {
        return originalParam as T;
      }
    }

    // 如果store中没有，尝试从序列化数据重建
    if (paramObj._serializedData && paramObj._paramType) {
      return reconstructObject(paramObj._serializedData, paramObj._paramType as string) as T;
    }

    // 兜底：返回序列化数据
    return paramObj._serializedData as T || params as T;
  }

  return null;
}

/// 根据类型名重建对象
function reconstructObject(data: ESObject, typeName: string): ESObject | null {
  try {
    const dataObj = data as Record<string, ESObject>;

    switch (typeName) {
      case 'WebViewParam':
        if (dataObj.url && dataObj.title !== undefined && dataObj.isWebNavigation !== undefined) {
          return new WebViewParam(
            dataObj.url as string,
            dataObj.title as string,
            dataObj.isWebNavigation as number
          );
        }
        break;
      // 可以添加其他类型的重建逻辑
      default:
        return data;
    }
  } catch (error) {
    console.error(`重建对象失败: ${error}`);
  }

  return null;
}