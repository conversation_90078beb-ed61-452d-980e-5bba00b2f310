import router from '@ohos.router';
import { Country } from '../types/Country';
import { log } from '../util/Log';

// interface Country {
//   name: string,
//   capital: string
// }


@Entry   // 页面级别
@Component  // 组件级别
struct Page {

   @State countryData: Country = { name: '', capital: '' };
   @State inputText: string = '';

   // 单次调用
  aboutToAppear() {
    log('aboutToAppear.......')

    // 获取路由参数
    const params = this.getUIContext().getRouter().getParams() as Record<string, Country>;
    log(`=====> params: ${JSON.stringify(params)}`);
    if (params && params.country) {
      this.countryData = params.country;

      log(`=====> countryData: ${JSON.stringify(this.countryData)}`);
    }
  }

  build() {
    Column() {
      Text('国家详情')
        .fontSize(28)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 40, bottom: 40 })
        .align(Alignment.Center)

      Column({ space: 20 }) {
        Row() {
          Text('国家名称：')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .width('40%')
          Text(this.countryData.name)
            .fontSize(18)
            .width('60%')
        }
        .width('100%')
        .padding({ left: 20, right: 20 })

        Row() {
          Text('首都：')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .width('40%')
          Text(this.countryData.capital)
            .fontSize(18)
            .width('60%')
        }
        .width('100%')
        .padding({ left: 20, right: 20 })
      }
      .width('100%')
      .margin({ top: 40 })

      // 添加输入框
      Column({ space: 16 }) {
        Text('请输入备注信息：')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)

        TextInput({ placeholder: '请输入您的备注...' })
          .width('100%')
          .height(40)
          .fontSize(16)
          .onChange((value: string) => {
            this.inputText = value;
          })
      }
      .width('100%')
      .margin({ top: 40 })
      .padding({ left: 20, right: 20 })

      Button('返回首页')
        .fontSize(16)
        .margin({ top: 40 })
        .onClick(() => {
          // 使用router.back()并传递参数
          this.getUIContext().getRouter().back({
            url: 'pages/Index',
            params: {
              returnData: this.inputText,
              country: this.countryData
            }
          });
        })

      Button('跳转到CAbility')
        .fontSize(16)
        .margin({ top: 20 })
        .onClick((event?: ClickEvent)  => {
          log('333')
          this.getUIContext().getRouter().pushUrl({
            url: 'pages/DDMainPage'
          });
        })
    }
    .width('100%')
    .height('100%')
    .padding(20)
  }


  aboutToDisappear(): void {
    log('detail....aboutToDisappear')
  }

  onPageHide(): void {
    log('detail....onPageHide')
  }
}