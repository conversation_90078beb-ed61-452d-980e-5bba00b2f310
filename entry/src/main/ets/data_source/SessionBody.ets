

export interface Session {
  name: string;
  avatar: string;
  message?: string;
  time?: string;
  unread?: number;
  tag?: string;
  tagColor?: string;
  dot?: boolean;
  sessionType?: number;
  isTop? : boolean ;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed';
  priority: 'high' | 'medium' | 'low';
  dueDate: string;
  assignee: string;
}

export interface WorkItem {
  id: string;
  name: string;
  icon: string;
  description: string;
  badge?: number;
}