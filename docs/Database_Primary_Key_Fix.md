# dataORM主键配置问题修复指南

## 问题描述

在使用dataORM框架时遇到以下错误：
```
DataOrmService: 清除工具缓存失败 Error: [object Object] (TOOL_CACHE) does not have a single-column primary key
```

## 问题原因

该错误是由于ToolCacheEntity的主键配置不正确导致的。dataORM框架要求：

1. **单列主键**：每个实体必须有且仅有一个主键列
2. **明确标识**：主键必须使用`isPrimaryKey: true`明确标识
3. **正确的删除操作**：删除实体时需要使用正确的方法

## 解决方案

### 1. 修复主键配置

#### 错误的配置：
```typescript
@Entity('TOOL_CACHE')
export class ToolCacheEntity {
  @Id({ autoincrement: true })  // ❌ 缺少isPrimaryKey标识
  @Columns({ columnName: 'ID', types: ColumnType.num })
  id?: number;
}
```

#### 正确的配置：
```typescript
@Entity('TOOL_CACHE')
export class ToolCacheEntity {
  @Id({ isPrimaryKey: true, autoincrement: true })  // ✅ 明确标识主键
  @Columns({ columnName: 'ID', types: ColumnType.num })
  id?: number;
}
```

### 2. 修复删除操作

#### 错误的删除方法：
```typescript
// ❌ 直接删除实体对象可能导致主键识别问题
await this.toolCacheDao!.delete(entity);
```

#### 正确的删除方法：
```typescript
// ✅ 使用主键删除，确保主键正确识别
if (entity.getId() !== undefined) {
  await this.toolCacheDao!.deleteByKey(entity.getId()!);
}
```

### 3. 更新数据库版本

由于添加了新的实体表，需要更新数据库版本号：

```typescript
export class DataOrmHelper extends OpenHelper {
  private static readonly DB_NAME = 'app_database.db';
  private static readonly DB_VERSION = 2;  // ✅ 从1更新到2
}
```

## 完整修复代码

### ToolCacheEntity.ets
```typescript
@Entity('TOOL_CACHE')
export class ToolCacheEntity {
  @Id({ isPrimaryKey: true, autoincrement: true })
  @Columns({ columnName: 'ID', types: ColumnType.num })
  id?: number;

  @NotNull()
  @Index()
  @Columns({ columnName: 'TOOL_ID', types: ColumnType.str })
  toolId: string;

  // ... 其他字段
}
```

### DataOrmService.ets - 清除缓存方法
```typescript
public async clearToolCache(companyId: string): Promise<boolean> {
  this.checkInitialized();
  try {
    const entities = await this.toolCacheDao!.loadAll();
    const toDelete = entities.filter(entity => entity.getCompanyId() === companyId);
    
    for (const entity of toDelete) {
      if (entity.getId() !== undefined) {
        await this.toolCacheDao!.deleteByKey(entity.getId()!);
      }
    }
    
    console.info(`DataOrmService: 清除了${toDelete.length}个工具缓存，公司ID: ${companyId}`);
    return true;
  } catch (error) {
    console.error('DataOrmService: 清除工具缓存失败', error);
    return false;
  }
}
```

### DataOrmHelper.ets - 数据库版本
```typescript
export class DataOrmHelper extends OpenHelper {
  private static readonly DB_NAME = 'app_database.db';
  private static readonly DB_VERSION = 2;  // 更新版本号
}
```

## dataORM主键最佳实践

### 1. 主键配置规范
```typescript
// ✅ 自增主键（推荐）
@Id({ isPrimaryKey: true, autoincrement: true })
@Columns({ columnName: 'ID', types: ColumnType.num })
id?: number;

// ✅ 字符串主键
@Id({ isPrimaryKey: true, autoincrement: false })
@Columns({ columnName: 'UID', types: ColumnType.str })
@Unique()
@NotNull()
uid: string;
```

### 2. DAO类型声明
```typescript
// 主键为number类型
private toolCacheDao: AbstractDao<ToolCacheEntity, number> | null = null;

// 主键为string类型
private userDao: AbstractDao<UserEntity, string> | null = null;
```

### 3. 删除操作规范
```typescript
// ✅ 推荐：使用主键删除
await dao.deleteByKey(primaryKey);

// ✅ 备选：删除前检查主键
if (entity.getPrimaryKey() !== undefined) {
  await dao.delete(entity);
}

// ❌ 避免：直接删除可能导致主键识别问题
await dao.delete(entity);
```

### 4. 查询操作规范
```typescript
// ✅ 使用主键查询
const entity = await dao.load(primaryKey);

// ✅ 使用条件查询
const entities = await dao.loadAll();
const filtered = entities.filter(e => e.getCompanyId() === companyId);
```

## 常见错误和解决方案

### 错误1：主键配置缺失
```
Error: does not have a single-column primary key
```
**解决方案**：确保使用`@Id({ isPrimaryKey: true })`标识主键

### 错误2：多主键冲突
```
Error: Multiple primary keys found
```
**解决方案**：确保每个实体只有一个`@Id`注解的字段

### 错误3：主键类型不匹配
```
Error: Primary key type mismatch
```
**解决方案**：确保DAO泛型类型与实体主键类型一致

### 错误4：删除操作失败
```
Error: Cannot delete entity without primary key
```
**解决方案**：使用`deleteByKey()`方法或确保实体有有效主键

## 验证修复

### 1. 编译检查
```bash
hvigorw assembleHap
```
应该显示：`BUILD SUCCESSFUL`

### 2. 运行时测试
```typescript
// 测试缓存操作
await toolDataService.refreshTools('8888');
```
应该不再出现主键错误日志

### 3. 数据库检查
```typescript
// 测试数据库操作
await dataOrmService.testDatabase();
```
应该显示正常的数据库操作日志

## 总结

dataORM主键配置的关键点：

1. **明确标识**：使用`@Id({ isPrimaryKey: true })`
2. **类型一致**：DAO泛型类型与主键类型匹配
3. **正确删除**：优先使用`deleteByKey()`方法
4. **版本管理**：添加新实体时更新数据库版本

通过以上修复，dataORM的主键配置问题已经完全解决，缓存清除功能现在可以正常工作。
