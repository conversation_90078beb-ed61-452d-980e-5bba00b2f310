import { Session, Task, WorkItem } from './SessionBody';
import { distributedDataObject, distributedKVStore } from '@kit.ArkData';
import { Context } from '@kit.AbilityKit';
import { dataOrmService } from '../database/DataOrmService';
import { UserEntity } from '../entities/UserEntity';

// 消息数据 - 现在从dataORM读取
export const getAllSessions = async (): Promise<Session[]> => {
  try {
    return await dataOrmService.getAllSessions();
  } catch (error) {
    console.error('SessionDataSource: 获取会话数据失败', error);
    // 如果数据库读取失败，返回空数组
    return [];
  }
};


// 任务数据 - 现在从dataORM读取
export const getAllTasks = async (): Promise<Task[]> => {
  try {
    return await dataOrmService.getAllTasks();
  } catch (error) {
    console.error('SessionDataSource: 获取任务数据失败', error);
    return [];
  }
};





// 获取主会话未读数 - 异步版本
export async function getMainSessionUnreadCount(): Promise<number> {
  try {
    return await dataOrmService.getTotalUnreadCount();
  } catch (error) {
    console.error('SessionDataSource: 获取未读数失败', error);
    return 0;
  }
}


// user 操作
export async function insertUser(user:UserEntity): Promise<boolean> {
  try {
    return await dataOrmService.insertUser(user);
  } catch (error) {
    console.error('SessionDataSource: 获取未读数失败', error);
    return false;
  }
}

export async function getUser(): Promise<UserEntity | null> {
  try {
    return await dataOrmService.getAllUsers();
  } catch (error) {
    console.error('SessionDataSource: 获取未读数失败', error);
    return null;
  }
}
