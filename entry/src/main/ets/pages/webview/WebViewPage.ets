import router from '@ohos.router';
import web_webview from '@ohos.web.webview'
import { CommonToolbar, ToolbarPresets } from '../../components/CommonToolbar';
import { WebViewViewModel } from './WebViewViewModel';
import { WebViewParam } from '../../util/WebViewHelper';
import { BASE_WIN_OBJ, JSMethodArray, JsProxy } from './WebJS';
import { log } from '../../util/Log';


/// 通用 web 加载组件
@Entry
@Component
struct WebViewPage {

  private controller = new web_webview.WebviewController();
  private vm = new WebViewViewModel();

  // 用于控制进度条的状态变量
  @State progress: number = 0;
  @State isProgressVisible: boolean = false;

  aboutToAppear() {
    const params = this.getUIContext().getRouter().getParams() as WebViewParam;
    this.vm.param = params;
    log(` ===> ${this.vm.param?.url}`)
  }

  private proxy: JavaScriptProxy = {
    object: new JsProxy(this.controller),
    name: BASE_WIN_OBJ,
    methodList: JSMethodArray,
    controller: this.controller
  };

  build() {
    Column() {
      // 工具栏
      if ((this.vm.param?.isWebNavigation || 0) > 0) {
        CommonToolbar({ config: ToolbarPresets.simple(`${this.vm.title}`) });
      }
      // 顶部的加载进度条组件
      Progress({
        value: this.progress,
        total: 100,
        type: ProgressType.Linear
      })
        .width('100%')
        .height(1)
        .color(Color.Blue)
        .visibility(this.isProgressVisible ? Visibility.Visible : Visibility.Hidden)
        .align(Alignment.TopStart);

      Web({
        src: this.vm.param?.url,
        controller: this.controller
      })
        .javaScriptAccess(true)
        .domStorageAccess(true)
        .fileAccess(true)
        .imageAccess(true)
        .geolocationAccess(true)
        .cacheMode(CacheMode.Online)
        .onTitleReceive((e) => {
          if (e) this.vm.title = e.title;
        })
        .onPageEnd((e) => {
          log(`WebView页面加载完成: ${e?.url}`);
          // 页面加载完成后，确保JavaScript对象已注入
          this.checkAndInjectJavaScript();
        })
        .javaScriptProxy(this.proxy)
        .onProgressChange((e) => {
          if (e) {
            this.progress = e.newProgress;
            // 当开始加载且进度未满100时，显示进度条
            if (this.progress > 0 && this.progress < 100) {
              this.isProgressVisible = true;
            }
            // 当加载完成时，延迟一小段时间再隐藏，以优化视觉效果
            else if (this.progress === 100) {
              setTimeout(() => {
                this.isProgressVisible = false;
              }, 300);
            }
          }
        });
    }
    .width('100%')
    .height('100%')
    .onAppear(() => {

    })
  }

  // 检查并注入JavaScript对象
  private checkAndInjectJavaScript() {
    try {
      // 延迟一小段时间确保页面完全加载
      setTimeout(() => {
        // 检查JavaScript对象是否存在
        const checkScript = `
          (function() {
            if (typeof window.${BASE_WIN_OBJ} !== 'undefined') {
              console.log('${BASE_WIN_OBJ} 对象已存在');
              return true;
            } else {
              console.log('${BASE_WIN_OBJ} 对象不存在');
              return false;
            }
          })();
        `;

        this.controller.runJavaScript(checkScript)
          .then((result) => {
            log(`JavaScript对象检查结果: ${result}`);
            // 无论对象是否存在，都尝试触发页面的初始化方法
            this.triggerPageReady();
          })
          .catch((error: Error) => {
            log(`JavaScript对象检查失败: ${error.message}`);
            // 即使检查失败，也尝试触发页面初始化
            this.triggerPageReady();
          });
      }, 500);
    } catch (error) {
      log(`注入JavaScript对象失败: ${error}`);
    }
  }

  // 触发页面准备就绪事件
  private triggerPageReady() {
    try {
      // 通知页面原生对象已准备就绪
      const readyScript = `
        (function() {
          // 触发自定义事件通知页面原生对象已准备就绪
          if (typeof window.dispatchEvent === 'function') {
            var event = new CustomEvent('nativeReady', {
              detail: { platform: 'harmonyos', object: '${BASE_WIN_OBJ}' }
            });
            window.dispatchEvent(event);
            console.log('已触发 nativeReady 事件');
          }

          // 如果页面有特定的初始化方法，也可以直接调用
          if (typeof window.onNativeReady === 'function') {
            window.onNativeReady('${BASE_WIN_OBJ}');
            console.log('已调用 onNativeReady 方法');
          }

          return true;
        })();
      `;

      this.controller.runJavaScript(readyScript)
        .then((result) => {
          log(`页面准备就绪事件触发成功: ${result}`);
        })
        .catch((error: Error) => {
          log(`页面准备就绪事件触发失败: ${error.message}`);
        });
    } catch (error) {
      log(`触发页面准备就绪事件失败: ${error}`);
    }
  }

  aboutToDisappear(): void {}

  onPageHide(): void {}
}