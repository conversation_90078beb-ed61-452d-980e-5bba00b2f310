# 状态栏高度获取实现

## 🎯 **实现的功能**

我已经为 `DeviceUtil` 中的 `getStatusBarHeight` 方法补全了完整的实现，提供了两种获取状态栏高度的方式。

## 🔧 **实现方法**

### 1. **同步方法 - getStatusBarHeight()**

```typescript
static getStatusBarHeight(): number {
  try {
    // 获取主窗口
    const mainWindow = window.getLastWindow(getContext());
    
    if (mainWindow) {
      // 获取窗口避让区域
      const avoidArea = mainWindow.getWindowAvoidAreaSync(window.AvoidAreaType.TYPE_SYSTEM);
      
      if (avoidArea && avoidArea.topRect) {
        const statusBarHeight = avoidArea.topRect.height;
        console.info(`获取状态栏高度成功: ${statusBarHeight}px`);
        return statusBarHeight;
      }
    }
    
    return 44; // 默认值
  } catch (error) {
    console.error(`获取状态栏高度失败: ${error}`);
    return 44;
  }
}
```

### 2. **异步方法 - getStatusBarHeightAsync()**

```typescript
static async getStatusBarHeightAsync(): Promise<number> {
  try {
    // 获取主窗口
    const mainWindow = await window.getLastWindow(getContext());
    
    if (mainWindow) {
      // 获取窗口避让区域
      const avoidArea = await mainWindow.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
      
      if (avoidArea && avoidArea.topRect) {
        const statusBarHeight = avoidArea.topRect.height;
        console.info(`异步获取状态栏高度成功: ${statusBarHeight}px`);
        return statusBarHeight;
      }
    }
    
    return 44; // 默认值
  } catch (error) {
    console.error(`异步获取状态栏高度失败: ${error}`);
    return 44;
  }
}
```

## 📱 **技术原理**

### 1. **窗口避让区域**
- 使用 `window.getWindowAvoidArea()` 获取系统UI占用的区域
- `window.AvoidAreaType.TYPE_SYSTEM` 表示系统UI区域（状态栏、导航栏等）
- `avoidArea.topRect.height` 就是状态栏的高度

### 2. **同步 vs 异步**
- **同步方法**: 使用 `getWindowAvoidAreaSync()` 立即返回结果
- **异步方法**: 使用 `getWindowAvoidArea()` 返回Promise，适合在异步环境中使用

### 3. **错误处理**
- 多层错误检查：窗口获取失败、避让区域获取失败
- 降级机制：失败时返回默认值44px
- 详细日志：记录成功和失败的情况

## 🎨 **使用方式**

### 1. **同步调用**
```typescript
// 在组件中使用
const statusBarHeight = DeviceUtils.getStatusBarHeight();
console.log(`状态栏高度: ${statusBarHeight}px`);

// 在样式中使用
.content {
  margin-top: ${DeviceUtils.getStatusBarHeight()}px;
}
```

### 2. **异步调用**
```typescript
// 在异步函数中使用
async function setupUI() {
  const statusBarHeight = await DeviceUtils.getStatusBarHeightAsync();
  console.log(`状态栏高度: ${statusBarHeight}px`);
  
  // 设置UI布局
  this.topMargin = statusBarHeight;
}
```

### 3. **在WebView中使用**
```typescript
// 在updateClientInfo中使用
const clientInfo: ClientInfo = {
  statusHeight: DeviceUtils.getStatusBarHeight(), // 使用真实的状态栏高度
  company: companyInfo,
  user: userInfo,
  token: "Bearer xxx"
};
```

## 🔍 **不同设备的状态栏高度**

### 常见高度值
- **标准设备**: 44px - 48px
- **刘海屏设备**: 48px - 54px
- **打孔屏设备**: 44px - 50px
- **折叠屏设备**: 可能有不同的值

### 动态获取的优势
- ✅ **适配所有设备**: 自动获取真实高度
- ✅ **响应屏幕旋转**: 横竖屏切换时高度可能不同
- ✅ **适配系统更新**: 系统UI变化时自动适配

## 🧪 **测试验证**

### 1. **基本测试**
```typescript
// 测试同步方法
const height1 = DeviceUtils.getStatusBarHeight();
console.log(`同步获取: ${height1}px`);

// 测试异步方法
DeviceUtils.getStatusBarHeightAsync().then(height2 => {
  console.log(`异步获取: ${height2}px`);
});
```

### 2. **不同场景测试**
- **应用启动时**: 测试初始获取
- **屏幕旋转后**: 测试横竖屏切换
- **多窗口模式**: 测试分屏模式下的表现

### 3. **错误场景测试**
- **无窗口环境**: 测试在Service中调用
- **权限不足**: 测试权限受限情况

## 📋 **注意事项**

### 1. **调用时机**
- 确保在UI组件初始化后调用
- 避免在应用启动的极早期调用

### 2. **性能考虑**
- 同步方法性能更好，适合频繁调用
- 异步方法更安全，适合一次性获取

### 3. **兼容性**
- 提供了44px的默认值，确保在任何情况下都有合理的返回值
- 适配不同版本的HarmonyOS系统

## ✅ **验证清单**

- [ ] **编译通过**: 没有类型错误
- [ ] **功能正常**: 能正确获取状态栏高度
- [ ] **错误处理**: 异常情况下返回默认值
- [ ] **日志输出**: 有详细的调试信息
- [ ] **性能良好**: 调用速度快，不阻塞UI

## 🚀 **使用建议**

1. **优先使用同步方法**: 在大多数UI场景中使用 `getStatusBarHeight()`
2. **异步方法用于初始化**: 在应用启动或页面初始化时使用 `getStatusBarHeightAsync()`
3. **缓存结果**: 如果频繁使用，可以缓存获取的高度值
4. **监听变化**: 如果需要响应屏幕旋转，可以监听窗口变化事件

现在您可以在项目中使用真实的状态栏高度了，特别是在WebView的 `updateClientInfo` 方法中！
