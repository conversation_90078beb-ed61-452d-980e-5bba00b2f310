// IMWebSocket.ts
// 鸿蒙 ArkTS 单例 WebSocket 工具，支持连接、重连、消息收发、事件回调
import { BusinessError } from '@ohos.base';
import * as IMProto from './proto';
import webSocket from '@ohos.net.webSocket';
import { UserManager } from '../user_manage/UserManage';
import { BufferUtils } from '../util/BufferUtils';
import { DeviceUtils } from '../util/DeviceUtil';
import { protobufToMessage } from './MessageConvert';

// TODO: 引入一个事件分发库，例如 eventemitter3，或者自己实现一个简单的
// import { EventEmitter } from 'events';

const PING_MSG_TYPE = 0; // 心跳请求消息类型，请根据您的 proto 文件定义修改
const PONG_MSG_TYPE = 0; // 心跳响应消息类型，请根据您的 proto 文件定义修改

enum SocketState {
  CONNECTING,
  OPEN,
  CLOSING,
  CLOSED,
}

class IMWebSocket {
  private ws = webSocket.createWebSocket();;
  private serverUrl: string = '';
  private _state: SocketState = SocketState.CLOSED;

  // 重连相关
  private reconnectTimer: number | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectInterval: number = 3000; // 初始重连间隔 3 秒

  // 心跳相关
  private heartbeatTimer: number | null = null;
  private serverTimeoutTimer: number | null = null;
  private heartbeatInterval: number = 30000; // 30 秒
  private serverTimeoutInterval: number = 10000; // 10 秒

  // 是否为手动关闭
  private isManualClose: boolean = false;

  // 简单的事件监听器
  private listeners: Map<string, Function[]> = new Map();

  constructor(url: string) {
    // this.serverUrl = url;
  }

  public connect(url: string) {
    this.serverUrl = url
    if (this._state === SocketState.OPEN || this._state === SocketState.CONNECTING) {
      console.log(`[IMWebSocket] connect() called in state ${this._state}, ignoring.`);
      return;
    }

    console.log('[IMWebSocket] Connecting...');
    this.isManualClose = false;
    this._state = SocketState.CONNECTING;

    this.ws = webSocket.createWebSocket();
    this._bindEvents();

    this.ws.connect(url, (err: BusinessError) => {
      if (err) {
        console.error('[IMWebSocket] Connection failed:', JSON.stringify(err));
        // 连接失败会触发 close 事件，由 close 事件统一处理重连
      }
    });
  }

  public close() {
    if (this._state === SocketState.CLOSED || this._state === SocketState.CLOSING) {
      return;
    }
    console.log('[IMWebSocket] Closing connection manually.');
    this.isManualClose = true;
    this._stopHeartbeat();
    this._stopReconnect();
    this.ws.close();
  }

  public send(data: ArrayBuffer | string): boolean {
    if (this._state !== SocketState.OPEN) {
      console.error('[IMWebSocket] Cannot send message, socket is not open.');
      return false;
    }
    this.ws.send(data, (err) => {
      if (err) {
        console.error('[IMWebSocket] Failed to send message:', JSON.stringify(err));
      }
    });
    return true;
  }

  public getState(): SocketState {
    return this._state;
  }

  // 订阅事件
  public on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    // 先获取，再操作，对类型检查更友好
    const listenersForEvent = this.listeners.get(event);
    if (listenersForEvent) {
      listenersForEvent.push(callback);
    }
  }

  // 取消订阅
  public off(event: string, callback: Function) {
    const listenersForEvent = this.listeners.get(event);
    if (!listenersForEvent) {
      return;
    }
    const updatedListeners = listenersForEvent.filter(listener => listener !== callback);
    this.listeners.set(event, updatedListeners);
  }

  // 内部触发事件
  private _emit(event: string, data: object) {
    const listenersForEvent = this.listeners.get(event);
    if (listenersForEvent) {
      listenersForEvent.forEach(callback => {
        try {
          callback(data);
        } catch (e) {
          console.error(`[IMWebSocket] Error in '${event}' listener:`, e);
        }
      });
    }
  }

  // public on(event: string, listener: (...args: any[]) => void) {
  //   this.emitter.on(event, listener);
  // }
  //
  // public off(event: string, listener: (...args: any[]) => void) {
  //   this.emitter.off(event, listener);
  // }

  private _bindEvents() {
    this.ws.on('open', (err: BusinessError, value: object) => {
      if (err) {
        // 'open' 事件中的错误通常意味着连接尝试失败，close 事件会被触发
        console.error('[IMWebSocket] on-open error:', JSON.stringify(err));
        return;
      }
      console.log('[IMWebSocket] Connection opened.');
      this._state = SocketState.OPEN;
      this.reconnectAttempts = 0; // 连接成功，重置重连尝试次数
      this._stopReconnect(); // 停止可能存在的重连计时器
      this._startHeartbeat(); // 开启心跳
      this.toLoginIM(); // 发送登录请求
    });

    this.ws.on('message', (err: BusinessError, value: string | ArrayBuffer) => {
      if (err) {
        console.error('[IMWebSocket] on-message error:', JSON.stringify(err));
        return;
      }

      // 收到任何消息都代表连接正常，重置心跳
      this._resetHeartbeat();

      if (value instanceof ArrayBuffer) {
        try {
          const imMsg = IMProto.com.joinu.im.protobuf.ImMsg.decode(new Uint8Array(value));
          if (imMsg.type === PONG_MSG_TYPE) {
            console.log('[IMWebSocket] Pong received.');
            return; // 是心跳响应，无需进一步处理
          }

          let msg = protobufToMessage(imMsg)
          console.log('[IMWebSocket] 转换后的 :', JSON.stringify(msg));

          this._emit('message', msg);
          console.log('[IMWebSocket] Decoded IM Message:', JSON.stringify(imMsg));
        } catch (e) {
          console.error('[IMWebSocket] Failed to decode protobuf message:', e);
        }
      }
    });

    this.ws.on('close', (err: BusinessError, value: webSocket.CloseResult) => {
      this._state = SocketState.CLOSED;
      this._stopHeartbeat();
      console.log(`[IMWebSocket] Connection closed. Code: ${value.code}, Reason: ${value.reason}`);
      if (!this.isManualClose) {
        console.log('[IMWebSocket] Connection closed unexpectedly, attempting to reconnect...');
        this._reconnect();
      }
    });

    this.ws.on('error', (err: BusinessError) => {
      // error 事件通常会伴随着 close 事件，统一在 close 中处理
      console.error('[IMWebSocket] An error occurred:', JSON.stringify(err));
    });
  }

  private _reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('[IMWebSocket] Reached max reconnect attempts.');
      return;
    }

    this._stopReconnect(); // 清除旧的计时器

    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    console.log(`[IMWebSocket] Reconnecting in ${delay / 1000} seconds... (Attempt ${this.reconnectAttempts})`);

    this.reconnectTimer = setTimeout(() => {
      this.connect(this.serverUrl);
    }, delay);
  }

  private _stopReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private _startHeartbeat() {
    this._stopHeartbeat();
    console.log('[IMWebSocket] Starting heartbeat...');
    this.heartbeatTimer = setInterval(() => {
      if (this._state !== SocketState.OPEN) {
        return;
      }
      // 发送心跳包
      // const heartbeatMsg = new IMProto.com.joinu.im.protobuf.ImMsg();
      // heartbeatMsg.type = PING_MSG_TYPE;
      // heartbeatMsg.msgContent = 'ping';
      // const buffer = IMProto.com.joinu.im.protobuf.ImMsg.encode(heartbeatMsg).finish();
      // this.send(buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength));

      this.send('ping')
      console.log('[IMWebSocket] Ping sent.');

      // 设置服务器超时计时器
      this.serverTimeoutTimer = setTimeout(() => {
        console.error('[IMWebSocket] Server timeout. No pong received. Closing connection...');
        this.ws.close(); // 主动关闭，会触发 onclose 中的重连逻辑
      }, this.serverTimeoutInterval);

    }, this.heartbeatInterval);
  }

  private _stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    if (this.serverTimeoutTimer) {
      clearTimeout(this.serverTimeoutTimer);
      this.serverTimeoutTimer = null;
    }
    console.log('[IMWebSocket] Heartbeat stopped.');
  }

  private _resetHeartbeat() {
    this._stopHeartbeat();
    this._startHeartbeat();
  }

  private async toLoginIM() {
    console.log('[IMWebSocket] Attempting to log in...');
    try {
      const req = await this.createLogin();
      this.send(req);
      console.log('[IMWebSocket] Login request sent.');
    } catch (e) {
      console.error('[IMWebSocket] Failed to create login request:', e);
    }
  }

  public async createLogin(): Promise<ArrayBuffer> {
    const aaid = await DeviceUtils.getDeviceAAID();
    const loginRequest = new IMProto.com.joinu.im.protobuf.LoginRequest();
    const deviceToken = `${UserManager.getImToken()}::${aaid}`;
    loginRequest.token = deviceToken;
    loginRequest.clientType = IMProto.com.joinu.im.protobuf.LoginRequest.ClientTypeEnum.ANDROID;

    const userInfo = new IMProto.com.joinu.im.protobuf.UserInfo();
    userInfo.nickname = UserManager.getUserName();
    userInfo.userId = UserManager.getUserId();
    userInfo.avatar = UserManager.getUserAvatar();

    const imMsg = new IMProto.com.joinu.im.protobuf.ImMsg();
    imMsg.cmdId = DeviceUtils.getUUID();
    imMsg.msgTime = DeviceUtils.getTimStamp();
    imMsg.loginRequest = loginRequest;
    imMsg.msgContent = '';
    imMsg.type = 1; // 登录请求类型
    imMsg.senderInfo = userInfo;
    imMsg.recordIgnore = false;

    const buff = IMProto.com.joinu.im.protobuf.ImMsg.encode(imMsg).finish();
    return buff.buffer.slice(buff.byteOffset, buff.byteOffset + buff.byteLength);
  }


  parseMsg() {

  }

}

// 单例模式导出
// 请在使用前，通过 IMWebSocket.getInstance().setUrl('YOUR_URL') 设置地址
class WebSocketManager {
  private static instance: IMWebSocket;

  public static getInstance(): IMWebSocket {
    if (!WebSocketManager.instance) {
      // 初始 URL 可以是空的，或者是一个默认的开发环境地址
      WebSocketManager.instance = new IMWebSocket('');
    }
    return WebSocketManager.instance;
  }

  // 允许在应用启动时动态设置 URL
  public static setUrl(url: string) {
    if (WebSocketManager.instance && WebSocketManager.instance.getState() !== SocketState.CLOSED) {
      console.warn('[IMWebSocket] Cannot set URL while connection is active. Please close it first.');
      return;
    }
    WebSocketManager.instance = new IMWebSocket(url);
  }
}

export default WebSocketManager;
