# Android代码深度分析与HarmonyOS修正

## 重要发现

通过深度分析Android的 `CommonWebFragment.kt`，发现了关键的双对象名机制：

### Android的双对象名机制

#### 1. **注入时的对象名** (第380行)
```kotlin
webview?.addJavascriptInterface(WebMutualInterface(mActivity, this), ConsKeys.JS_TRANS_PORT)
```
- 使用 `ConsKeys.JS_TRANS_PORT` 作为注入的JavaScript对象名
- 根据您的确认，这个值是 `DDBESOFFICE`

#### 2. **回调时的对象名** (第164行)
```kotlin
var objName: String = "ddbes_web"  // 初始值
```

#### 3. **动态更新机制** (第1285-1286行)
```kotlin
if (!objName.isNullOrBlank()) {
    this.objName = objName  // 从client_isAlready参数中动态更新
}
```

#### 4. **JavaScript回调使用动态objName** (第2452行)
```kotlin
"javascript:window.${objName}.${method}()"
```

## Android的工作流程

1. **注入阶段**: 使用 `DDBESOFFICE` 注入JavaScript对象
2. **调用阶段**: 前端使用 `window.DDBESOFFICE.client_isAlready()` 调用
3. **更新阶段**: 从 `client_isAlready` 参数中获取 `objName`，更新为前端指定的名称
4. **回调阶段**: 使用更新后的 `objName` 进行JavaScript回调

## HarmonyOS修正

### 修正前的问题
```typescript
// ❌ 错误：只使用了一个对象名
export const BASE_WIN_OBJ = 'ddbes_web';
```

### 修正后的实现
```typescript
// ✅ 正确：实现双对象名机制
export const BASE_WIN_OBJ = 'DDBESOFFICE';        // 注入时使用
export const DEFAULT_CALLBACK_OBJ = 'ddbes_web';   // 默认回调对象名

export class JsProxy {
  // 对应Android的objName变量，会被动态更新
  currentObj: string = DEFAULT_CALLBACK_OBJ;
  
  public client_isAlready(message: string) {
    try {
      let o = JSON.parse(message) as ClientIsAlreadyBody;
      
      // 对应Android第1285-1286行的逻辑
      if (o.objName && o.objName.trim() !== '') {
        this.currentObj = o.objName;  // 动态更新回调对象名
      }
      
      // 使用更新后的对象名进行回调
      this.updateClientInfo();
    } catch (error) {
      // 错误处理
    }
  }
}
```

## 前端调用方式

### 正确的调用方式
```javascript
// 1. 使用注入的对象名调用原生方法
window.DDBESOFFICE.client_isAlready(JSON.stringify({
  title: '页面标题',
  subTitle: '页面副标题',
  objName: 'ddbes_web',  // 指定回调时使用的对象名
  useTitleBar: true
}));

// 2. 准备接收回调的对象
window.ddbes_web = window.ddbes_web || {};
window.ddbes_web.updateClientInfo = function(clientInfoJson) {
  // 处理客户端信息回调
};
```

## 为什么之前没有工作

### 1. **对象名错误**
- HarmonyOS使用了 `ddbes_web` 作为注入对象名
- 但前端代码使用 `DDBESOFFICE` 调用
- 导致 `window.DDBESOFFICE` 不存在

### 2. **缺少动态更新机制**
- Android会根据 `client_isAlready` 参数动态更新回调对象名
- HarmonyOS之前没有实现这个机制

### 3. **回调对象名固定**
- Android使用动态的 `objName` 进行回调
- HarmonyOS之前使用固定的对象名

## 验证方法

### 1. **检查注入对象**
```javascript
console.log('DDBESOFFICE对象:', typeof window.DDBESOFFICE);
console.log('ddbes_web对象:', typeof window.ddbes_web);
```

### 2. **测试调用**
```javascript
if (typeof window.DDBESOFFICE !== 'undefined') {
  window.DDBESOFFICE.client_isAlready(JSON.stringify({
    title: '测试',
    objName: 'ddbes_web',
    useTitleBar: true
  }));
}
```

### 3. **验证回调**
```javascript
window.ddbes_web = window.ddbes_web || {};
window.ddbes_web.updateClientInfo = function(data) {
  console.log('收到客户端信息:', data);
};
```

## 预期结果

修正后，HarmonyOS应该能够：

1. ✅ **正确注入**: `window.DDBESOFFICE` 对象存在
2. ✅ **正常调用**: `client_isAlready` 方法被调用
3. ✅ **动态更新**: 回调对象名根据参数更新
4. ✅ **正确回调**: 使用正确的对象名进行JavaScript回调

这样就能与Android/iOS保持完全一致的行为，现有的前端代码无需任何修改即可正常工作。
