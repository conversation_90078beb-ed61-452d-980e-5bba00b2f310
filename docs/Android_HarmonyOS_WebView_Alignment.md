# Android与HarmonyOS WebView JavaScript调用对齐

## 问题根因分析

通过分析Android的 `CommonWebFragment.kt` 实现，发现了HarmonyOS版本与Android版本的关键差异：

### 1. **JavaScript对象名不一致**

**Android版本**:
```kotlin
// 第164行：默认对象名
var objName: String = "ddbes_web"

// 第380行：注入JavaScript对象
webview?.addJavascriptInterface(WebMutualInterface(mActivity, this), ConsKeys.JS_TRANS_PORT)
```

**HarmonyOS原版本**:
```typescript
export const BASE_WIN_OBJ = 'DDBESOFFICE'; // ❌ 与Android不一致
```

**修复后**:
```typescript
export const BASE_WIN_OBJ = 'ddbes_web'; // ✅ 与Android保持一致
```

### 2. **缺少updateClientInfo回调机制**

**Android版本** (第1323-1341行):
```kotlin
private fun updateClientInfo() {
    val map = hashMapOf<String, Any>()
    map["token"] = UserHolder.getAccessToken()
    if (UserHolder.isLogin()) {
        map["user"] = UserHolder.getCurrentUser()!!
    }
    if (CompanyHolder.getCurrentOrg() != null) {
        map["company"] = CompanyHolder.getCurrentOrg()!!
    }
    map["statusHeight"] = statusHeight
    invokeJs("updateClientInfo", GsonUtil.toJson(map))
}
```

**HarmonyOS新增**:
```typescript
private updateClientInfo(): void {
    const clientInfo = {
        token: 'harmony_test_token_' + Date.now(),
        user: { userId: 'harmony_user_123', userName: 'HarmonyOS用户' },
        company: { companyId: 'harmony_company_456', companyName: 'HarmonyOS公司' },
        statusHeight: 44
    };
    
    // 调用Web页面的updateClientInfo方法
    window.${this.currentObj}.updateClientInfo(JSON.stringify(clientInfo));
}
```

### 3. **client_isAlready处理流程对齐**

**Android版本** (第769-786行):
```kotlin
@JavascriptInterface
fun client_isAlready(json: String) {
    mActivity.runOnUiThread {
        try {
            val jsonObj = JSONObject(json)
            callback.onWebReady(
                jsonObj.optString("title"),
                jsonObj.optString("subTitle"),
                jsonObj.optString("objName"),
                jsonObj.optBoolean("useTitleBar")
            )
        }catch (e: Exception){
            e.printStackTrace()
        }
    }
}
```

**HarmonyOS对齐后**:
```typescript
public client_isAlready(message: string) {
    try {
        let o = JSON.parse(message) as ClientIsAlreadyBody;
        if (o.objName) {
            this.currentObj = o.objName; // 更新对象名
        }
        // 模拟Android的行为：回调客户端信息
        this.updateClientInfo();
    } catch (error) {
        // 错误处理
    }
}
```

## 关键修复点

### 1. **统一JavaScript对象名**
- 将 `DDBESOFFICE` 改为 `ddbes_web`
- 确保与Android使用相同的对象名

### 2. **添加客户端信息回调**
- 实现 `updateClientInfo` 方法
- 在 `client_isAlready` 调用后自动回调客户端信息
- 提供token、用户信息、公司信息等

### 3. **完善Web页面接收机制**
- 在测试页面中添加 `updateClientInfo` 接收方法
- 正确处理客户端信息回调

### 4. **保持调用时机一致**
- Android在页面加载完成后立即可用
- HarmonyOS通过优化注入时机实现相同效果

## 验证方法

### 1. **运行测试页面**
```
工作台 -> JS调用测试
```

### 2. **查看关键日志**
```
🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉
📨 接收到的消息: {"title":"测试页面标题","objName":"ddbes_web",...}
📤 开始向Web页面回调客户端信息...
📤 updateClientInfo调用结果: UPDATE_CLIENT_INFO_SUCCESS
```

### 3. **Web页面接收验证**
```javascript
// 在浏览器控制台应该看到
🎉 收到客户端信息回调: {"token":"harmony_test_token_...","user":{...}}
📋 Token: harmony_test_token_...
👤 用户: HarmonyOS用户
🏢 公司: HarmonyOS公司
```

## 业务页面适配

现有的业务页面无需修改，因为：

1. **对象名统一**: 业务页面使用 `ddbes_web` 对象
2. **方法名一致**: `client_isAlready` 等方法名保持不变
3. **参数格式相同**: JSON字符串格式与Android一致
4. **回调机制兼容**: `updateClientInfo` 回调与Android行为一致

## 预期效果

修复后，HarmonyOS的WebView JavaScript调用应该与Android/iOS完全一致：

1. ✅ **对象注入正常**: `ddbes_web` 对象正确注入
2. ✅ **方法调用成功**: `client_isAlready` 等方法正常响应
3. ✅ **回调机制工作**: `updateClientInfo` 正确回调客户端信息
4. ✅ **业务页面兼容**: 现有页面无需修改即可正常工作

## 后续优化

1. **真实数据集成**: 将模拟的token、用户信息替换为真实数据
2. **错误处理完善**: 添加更完善的异常处理机制
3. **性能优化**: 优化JavaScript注入和回调的性能
4. **日志管理**: 在生产环境中适当减少调试日志

通过这些修复，HarmonyOS的WebView JavaScript调用应该能够与Android/iOS保持完全一致的行为。
