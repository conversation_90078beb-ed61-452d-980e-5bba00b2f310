import { CommonToolbar, ToolbarPresets } from "../../components/CommonToolbar";
import { Session } from "../../data_source/SessionBody";
import { onEvent, sendEvent, unSubscribe } from "../../util/EventBus";
import { log } from "../../util/Log";
import { ChatViewModel } from "./ChatViewModel";
import { MessageEntity } from './MessageEntity';

@Entry
@ComponentV2
export struct ChatPage {
  vm: ChatViewModel = new ChatViewModel();

  aboutToAppear(): void {
    const params = this.getUIContext().getRouter().getParams() as Session;
    this.vm.session = params;
    onEvent(this.getUIContext(), "a", (x) => {
      log(`ddddddd ${x}`)
    })
    this.vm.fetchIMConfig();

    this.vm.init()



  }

  aboutToDisappear(): void {

  }


  onPageShow(): void {
  }

  // 构建消息气泡
  @Builder
  MessageBubble(msg: MessageEntity) {
    Row() {
      if (msg.sender === 'other') {
        // 对方头像
        Image(this.vm.session?.avatar || '/resources/base/media/im_newsHome_search.png')
          .width(40)
          .height(40)
          .borderRadius(20)
          .margin({ right: 8 })

        // 对方消息气泡
        Column() {
          Text(msg.content)
            .fontSize(16)
            .fontColor('#333')
            .padding({ left: 12, right: 12, top: 10, bottom: 10 })
            .backgroundColor('#FFFFFF')
            .borderRadius(12)
            .shadow({ radius: 2, color: '#00000010', offsetX: 0, offsetY: 1 })
            .maxLines(999)
            .wordBreak(WordBreak.BREAK_ALL)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        Blank()
      } else {
        // 我的消息布局
        Blank()

        // 我的消息气泡
        Column() {
          Text(msg.content)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .padding({ left: 12, right: 12, top: 10, bottom: 10 })
            .backgroundColor('#07C160')
            .borderRadius(12)
            .shadow({ radius: 2, color: '#00000010', offsetX: 0, offsetY: 1 })
            .maxLines(999)
            .wordBreak(WordBreak.BREAK_ALL)
        }
        .alignItems(HorizontalAlign.End)
        .layoutWeight(1)

        // 我的头像
        Image('/resources/base/media/im_newsHome_search.png')
          .width(40)
          .height(40)
          .borderRadius(20)
          .margin({ left: 8 })
      }
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 6, bottom: 6 })
    .alignItems(VerticalAlign.Top)
  }

  build() {
    Column() {
      CommonToolbar({ config: ToolbarPresets.simple(`与${this.vm.session?.name ?? ''}的聊天`) })

      // 聊天消息区
      List({ scroller: this.vm.listScroller }) {
        ForEach(this.vm.messages, (msg: MessageEntity, idx: number) => {
          ListItem() {
            this.MessageBubble(msg)
          }
        }, (msg: MessageEntity, idx: number) => `${idx}_${msg.content}_${msg.sender}`)
      }
      .layoutWeight(1)
      .backgroundColor('#EDEDED')
      .width('100%')
      .scrollBar(BarState.Off)
      .onScrollIndex((start: number, end: number) => {
        // 滚动到底部时的处理
      })

      // 底部输入区
      Column() {
        Divider()
          .color('#E5E5E5')
          .strokeWidth(0.5)

        Row() {
          // 语音按钮
          Image('/resources/base/media/im_newsHome_search.png')
            .width(32)
            .height(32)
            .margin({ right: 8 })
            .onClick(() => {
              // TODO: 语音功能
            })

          // 输入框
          TextInput({
            placeholder: '请输入消息...',
            text: $$this.vm.input
          })
            .layoutWeight(1)
            .fontSize(16)
            .backgroundColor('#F7F7F7')
            .borderRadius(20)
            .padding({ left: 16, right: 16, top: 8, bottom: 8 })
            .border({ width: 1, color: '#E5E5E5' })
            .onSubmit(() => {
              this.vm.sendMsg()
            })

          // 表情按钮
          Image('/resources/base/media/im_newsHome_search.png')
            .width(32)
            .height(32)
            .margin({ left: 8, right: 8 })
            .onClick(() => {
              // TODO: 表情功能
            })

          // 发送按钮
          if (this.vm.input.trim().length > 0) {
            Button('发送')
              .onClick(() => this.vm.sendMsg())
              .fontSize(14)
              .backgroundColor('#07C160')
              .fontColor('#FFFFFF')
              .borderRadius(16)
              .padding({ left: 16, right: 16, top: 6, bottom: 6 })
              .height(32)
          } else {
            Image('/resources/base/media/im_newsHome_search.png')
              .width(32)
              .height(32)
              .onClick(() => {
                // TODO: 更多功能
              })
          }
        }
        .padding({ left: 16, right: 16, top: 8, bottom: 8 })
        .alignItems(VerticalAlign.Center)
      }
      .backgroundColor('#FFFFFF')
      .width('100%')
    }
    .backgroundColor('#EDEDED')
    .width('100%')
    .height('100%')
  }
}