import { Context } from '@kit.AbilityKit';
import { DaoSession, AbstractDao } from '@ohos/dataorm';
import { DataOrmHelper } from './DataOrmHelper';
import { SessionEntity } from '../entities/SessionEntity';
import { TaskEntity } from '../entities/TaskEntity';
import { WorkItemEntity } from '../entities/WorkItemEntity';
import { Session, Task, WorkItem } from '../data_source/SessionBody';
import { UserEntity } from '../entities/UserEntity';
import { ToolCacheEntity } from '../entities/ToolCacheEntity';
import { ToolGridItem } from '../models/ToolModels';

/**
 * dataORM数据服务类
 * 提供统一的数据访问接口
 */
export class DataOrmService {
  private static instance: DataOrmService;
  private daoSession: DaoSession | null = null;
  private sessionDao: AbstractDao<SessionEntity, number> | null = null;
  private taskDao: AbstractDao<TaskEntity, string> | null = null;
  private workItemDao: AbstractDao<WorkItemEntity, string> | null = null;
  private userDao: AbstractDao<UserEntity, string> | null = null;
  private toolCacheDao: AbstractDao<ToolCacheEntity, string> | null = null;
  private isInitialized: boolean = false;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): DataOrmService {
    if (!DataOrmService.instance) {
      DataOrmService.instance = new DataOrmService();
    }
    return DataOrmService.instance;
  }

  /**
   * 初始化数据服务
   */
  public async initialize(context: Context): Promise<void> {
    if (this.isInitialized) {
      console.info('DataOrmService: 已经初始化，跳过');
      return;
    }

    try {
      console.info('DataOrmService: 开始初始化');
      
      const helper = new DataOrmHelper(context);
      this.daoSession = await helper.initializeDatabase(context);
      
      // 获取各个DAO
      this.sessionDao = this.daoSession.getBaseDao(SessionEntity) as AbstractDao<SessionEntity, number>;
      this.taskDao = this.daoSession.getBaseDao(TaskEntity) as AbstractDao<TaskEntity, string>;
      this.workItemDao = this.daoSession.getBaseDao(WorkItemEntity) as AbstractDao<WorkItemEntity, string>;
      this.userDao = this.daoSession.getBaseDao(UserEntity) as AbstractDao<UserEntity, string>;
      this.toolCacheDao = this.daoSession.getBaseDao(ToolCacheEntity) as AbstractDao<ToolCacheEntity, string>;

      
      this.isInitialized = true;
      console.info('DataOrmService: 初始化完成');
    } catch (error) {
      console.error('DataOrmService: 初始化失败', error);
      throw new Error(`数据服务初始化失败: ${error}`);
    }
  }

  /**
   * 检查是否已初始化
   */
  private checkInitialized(): void {
    if (!this.isInitialized || !this.daoSession) {
      throw new Error('DataOrmService未初始化，请先调用initialize方法');
    }
  }

  // ==================== Session相关操作 ====================

  /**
   * 获取所有会话
   */
  public async getAllSessions(): Promise<Session[]> {
    this.checkInitialized();
    try {
      const entities = await this.sessionDao!.loadAll();

      return entities.map(entity => entity.toSession());
    } catch (error) {
      console.error('DataOrmService: 获取所有会话失败', error);
      return [];
    }
  }

  /**
   * 插入会话
   */
  public async insertSession(session: Session): Promise<boolean> {
    this.checkInitialized();
    try {
      const entity = SessionEntity.fromSession(session);
      await this.sessionDao!.insert(entity);
      return true;
    } catch (error) {
      console.error('DataOrmService: 插入会话失败', error);
      return false;
    }
  }

  /**
   * 更新会话未读数
   */
  public async updateSessionUnread(sessionName: string, unread: number): Promise<boolean> {
    this.checkInitialized();
    try {
      // 查询所有会话，然后过滤
      const entities = await this.sessionDao!.loadAll();
      const targetEntity = entities.find(entity => entity.getName() === sessionName);

      if (targetEntity) {
        targetEntity.setUnread(unread);
        await this.sessionDao!.update(targetEntity);
        return true;
      }
      return false;
    } catch (error) {
      console.error('DataOrmService: 更新会话未读数失败', error);
      return false;
    }
  }

  /**
   * 设置会话置顶状态
   */
  public async setSessionTop(sessionName: string, isTop: boolean): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.sessionDao!.loadAll();
      const targetEntity = entities.find(entity => entity.getName() === sessionName);

      if (targetEntity) {
        targetEntity.setIsTop(isTop);
        await this.sessionDao!.update(targetEntity);
        return true;
      }
      return false;
    } catch (error) {
      console.error('DataOrmService: 设置会话置顶状态失败', error);
      return false;
    }
  }

  /**
   * 删除会话
   */
  public async deleteSession(sessionName: string): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.sessionDao!.loadAll();
      const targetEntity = entities.find(entity => entity.getName() === sessionName);

      if (targetEntity && targetEntity.getId() !== undefined) {
        await this.sessionDao!.deleteByKey(targetEntity.getId()!);
        return true;
      }
      return false;
    } catch (error) {
      console.error('DataOrmService: 删除会话失败', error);
      return false;
    }
  }

  /**
   * 获取会话总未读数
   */
  public async getTotalUnreadCount(): Promise<number> {
    this.checkInitialized();
    try {
      const entities = await this.sessionDao!.loadAll();
      return entities.reduce((total, entity) => total + entity.getUnread(), 0);
    } catch (error) {
      console.error('DataOrmService: 获取总未读数失败', error);
      return 0;
    }
  }

  // ==================== User 相关操作 ====================
  /**
   * 获取所有任务
   */
  public async getAllUsers(): Promise<UserEntity | null> {
    this.checkInitialized();
    try {
      const entities = await this.userDao!.loadAll();

      return entities[0]
    } catch (error) {
      return null;
    }
  }

  /**
   * 插入任务
   */
  public async insertUser(user: UserEntity): Promise<boolean> {
    this.checkInitialized();
    try {
      await this.userDao!.insert(user);
      return true;
    } catch (error) {
      console.error('DataOrmService: 插入user失败', error);
      return false;
    }
  }



  // ==================== Task相关操作 ====================

  /**
   * 获取所有任务
   */
  public async getAllTasks(): Promise<Task[]> {
    this.checkInitialized();
    try {
      const entities = await this.taskDao!.loadAll();

      return entities.map(entity => entity.toTask());
    } catch (error) {
      console.error('DataOrmService: 获取所有任务失败', error);
      return [];
    }
  }

  /**
   * 插入任务
   */
  public async insertTask(task: Task): Promise<boolean> {
    this.checkInitialized();
    try {
      const entity = TaskEntity.fromTask(task);
      await this.taskDao!.insert(entity);
      return true;
    } catch (error) {
      console.error('DataOrmService: 插入任务失败', error);
      return false;
    }
  }

  /**
   * 更新任务状态
   */
  public async updateTaskStatus(taskId: string, status: 'pending' | 'in-progress' | 'completed'): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.taskDao!.loadAll();
      const targetEntity = entities.find(entity => entity.getId() === taskId);

      if (targetEntity) {
        targetEntity.setStatus(status);
        await this.taskDao!.update(targetEntity);
        return true;
      }
      return false;
    } catch (error) {
      console.error('DataOrmService: 更新任务状态失败', error);
      return false;
    }
  }

  // ==================== WorkItem相关操作 ====================

  /**
   * 获取所有工作台应用
   */
  public async getAllWorkItems(): Promise<WorkItem[]> {
    this.checkInitialized();
    try {
      const entities = await this.workItemDao!.loadAll();

      return entities.map(entity => entity.toWorkItem());
    } catch (error) {
      console.error('DataOrmService: 获取所有工作台应用失败', error);
      return [];
    }
  }

  /**
   * 插入工作台应用
   */
  public async insertWorkItem(workItem: WorkItem): Promise<boolean> {
    this.checkInitialized();
    try {
      const entity = WorkItemEntity.fromWorkItem(workItem);
      await this.workItemDao!.insert(entity);
      return true;
    } catch (error) {
      console.error('DataOrmService: 插入工作台应用失败', error);
      return false;
    }
  }

  /**
   * 更新工作台应用徽章
   */
  public async updateWorkItemBadge(workItemId: string, badge: number): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.workItemDao!.loadAll();
      const targetEntity = entities.find(entity => entity.getId() === workItemId);

      if (targetEntity) {
        targetEntity.setBadge(badge);
        await this.workItemDao!.update(targetEntity);
        return true;
      }
      return false;
    } catch (error) {
      console.error('DataOrmService: 更新工作台应用徽章失败', error);
      return false;
    }
  }

  /**
   * 测试数据库连接和数据
   */
  public async testDatabase(): Promise<void> {
    this.checkInitialized();
    try {
      console.info('DataOrmService: 开始测试数据库');

      const sessions = await this.getAllSessions();
      console.info(`DataOrmService: 测试结果 - 会话数量: ${sessions.length}`);

      const tasks = await this.getAllTasks();
      console.info(`DataOrmService: 测试结果 - 任务数量: ${tasks.length}`);

      const workItems = await this.getAllWorkItems();
      console.info(`DataOrmService: 测试结果 - 工作台应用数量: ${workItems.length}`);

      const users = await this.getAllUsers();
      console.info(`DataOrmService: 测试结果 - 用户数据: ${users ? users.getUid() : '无'}`);

      const unreadCount = await this.getTotalUnreadCount();
      console.info(`DataOrmService: 测试结果 - 总未读数: ${unreadCount}`);

      // 测试插入新用户
      const testUser = new UserEntity('test_user', '测试用户', 'test_token');
      const insertResult = await this.insertUser(testUser);
      console.info(`DataOrmService: 测试插入用户结果: ${insertResult}`);

      console.info('DataOrmService: 数据库测试完成');
    } catch (error) {
      console.error('DataOrmService: 数据库测试失败', error);
    }
  }

  // ==================== ToolCache相关操作 ====================

  /**
   * 获取缓存的工具列表
   */
  public async getCachedTools(companyId: string): Promise<ToolGridItem[]> {
    this.checkInitialized();
    try {
      const entities = await this.toolCacheDao!.loadAll();
      const filteredEntities = entities.filter(entity => entity.getCompanyId() === companyId);

      return filteredEntities.map(entity => entity.toToolGridItem());
    } catch (error) {
      console.error('DataOrmService: 获取缓存工具失败', error);
      return [];
    }
  }

  /**
   * 缓存工具列表
   */
  public async cacheTools(tools: ToolGridItem[], companyId: string): Promise<boolean> {
    this.checkInitialized();
    try {
      // 先清除该公司的旧缓存
      await this.clearToolCache(companyId);

      // 插入新的缓存数据
      for (const tool of tools) {
        const entity = ToolCacheEntity.fromToolGridItem(tool, companyId);
        await this.toolCacheDao!.insert(entity);
      }

      console.info(`DataOrmService: 成功缓存${tools.length}个工具，公司ID: ${companyId}`);
      return true;
    } catch (error) {
      console.error('DataOrmService: 缓存工具失败', error);
      return false;
    }
  }

  /**
   * 清除工具缓存
   */
  public async clearToolCache(companyId: string): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.toolCacheDao!.loadAll();
      const toDelete = entities.filter(entity => entity.getCompanyId() === companyId);

      for (const entity of toDelete) {
        await this.toolCacheDao!.deleteByKey(entity.getKey());
      }

      console.info(`DataOrmService: 清除了${toDelete.length}个工具缓存，公司ID: ${companyId}`);
      return true;
    } catch (error) {
      console.error('DataOrmService: 清除工具缓存失败', error);
      return false;
    }
  }

  /**
   * 更新工具徽章数
   */
  public async updateToolBadge(toolKey: string, companyId: string, badge: number): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.toolCacheDao!.loadAll();
      const targetEntity = entities.find(entity =>
        entity.getKey() === toolKey && entity.getCompanyId() === companyId
      );

      if (targetEntity) {
        targetEntity.setBadge(badge);
        await this.toolCacheDao!.update(targetEntity);
        return true;
      }
      return false;
    } catch (error) {
      console.error('DataOrmService: 更新工具徽章失败', error);
      return false;
    }
  }

  /**
   * 检查工具缓存是否存在
   */
  public async hasToolCache(companyId: string): Promise<boolean> {
    this.checkInitialized();
    try {
      const entities = await this.toolCacheDao!.loadAll();
      return entities.some(entity => entity.getCompanyId() === companyId);
    } catch (error) {
      console.error('DataOrmService: 检查工具缓存失败', error);
      return false;
    }
  }

  /**
   * 关闭数据服务
   */
  public async close(): Promise<void> {
    if (this.isInitialized && this.daoSession) {
      // dataORM会自动管理连接，这里只需要重置状态
      this.daoSession = null;
      this.sessionDao = null;
      this.taskDao = null;
      this.workItemDao = null;
      this.userDao = null;
      this.toolCacheDao = null;
      this.isInitialized = false;
      console.info('DataOrmService: 数据服务已关闭');
    }
  }
}

// 导出单例实例
export const dataOrmService = DataOrmService.getInstance();
