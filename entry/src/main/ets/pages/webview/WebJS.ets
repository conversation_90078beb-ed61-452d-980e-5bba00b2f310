import { log } from '../../util/Log';
import { toast } from '../../util/Toast';
import { router } from '@kit.ArkUI';
import web_webview from '@ohos.web.webview'
import { JSON } from '@kit.ArkTS';
import { ClientIsAlreadyBody } from './WebParamEntity';

// 定义Token数据接口
interface TokenData {
  token: string;
  expireTime: number;
}

// 定义用户数据接口
interface UserData {
  userId: string;
  userName: string;
  avatar: string;
}

// 定义客户端信息接口
interface ClientUserInfo {
  userId: string;
  userName: string;
  avatar: string;
}

interface ClientCompanyInfo {
  companyId: string;
  companyName: string;
  orgId: string;
}

interface ClientInfo {
  token: string;
  user: ClientUserInfo;
  company: ClientCompanyInfo;
  statusHeight: number;
}

// 定义响应数据接口
interface ResponseData {
  success: boolean;
  message: string;
  data?: TokenData | UserData;
}

// 基础配置 - 与Android保持一致
// 注入时使用的对象名（对应Android的ConsKeys.JS_TRANS_PORT）
export const BASE_WIN_OBJ = 'DDBESOFFICE';
// 默认的回调对象名（对应Android的初始objName）
export const DEFAULT_CALLBACK_OBJ = 'ddbes_web';

export class JsProxy {

  // 可变 - 对应Android的objName变量，初始值为默认回调对象名
  currentObj: string = DEFAULT_CALLBACK_OBJ
  controller?: web_webview.WebviewController

  constructor(controller: web_webview.WebviewController) {
    log(`🚀 JsProxy 开始初始化...`);
    log(`📱 控制器状态: ${controller ? '已提供' : '未提供'}`);
    this.controller = controller;
    log(`🎯 JavaScript对象名: ${this.currentObj}`);
    log(`✅ JsProxy 构造完成`);

    // 添加方法存在性检查
    this.logAvailableMethods();
  }

  // 记录可用方法
  private logAvailableMethods() {
    log(`📋 JsProxy 可用方法列表:`);
    JSMethodArray.forEach((method, index) => {
      const methodExists = typeof (this as object)[method] === 'function';
      log(`  ${index + 1}. ${method}: ${methodExists ? '✅ 存在' : '❌ 不存在'}`);
    });
  }

  public client_isAlready(message: string) {
    log(`🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉`);
    log(`📨 接收到的消息: ${message}`);
    log(`⏰ 调用时间: ${new Date().toLocaleString()}`);

    try {
      let o = JSON.parse(message) as ClientIsAlreadyBody;
      log(`解析后的数据: ${JSON.stringify(o)}`);

      if (o) {
        // 处理接收到的数据
        if (o.title) {
          log(`页面标题: ${o.title}`);
        }
        if (o.subTitle) {
          log(`页面副标题: ${o.subTitle}`);
        }
        if (o.objName) {
          log(`对象名称: ${o.objName}`);
          // 更新当前对象名，与Android保持一致（对应Android第1285-1286行）
          if (o.objName.trim() !== '') {
            this.currentObj = o.objName;
            log(`🔄 动态更新回调对象名为: ${this.currentObj}`);
          }
        }
        if (o.useTitleBar !== undefined) {
          log(`使用标题栏: ${o.useTitleBar}`);
        }

        // 模拟Android的行为：web初始化完成后，回调token，用户信息，当前团队信息到web页面
        this.updateClientInfo();

        // 向JavaScript返回成功响应
        const successResponse: ResponseData = { success: true, message: '数据接收成功' };
        this.sendResponseToJS('client_isAlready', successResponse);
      }
    } catch (error) {
      log(`client_isAlready 解析消息失败: ${error}`);
      // 向JavaScript返回错误响应
      const errorResponse: ResponseData = { success: false, message: '数据解析失败' };
      this.sendResponseToJS('client_isAlready', errorResponse);
    }
  }

  public client_getToken(message: string) {
    log(`=== client_getToken 方法被调用 ===`);
    log(`接收到的消息: ${message}`);

    // 这里可以返回实际的token
    const responseData: TokenData = {
      token: 'your_token_here',
      expireTime: Date.now() + 3600000 // 1小时后过期
    };

    const response: ResponseData = {
      success: true,
      message: 'Token获取成功',
      data: responseData
    };
    this.sendResponseToJS('client_getToken', response);
  }

  public client_getUserInfo(message: string) {
    log(`=== client_getUserInfo 方法被调用 ===`);
    log(`接收到的消息: ${message}`);

    // 这里可以返回实际的用户信息
    const responseData: UserData = {
      userId: 'user123',
      userName: '用户名',
      avatar: 'avatar_url'
    };

    const response: ResponseData = {
      success: true,
      message: '用户信息获取成功',
      data: responseData
    };
    this.sendResponseToJS('client_getUserInfo', response);
  }

  public client_goBack(message: string) {
    log(`=== client_goBack 方法被调用 ===`);
    log(`接收到的消息: ${message}`);

    try {
      router.back();
      const successResponse: ResponseData = { success: true, message: '页面返回成功' };
      this.sendResponseToJS('client_goBack', successResponse);
    } catch (error) {
      log(`页面返回失败: ${error}`);
      const errorResponse: ResponseData = { success: false, message: '页面返回失败' };
      this.sendResponseToJS('client_goBack', errorResponse);
    }
  }

  public client_popBack(message: string) {
    log(`=== client_popBack 方法被调用 ===`);
    log(`接收到的消息: ${message}`);

    try {
      if (this.controller) {
        if (this.controller.accessBackward()) {
          this.controller.backward();
          const webViewResponse: ResponseData = { success: true, message: 'WebView返回成功' };
          this.sendResponseToJS('client_popBack', webViewResponse);
        } else {
          router.back();
          const pageResponse: ResponseData = { success: true, message: '页面返回成功' };
          this.sendResponseToJS('client_popBack', pageResponse);
        }
      } else {
        router.back();
        const fallbackResponse: ResponseData = { success: true, message: '页面返回成功' };
        this.sendResponseToJS('client_popBack', fallbackResponse);
      }
    } catch (error) {
      log(`返回操作失败: ${error}`);
      const errorResponse: ResponseData = { success: false, message: '返回操作失败' };
      this.sendResponseToJS('client_popBack', errorResponse);
    }
  }

  // 向JavaScript发送响应
  private sendResponseToJS(methodName: string, response: ResponseData): void {
    try {
      if (this.controller) {
        const script = `
          (function() {
            // 如果页面定义了回调函数，则调用
            if (typeof window.${this.currentObj}_callback === 'function') {
              window.${this.currentObj}_callback('${methodName}', ${JSON.stringify(response)});
            }

            // 触发自定义事件
            if (typeof window.dispatchEvent === 'function') {
              var event = new CustomEvent('${this.currentObj}_${methodName}_response', {
                detail: ${JSON.stringify(response)}
              });
              window.dispatchEvent(event);
            }

            console.log('${methodName} 响应已发送:', ${JSON.stringify(response)});
          })();
        `;

        this.controller.runJavaScript(script)
          .then(() => {
            log(`${methodName} 响应发送成功`);
          })
          .catch((error: Error) => {
            log(`${methodName} 响应发送失败: ${error.message}`);
          });
      }
    } catch (error) {
      log(`发送响应到JavaScript失败: ${error}`);
    }
  }

  // 模拟Android的updateClientInfo方法
  private updateClientInfo(): void {
    try {
      log(`📤 开始向Web页面回调客户端信息...`);

      // 构建客户端信息，模拟Android的实现
      const userInfo: ClientUserInfo = {
        userId: 'harmony_user_123',
        userName: 'HarmonyOS用户',
        avatar: 'https://example.com/avatar.jpg'
      };

      const companyInfo: ClientCompanyInfo = {
        companyId: 'harmony_company_456',
        companyName: 'HarmonyOS公司',
        orgId: 'harmony_org_789'
      };

      const clientInfo: ClientInfo = {
        token: 'harmony_test_token_' + Date.now(), // 模拟token
        user: userInfo,
        company: companyInfo,
        statusHeight: 44 // 模拟状态栏高度
      };

      log(`📋 客户端信息: ${JSON.stringify(clientInfo)}`);

      // 调用Web页面的updateClientInfo方法，与Android保持一致
      if (this.controller) {
        const script = `
          (function() {
            if (typeof window.${this.currentObj} !== 'undefined' &&
                typeof window.${this.currentObj}.updateClientInfo === 'function') {
              console.log('📤 调用Web页面的updateClientInfo方法');
              window.${this.currentObj}.updateClientInfo('${JSON.stringify(clientInfo)}');
              return 'UPDATE_CLIENT_INFO_SUCCESS';
            } else {
              console.log('❌ Web页面的updateClientInfo方法不存在');
              return 'UPDATE_CLIENT_INFO_FAILED';
            }
          })();
        `;

        this.controller.runJavaScript(script)
          .then((result) => {
            log(`📤 updateClientInfo调用结果: ${result}`);
          })
          .catch((error: Error) => {
            log(`📤 updateClientInfo调用失败: ${error.message}`);
          });
      }
    } catch (error) {
      log(`❌ updateClientInfo执行失败: ${error}`);
    }
  }

}

export function callJS(controller: web_webview.WebviewController, obj: string, method: string): void {
  controller.runJavaScript(`javascript:window.${obj}.${method}()`);
}

export const JSMethodArray: string[] = [
  "client_isAlready",
  "client_getToken",
  "client_getUserInfo",
  "client_goBack",
  "client_popBack",
];

