// import * as $protobuf from "protobufjs";
import * as $protobuf from "@ohos/protobufjs";
import Long = require("long");
/** Namespace com. */
export namespace com {

    /** Namespace joinu. */
    namespace joinu {

        /** Namespace im. */
        namespace im {

            /** Namespace protobuf. */
            namespace protobuf {

                /** Properties of an ImMsg. */
                interface IImMsg {

                    /** ImMsg msgId */
                    msgId?: (string|null);

                    /** ImMsg cmdId */
                    cmdId?: (string|null);

                    /** ImMsg type */
                    type?: (number|null);

                    /** ImMsg senderInfo */
                    senderInfo?: (com.joinu.im.protobuf.IUserInfo|null);

                    /** ImMsg msgTime */
                    msgTime?: (number|Long|null);

                    /** ImMsg recordIgnore */
                    recordIgnore?: (boolean|null);

                    /** ImMsg msgContent */
                    msgContent?: (string|null);

                    /** ImMsg selfMsg */
                    selfMsg?: (boolean|null);

                    /** ImMsg customMessage */
                    customMessage?: (string|null);

                    /** ImMsg exception */
                    exception?: (com.joinu.im.protobuf.IException|null);

                    /** ImMsg loginRequest */
                    loginRequest?: (com.joinu.im.protobuf.ILoginRequest|null);

                    /** ImMsg loginResult */
                    loginResult?: (com.joinu.im.protobuf.ILoginResult|null);

                    /** ImMsg c2cMsg */
                    c2cMsg?: (com.joinu.im.protobuf.IC2CMsg|null);

                    /** ImMsg c2cMsgRequest */
                    c2cMsgRequest?: (com.joinu.im.protobuf.IC2CMsgRequest|null);

                    /** ImMsg c2cMsgResponse */
                    c2cMsgResponse?: (com.joinu.im.protobuf.IC2CMsgResponse|null);

                    /** ImMsg groupMsg */
                    groupMsg?: (com.joinu.im.protobuf.IGroupMsg|null);

                    /** ImMsg groupMsgRequest */
                    groupMsgRequest?: (com.joinu.im.protobuf.IGroupMsgRequest|null);

                    /** ImMsg groupMsgResponse */
                    groupMsgResponse?: (com.joinu.im.protobuf.IGroupMsgResponse|null);

                    /** ImMsg pushMsg */
                    pushMsg?: (com.joinu.im.protobuf.IPushMsg|null);

                    /** ImMsg serverMsg */
                    serverMsg?: (com.joinu.im.protobuf.IServerMsg|null);

                    /** ImMsg noticeMsg */
                    noticeMsg?: (com.joinu.im.protobuf.INoticeMsg|null);

                    /** ImMsg ext */
                    ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** ImMsg offline */
                    offline?: (com.joinu.im.protobuf.IMultipleMsg|null);

                    /** ImMsg offlineMsgRequest */
                    offlineMsgRequest?: (com.joinu.im.protobuf.IOfflineMsgRequest|null);

                    /** ImMsg kick */
                    kick?: (com.joinu.im.protobuf.IKickMsg|null);

                    /** ImMsg imPushMsg */
                    imPushMsg?: (com.joinu.im.protobuf.IImPushMsg|null);
                }

                /** Represents an ImMsg. */
                class ImMsg implements IImMsg {

                    /**
                     * Constructs a new ImMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IImMsg);

                    /** ImMsg msgId. */
                    public msgId: string;

                    /** ImMsg cmdId. */
                    public cmdId: string;

                    /** ImMsg type. */
                    public type: number;

                    /** ImMsg senderInfo. */
                    public senderInfo?: (com.joinu.im.protobuf.IUserInfo|null);

                    /** ImMsg msgTime. */
                    public msgTime: (number|Long);

                    /** ImMsg recordIgnore. */
                    public recordIgnore: boolean;

                    /** ImMsg msgContent. */
                    public msgContent: string;

                    /** ImMsg selfMsg. */
                    public selfMsg: boolean;

                    /** ImMsg customMessage. */
                    public customMessage: string;

                    /** ImMsg exception. */
                    public exception?: (com.joinu.im.protobuf.IException|null);

                    /** ImMsg loginRequest. */
                    public loginRequest?: (com.joinu.im.protobuf.ILoginRequest|null);

                    /** ImMsg loginResult. */
                    public loginResult?: (com.joinu.im.protobuf.ILoginResult|null);

                    /** ImMsg c2cMsg. */
                    public c2cMsg?: (com.joinu.im.protobuf.IC2CMsg|null);

                    /** ImMsg c2cMsgRequest. */
                    public c2cMsgRequest?: (com.joinu.im.protobuf.IC2CMsgRequest|null);

                    /** ImMsg c2cMsgResponse. */
                    public c2cMsgResponse?: (com.joinu.im.protobuf.IC2CMsgResponse|null);

                    /** ImMsg groupMsg. */
                    public groupMsg?: (com.joinu.im.protobuf.IGroupMsg|null);

                    /** ImMsg groupMsgRequest. */
                    public groupMsgRequest?: (com.joinu.im.protobuf.IGroupMsgRequest|null);

                    /** ImMsg groupMsgResponse. */
                    public groupMsgResponse?: (com.joinu.im.protobuf.IGroupMsgResponse|null);

                    /** ImMsg pushMsg. */
                    public pushMsg?: (com.joinu.im.protobuf.IPushMsg|null);

                    /** ImMsg serverMsg. */
                    public serverMsg?: (com.joinu.im.protobuf.IServerMsg|null);

                    /** ImMsg noticeMsg. */
                    public noticeMsg?: (com.joinu.im.protobuf.INoticeMsg|null);

                    /** ImMsg ext. */
                    public ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** ImMsg offline. */
                    public offline?: (com.joinu.im.protobuf.IMultipleMsg|null);

                    /** ImMsg offlineMsgRequest. */
                    public offlineMsgRequest?: (com.joinu.im.protobuf.IOfflineMsgRequest|null);

                    /** ImMsg kick. */
                    public kick?: (com.joinu.im.protobuf.IKickMsg|null);

                    /** ImMsg imPushMsg. */
                    public imPushMsg?: (com.joinu.im.protobuf.IImPushMsg|null);

                    /** ImMsg content. */
                    public content?: ("exception"|"loginRequest"|"loginResult"|"c2cMsg"|"c2cMsgRequest"|"c2cMsgResponse"|"groupMsg"|"groupMsgRequest"|"groupMsgResponse"|"pushMsg"|"serverMsg"|"noticeMsg"|"ext"|"offline"|"offlineMsgRequest"|"kick"|"imPushMsg");

                    /**
                     * Creates a new ImMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ImMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IImMsg): com.joinu.im.protobuf.ImMsg;

                    /**
                     * Encodes the specified ImMsg message. Does not implicitly {@link com.joinu.im.protobuf.ImMsg.verify|verify} messages.
                     * @param message ImMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IImMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ImMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.ImMsg.verify|verify} messages.
                     * @param message ImMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IImMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an ImMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ImMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.ImMsg;

                    /**
                     * Decodes an ImMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ImMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.ImMsg;

                    /**
                     * Verifies an ImMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an ImMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ImMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.ImMsg;

                    /**
                     * Creates a plain object from an ImMsg message. Also converts values to other types if specified.
                     * @param message ImMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.ImMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ImMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for ImMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of an ImPushMsg. */
                interface IImPushMsg {

                    /** ImPushMsg blacklistAdd */
                    blacklistAdd?: (string|null);

                    /** ImPushMsg blacklistRemove */
                    blacklistRemove?: (string|null);

                    /** ImPushMsg singleMuteListAdd */
                    singleMuteListAdd?: (string|null);

                    /** ImPushMsg singleMuteListRemove */
                    singleMuteListRemove?: (string|null);

                    /** ImPushMsg groupMuteListAdd */
                    groupMuteListAdd?: (string|null);

                    /** ImPushMsg groupMuteListRemove */
                    groupMuteListRemove?: (string|null);

                    /** ImPushMsg warnMessage */
                    warnMessage?: (com.joinu.im.protobuf.IWarnMessage|null);
                }

                /** Represents an ImPushMsg. */
                class ImPushMsg implements IImPushMsg {

                    /**
                     * Constructs a new ImPushMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IImPushMsg);

                    /** ImPushMsg blacklistAdd. */
                    public blacklistAdd?: (string|null);

                    /** ImPushMsg blacklistRemove. */
                    public blacklistRemove?: (string|null);

                    /** ImPushMsg singleMuteListAdd. */
                    public singleMuteListAdd?: (string|null);

                    /** ImPushMsg singleMuteListRemove. */
                    public singleMuteListRemove?: (string|null);

                    /** ImPushMsg groupMuteListAdd. */
                    public groupMuteListAdd?: (string|null);

                    /** ImPushMsg groupMuteListRemove. */
                    public groupMuteListRemove?: (string|null);

                    /** ImPushMsg warnMessage. */
                    public warnMessage?: (com.joinu.im.protobuf.IWarnMessage|null);

                    /** ImPushMsg content. */
                    public content?: ("blacklistAdd"|"blacklistRemove"|"singleMuteListAdd"|"singleMuteListRemove"|"groupMuteListAdd"|"groupMuteListRemove"|"warnMessage");

                    /**
                     * Creates a new ImPushMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ImPushMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IImPushMsg): com.joinu.im.protobuf.ImPushMsg;

                    /**
                     * Encodes the specified ImPushMsg message. Does not implicitly {@link com.joinu.im.protobuf.ImPushMsg.verify|verify} messages.
                     * @param message ImPushMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IImPushMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ImPushMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.ImPushMsg.verify|verify} messages.
                     * @param message ImPushMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IImPushMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an ImPushMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ImPushMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.ImPushMsg;

                    /**
                     * Decodes an ImPushMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ImPushMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.ImPushMsg;

                    /**
                     * Verifies an ImPushMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an ImPushMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ImPushMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.ImPushMsg;

                    /**
                     * Creates a plain object from an ImPushMsg message. Also converts values to other types if specified.
                     * @param message ImPushMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.ImPushMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ImPushMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for ImPushMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a WarnMessage. */
                interface IWarnMessage {

                    /** WarnMessage msg */
                    msg?: (string|null);

                    /** WarnMessage sessionId */
                    sessionId?: (string|null);

                    /** WarnMessage sessionType */
                    sessionType?: (com.joinu.im.protobuf.WarnMessage.SessionType|null);
                }

                /** Represents a WarnMessage. */
                class WarnMessage implements IWarnMessage {

                    /**
                     * Constructs a new WarnMessage.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IWarnMessage);

                    /** WarnMessage msg. */
                    public msg: string;

                    /** WarnMessage sessionId. */
                    public sessionId: string;

                    /** WarnMessage sessionType. */
                    public sessionType: com.joinu.im.protobuf.WarnMessage.SessionType;

                    /**
                     * Creates a new WarnMessage instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns WarnMessage instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IWarnMessage): com.joinu.im.protobuf.WarnMessage;

                    /**
                     * Encodes the specified WarnMessage message. Does not implicitly {@link com.joinu.im.protobuf.WarnMessage.verify|verify} messages.
                     * @param message WarnMessage message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IWarnMessage, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified WarnMessage message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.WarnMessage.verify|verify} messages.
                     * @param message WarnMessage message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IWarnMessage, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a WarnMessage message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns WarnMessage
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.WarnMessage;

                    /**
                     * Decodes a WarnMessage message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns WarnMessage
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.WarnMessage;

                    /**
                     * Verifies a WarnMessage message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a WarnMessage message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns WarnMessage
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.WarnMessage;

                    /**
                     * Creates a plain object from a WarnMessage message. Also converts values to other types if specified.
                     * @param message WarnMessage
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.WarnMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this WarnMessage to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for WarnMessage
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                namespace WarnMessage {

                    /** SessionType enum. */
                    enum SessionType {
                        NULL = 0,
                        C2C = 1,
                        GROUP = 2
                    }
                }

                /** Properties of a KickMsg. */
                interface IKickMsg {

                    /** KickMsg kickType */
                    kickType?: (com.joinu.im.protobuf.KickMsg.KickType|null);
                }

                /** Represents a KickMsg. */
                class KickMsg implements IKickMsg {

                    /**
                     * Constructs a new KickMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IKickMsg);

                    /** KickMsg kickType. */
                    public kickType: com.joinu.im.protobuf.KickMsg.KickType;

                    /**
                     * Creates a new KickMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns KickMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IKickMsg): com.joinu.im.protobuf.KickMsg;

                    /**
                     * Encodes the specified KickMsg message. Does not implicitly {@link com.joinu.im.protobuf.KickMsg.verify|verify} messages.
                     * @param message KickMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IKickMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified KickMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.KickMsg.verify|verify} messages.
                     * @param message KickMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IKickMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a KickMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns KickMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.KickMsg;

                    /**
                     * Decodes a KickMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns KickMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.KickMsg;

                    /**
                     * Verifies a KickMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a KickMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns KickMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.KickMsg;

                    /**
                     * Creates a plain object from a KickMsg message. Also converts values to other types if specified.
                     * @param message KickMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.KickMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this KickMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for KickMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                namespace KickMsg {

                    /** KickType enum. */
                    enum KickType {
                        NULL = 0,
                        OtherDeviceLogin = 1
                    }
                }

                /** Properties of an OfflineMsgRequest. */
                interface IOfflineMsgRequest {

                    /** OfflineMsgRequest offlineTime */
                    offlineTime?: (number|Long|null);

                    /** OfflineMsgRequest targetUserId */
                    targetUserId?: (string[]|null);

                    /** OfflineMsgRequest groupId */
                    groupId?: (string[]|null);
                }

                /** Represents an OfflineMsgRequest. */
                class OfflineMsgRequest implements IOfflineMsgRequest {

                    /**
                     * Constructs a new OfflineMsgRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IOfflineMsgRequest);

                    /** OfflineMsgRequest offlineTime. */
                    public offlineTime: (number|Long);

                    /** OfflineMsgRequest targetUserId. */
                    public targetUserId: string[];

                    /** OfflineMsgRequest groupId. */
                    public groupId: string[];

                    /**
                     * Creates a new OfflineMsgRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns OfflineMsgRequest instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IOfflineMsgRequest): com.joinu.im.protobuf.OfflineMsgRequest;

                    /**
                     * Encodes the specified OfflineMsgRequest message. Does not implicitly {@link com.joinu.im.protobuf.OfflineMsgRequest.verify|verify} messages.
                     * @param message OfflineMsgRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IOfflineMsgRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified OfflineMsgRequest message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.OfflineMsgRequest.verify|verify} messages.
                     * @param message OfflineMsgRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IOfflineMsgRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an OfflineMsgRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns OfflineMsgRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.OfflineMsgRequest;

                    /**
                     * Decodes an OfflineMsgRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns OfflineMsgRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.OfflineMsgRequest;

                    /**
                     * Verifies an OfflineMsgRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an OfflineMsgRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns OfflineMsgRequest
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.OfflineMsgRequest;

                    /**
                     * Creates a plain object from an OfflineMsgRequest message. Also converts values to other types if specified.
                     * @param message OfflineMsgRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.OfflineMsgRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this OfflineMsgRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for OfflineMsgRequest
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a MultipleMsg. */
                interface IMultipleMsg {

                    /** MultipleMsg msg */
                    msg?: (com.joinu.im.protobuf.IImMsg[]|null);
                }

                /** Represents a MultipleMsg. */
                class MultipleMsg implements IMultipleMsg {

                    /**
                     * Constructs a new MultipleMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IMultipleMsg);

                    /** MultipleMsg msg. */
                    public msg: com.joinu.im.protobuf.IImMsg[];

                    /**
                     * Creates a new MultipleMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns MultipleMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IMultipleMsg): com.joinu.im.protobuf.MultipleMsg;

                    /**
                     * Encodes the specified MultipleMsg message. Does not implicitly {@link com.joinu.im.protobuf.MultipleMsg.verify|verify} messages.
                     * @param message MultipleMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IMultipleMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified MultipleMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.MultipleMsg.verify|verify} messages.
                     * @param message MultipleMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IMultipleMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a MultipleMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns MultipleMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.MultipleMsg;

                    /**
                     * Decodes a MultipleMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns MultipleMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.MultipleMsg;

                    /**
                     * Verifies a MultipleMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a MultipleMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns MultipleMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.MultipleMsg;

                    /**
                     * Creates a plain object from a MultipleMsg message. Also converts values to other types if specified.
                     * @param message MultipleMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.MultipleMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this MultipleMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for MultipleMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a NoticeMsg. */
                interface INoticeMsg {

                    /** NoticeMsg title */
                    title?: (string|null);

                    /** NoticeMsg subtitle */
                    subtitle?: (string|null);

                    /** NoticeMsg context */
                    context?: (string|null);

                    /** NoticeMsg data */
                    data?: (string|null);

                    /** NoticeMsg ext */
                    ext?: (string|null);

                    /** NoticeMsg tempStatus */
                    tempStatus?: (number|null);
                }

                /** Represents a NoticeMsg. */
                class NoticeMsg implements INoticeMsg {

                    /**
                     * Constructs a new NoticeMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.INoticeMsg);

                    /** NoticeMsg title. */
                    public title: string;

                    /** NoticeMsg subtitle. */
                    public subtitle: string;

                    /** NoticeMsg context. */
                    public context: string;

                    /** NoticeMsg data. */
                    public data: string;

                    /** NoticeMsg ext. */
                    public ext: string;

                    /** NoticeMsg tempStatus. */
                    public tempStatus: number;

                    /**
                     * Creates a new NoticeMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns NoticeMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.INoticeMsg): com.joinu.im.protobuf.NoticeMsg;

                    /**
                     * Encodes the specified NoticeMsg message. Does not implicitly {@link com.joinu.im.protobuf.NoticeMsg.verify|verify} messages.
                     * @param message NoticeMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.INoticeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified NoticeMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.NoticeMsg.verify|verify} messages.
                     * @param message NoticeMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.INoticeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a NoticeMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns NoticeMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.NoticeMsg;

                    /**
                     * Decodes a NoticeMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns NoticeMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.NoticeMsg;

                    /**
                     * Verifies a NoticeMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a NoticeMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns NoticeMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.NoticeMsg;

                    /**
                     * Creates a plain object from a NoticeMsg message. Also converts values to other types if specified.
                     * @param message NoticeMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.NoticeMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this NoticeMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for NoticeMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a ServerMsg. */
                interface IServerMsg {

                    /** ServerMsg noticeMsgUpdated */
                    noticeMsgUpdated?: (com.joinu.im.protobuf.IImMsg|null);
                }

                /** Represents a ServerMsg. */
                class ServerMsg implements IServerMsg {

                    /**
                     * Constructs a new ServerMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IServerMsg);

                    /** ServerMsg noticeMsgUpdated. */
                    public noticeMsgUpdated?: (com.joinu.im.protobuf.IImMsg|null);

                    /**
                     * Creates a new ServerMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ServerMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IServerMsg): com.joinu.im.protobuf.ServerMsg;

                    /**
                     * Encodes the specified ServerMsg message. Does not implicitly {@link com.joinu.im.protobuf.ServerMsg.verify|verify} messages.
                     * @param message ServerMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IServerMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ServerMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.ServerMsg.verify|verify} messages.
                     * @param message ServerMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IServerMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a ServerMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ServerMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.ServerMsg;

                    /**
                     * Decodes a ServerMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ServerMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.ServerMsg;

                    /**
                     * Verifies a ServerMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a ServerMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ServerMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.ServerMsg;

                    /**
                     * Creates a plain object from a ServerMsg message. Also converts values to other types if specified.
                     * @param message ServerMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.ServerMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ServerMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for ServerMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of an Exception. */
                interface IException {

                    /** Exception msg */
                    msg?: (string|null);

                    /** Exception requestName */
                    requestName?: (number|null);
                }

                /** Represents an Exception. */
                class Exception implements IException {

                    /**
                     * Constructs a new Exception.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IException);

                    /** Exception msg. */
                    public msg: string;

                    /** Exception requestName. */
                    public requestName: number;

                    /**
                     * Creates a new Exception instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Exception instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IException): com.joinu.im.protobuf.Exception;

                    /**
                     * Encodes the specified Exception message. Does not implicitly {@link com.joinu.im.protobuf.Exception.verify|verify} messages.
                     * @param message Exception message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IException, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Exception message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.Exception.verify|verify} messages.
                     * @param message Exception message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IException, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an Exception message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Exception
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.Exception;

                    /**
                     * Decodes an Exception message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Exception
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.Exception;

                    /**
                     * Verifies an Exception message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an Exception message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Exception
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.Exception;

                    /**
                     * Creates a plain object from an Exception message. Also converts values to other types if specified.
                     * @param message Exception
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.Exception, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Exception to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for Exception
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a LoginRequest. */
                interface ILoginRequest {

                    /** LoginRequest token */
                    token?: (string|null);

                    /** LoginRequest clientType */
                    clientType?: (com.joinu.im.protobuf.LoginRequest.ClientTypeEnum|null);
                }

                /** Represents a LoginRequest. */
                class LoginRequest implements ILoginRequest {

                    /**
                     * Constructs a new LoginRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.ILoginRequest);

                    /** LoginRequest token. */
                    public token: string;

                    /** LoginRequest clientType. */
                    public clientType: com.joinu.im.protobuf.LoginRequest.ClientTypeEnum;

                    /**
                     * Creates a new LoginRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns LoginRequest instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.ILoginRequest): com.joinu.im.protobuf.LoginRequest;

                    /**
                     * Encodes the specified LoginRequest message. Does not implicitly {@link com.joinu.im.protobuf.LoginRequest.verify|verify} messages.
                     * @param message LoginRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.LoginRequest.verify|verify} messages.
                     * @param message LoginRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a LoginRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns LoginRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.LoginRequest;

                    /**
                     * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns LoginRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.LoginRequest;

                    /**
                     * Verifies a LoginRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns LoginRequest
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.LoginRequest;

                    /**
                     * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
                     * @param message LoginRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.LoginRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this LoginRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for LoginRequest
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                namespace LoginRequest {

                    /** ClientTypeEnum enum. */
                    enum ClientTypeEnum {
                        NULL = 0,
                        IOS = 1,
                        ANDROID = 2,
                        WEB = 3,
                        PC = 4,
                        OTHER = 5,
                        MOBILE = 6
                    }
                }

                /** Properties of a LoginResult. */
                interface ILoginResult {

                    /** LoginResult success */
                    success?: (boolean|null);

                    /** LoginResult imUserId */
                    imUserId?: (string|null);
                }

                /** Represents a LoginResult. */
                class LoginResult implements ILoginResult {

                    /**
                     * Constructs a new LoginResult.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.ILoginResult);

                    /** LoginResult success. */
                    public success: boolean;

                    /** LoginResult imUserId. */
                    public imUserId: string;

                    /**
                     * Creates a new LoginResult instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns LoginResult instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.ILoginResult): com.joinu.im.protobuf.LoginResult;

                    /**
                     * Encodes the specified LoginResult message. Does not implicitly {@link com.joinu.im.protobuf.LoginResult.verify|verify} messages.
                     * @param message LoginResult message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.ILoginResult, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified LoginResult message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.LoginResult.verify|verify} messages.
                     * @param message LoginResult message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.ILoginResult, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a LoginResult message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns LoginResult
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.LoginResult;

                    /**
                     * Decodes a LoginResult message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns LoginResult
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.LoginResult;

                    /**
                     * Verifies a LoginResult message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a LoginResult message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns LoginResult
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.LoginResult;

                    /**
                     * Creates a plain object from a LoginResult message. Also converts values to other types if specified.
                     * @param message LoginResult
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.LoginResult, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this LoginResult to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for LoginResult
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of an ImgMsg. */
                interface IImgMsg {

                    /** ImgMsg thumbnail */
                    thumbnail?: (Uint8Array|null);

                    /** ImgMsg url */
                    url?: (string|null);

                    /** ImgMsg imageId */
                    imageId?: (string|null);

                    /** ImgMsg thumbnailUrl */
                    thumbnailUrl?: (string|null);

                    /** ImgMsg width */
                    width?: (number|null);

                    /** ImgMsg height */
                    height?: (number|null);

                    /** ImgMsg attach */
                    attach?: (string|null);
                }

                /** Represents an ImgMsg. */
                class ImgMsg implements IImgMsg {

                    /**
                     * Constructs a new ImgMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IImgMsg);

                    /** ImgMsg thumbnail. */
                    public thumbnail: Uint8Array;

                    /** ImgMsg url. */
                    public url?: (string|null);

                    /** ImgMsg imageId. */
                    public imageId?: (string|null);

                    /** ImgMsg thumbnailUrl. */
                    public thumbnailUrl: string;

                    /** ImgMsg width. */
                    public width: number;

                    /** ImgMsg height. */
                    public height: number;

                    /** ImgMsg attach. */
                    public attach: string;

                    /** ImgMsg image. */
                    public image?: ("url"|"imageId");

                    /**
                     * Creates a new ImgMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ImgMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IImgMsg): com.joinu.im.protobuf.ImgMsg;

                    /**
                     * Encodes the specified ImgMsg message. Does not implicitly {@link com.joinu.im.protobuf.ImgMsg.verify|verify} messages.
                     * @param message ImgMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IImgMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ImgMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.ImgMsg.verify|verify} messages.
                     * @param message ImgMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IImgMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an ImgMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ImgMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.ImgMsg;

                    /**
                     * Decodes an ImgMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ImgMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.ImgMsg;

                    /**
                     * Verifies an ImgMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an ImgMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ImgMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.ImgMsg;

                    /**
                     * Creates a plain object from an ImgMsg message. Also converts values to other types if specified.
                     * @param message ImgMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.ImgMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ImgMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for ImgMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a Voice. */
                interface IVoice {

                    /** Voice url */
                    url?: (string|null);

                    /** Voice voiceId */
                    voiceId?: (string|null);

                    /** Voice duration */
                    duration?: (number|null);

                    /** Voice attach */
                    attach?: (string|null);
                }

                /** Represents a Voice. */
                class Voice implements IVoice {

                    /**
                     * Constructs a new Voice.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IVoice);

                    /** Voice url. */
                    public url?: (string|null);

                    /** Voice voiceId. */
                    public voiceId?: (string|null);

                    /** Voice duration. */
                    public duration: number;

                    /** Voice attach. */
                    public attach: string;

                    /** Voice voice. */
                    public voice?: ("url"|"voiceId");

                    /**
                     * Creates a new Voice instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Voice instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IVoice): com.joinu.im.protobuf.Voice;

                    /**
                     * Encodes the specified Voice message. Does not implicitly {@link com.joinu.im.protobuf.Voice.verify|verify} messages.
                     * @param message Voice message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IVoice, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Voice message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.Voice.verify|verify} messages.
                     * @param message Voice message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IVoice, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a Voice message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Voice
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.Voice;

                    /**
                     * Decodes a Voice message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Voice
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.Voice;

                    /**
                     * Verifies a Voice message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a Voice message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Voice
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.Voice;

                    /**
                     * Creates a plain object from a Voice message. Also converts values to other types if specified.
                     * @param message Voice
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.Voice, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Voice to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for Voice
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a Location. */
                interface ILocation {

                    /** Location title */
                    title?: (string|null);

                    /** Location address */
                    address?: (string|null);

                    /** Location latitude */
                    latitude?: (number|null);

                    /** Location longitude */
                    longitude?: (number|null);

                    /** Location uri */
                    uri?: (string|null);

                    /** Location attach */
                    attach?: (string|null);
                }

                /** Represents a Location. */
                class Location implements ILocation {

                    /**
                     * Constructs a new Location.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.ILocation);

                    /** Location title. */
                    public title: string;

                    /** Location address. */
                    public address: string;

                    /** Location latitude. */
                    public latitude: number;

                    /** Location longitude. */
                    public longitude: number;

                    /** Location uri. */
                    public uri: string;

                    /** Location attach. */
                    public attach: string;

                    /**
                     * Creates a new Location instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Location instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.ILocation): com.joinu.im.protobuf.Location;

                    /**
                     * Encodes the specified Location message. Does not implicitly {@link com.joinu.im.protobuf.Location.verify|verify} messages.
                     * @param message Location message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.ILocation, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Location message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.Location.verify|verify} messages.
                     * @param message Location message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.ILocation, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a Location message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Location
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.Location;

                    /**
                     * Decodes a Location message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Location
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.Location;

                    /**
                     * Verifies a Location message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a Location message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Location
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.Location;

                    /**
                     * Creates a plain object from a Location message. Also converts values to other types if specified.
                     * @param message Location
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.Location, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Location to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for Location
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a VideoMsg. */
                interface IVideoMsg {

                    /** VideoMsg cover */
                    cover?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** VideoMsg fileSize */
                    fileSize?: (number|Long|null);

                    /** VideoMsg duration */
                    duration?: (number|null);

                    /** VideoMsg attach */
                    attach?: (string|null);

                    /** VideoMsg url */
                    url?: (string|null);

                    /** VideoMsg fileId */
                    fileId?: (string|null);
                }

                /** Represents a VideoMsg. */
                class VideoMsg implements IVideoMsg {

                    /**
                     * Constructs a new VideoMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IVideoMsg);

                    /** VideoMsg cover. */
                    public cover?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** VideoMsg fileSize. */
                    public fileSize: (number|Long);

                    /** VideoMsg duration. */
                    public duration: number;

                    /** VideoMsg attach. */
                    public attach: string;

                    /** VideoMsg url. */
                    public url?: (string|null);

                    /** VideoMsg fileId. */
                    public fileId?: (string|null);

                    /** VideoMsg file. */
                    public file?: ("url"|"fileId");

                    /**
                     * Creates a new VideoMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns VideoMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IVideoMsg): com.joinu.im.protobuf.VideoMsg;

                    /**
                     * Encodes the specified VideoMsg message. Does not implicitly {@link com.joinu.im.protobuf.VideoMsg.verify|verify} messages.
                     * @param message VideoMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IVideoMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified VideoMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.VideoMsg.verify|verify} messages.
                     * @param message VideoMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IVideoMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a VideoMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns VideoMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.VideoMsg;

                    /**
                     * Decodes a VideoMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns VideoMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.VideoMsg;

                    /**
                     * Verifies a VideoMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a VideoMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns VideoMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.VideoMsg;

                    /**
                     * Creates a plain object from a VideoMsg message. Also converts values to other types if specified.
                     * @param message VideoMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.VideoMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this VideoMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for VideoMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a FileMsg. */
                interface IFileMsg {

                    /** FileMsg name */
                    name?: (string|null);

                    /** FileMsg fileSize */
                    fileSize?: (number|Long|null);

                    /** FileMsg url */
                    url?: (string|null);

                    /** FileMsg fileId */
                    fileId?: (string|null);

                    /** FileMsg attach */
                    attach?: (string|null);
                }

                /** Represents a FileMsg. */
                class FileMsg implements IFileMsg {

                    /**
                     * Constructs a new FileMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IFileMsg);

                    /** FileMsg name. */
                    public name: string;

                    /** FileMsg fileSize. */
                    public fileSize: (number|Long);

                    /** FileMsg url. */
                    public url?: (string|null);

                    /** FileMsg fileId. */
                    public fileId?: (string|null);

                    /** FileMsg attach. */
                    public attach: string;

                    /** FileMsg file. */
                    public file?: ("url"|"fileId");

                    /**
                     * Creates a new FileMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns FileMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IFileMsg): com.joinu.im.protobuf.FileMsg;

                    /**
                     * Encodes the specified FileMsg message. Does not implicitly {@link com.joinu.im.protobuf.FileMsg.verify|verify} messages.
                     * @param message FileMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IFileMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified FileMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.FileMsg.verify|verify} messages.
                     * @param message FileMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IFileMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a FileMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns FileMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.FileMsg;

                    /**
                     * Decodes a FileMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns FileMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.FileMsg;

                    /**
                     * Verifies a FileMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a FileMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns FileMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.FileMsg;

                    /**
                     * Creates a plain object from a FileMsg message. Also converts values to other types if specified.
                     * @param message FileMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.FileMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this FileMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for FileMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a UserInfo. */
                interface IUserInfo {

                    /** UserInfo nickname */
                    nickname?: (string|null);

                    /** UserInfo userId */
                    userId?: (string|null);

                    /** UserInfo avatar */
                    avatar?: (string|null);

                    /** UserInfo imUserId */
                    imUserId?: (string|null);
                }

                /** Represents a UserInfo. */
                class UserInfo implements IUserInfo {

                    /**
                     * Constructs a new UserInfo.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IUserInfo);

                    /** UserInfo nickname. */
                    public nickname: string;

                    /** UserInfo userId. */
                    public userId: string;

                    /** UserInfo avatar. */
                    public avatar: string;

                    /** UserInfo imUserId. */
                    public imUserId: string;

                    /**
                     * Creates a new UserInfo instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns UserInfo instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IUserInfo): com.joinu.im.protobuf.UserInfo;

                    /**
                     * Encodes the specified UserInfo message. Does not implicitly {@link com.joinu.im.protobuf.UserInfo.verify|verify} messages.
                     * @param message UserInfo message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IUserInfo, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified UserInfo message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.UserInfo.verify|verify} messages.
                     * @param message UserInfo message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IUserInfo, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a UserInfo message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns UserInfo
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.UserInfo;

                    /**
                     * Decodes a UserInfo message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns UserInfo
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.UserInfo;

                    /**
                     * Verifies a UserInfo message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a UserInfo message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns UserInfo
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.UserInfo;

                    /**
                     * Creates a plain object from a UserInfo message. Also converts values to other types if specified.
                     * @param message UserInfo
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.UserInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this UserInfo to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for UserInfo
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of an AudioAndVideoCall. */
                interface IAudioAndVideoCall {

                    /** AudioAndVideoCall durationType */
                    durationType?: (com.joinu.im.protobuf.AudioAndVideoCall.DurationType|null);

                    /** AudioAndVideoCall content */
                    content?: (string|null);

                    /** AudioAndVideoCall metingId */
                    metingId?: (string|null);

                    /** AudioAndVideoCall attach */
                    attach?: (string|null);
                }

                /** Represents an AudioAndVideoCall. */
                class AudioAndVideoCall implements IAudioAndVideoCall {

                    /**
                     * Constructs a new AudioAndVideoCall.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IAudioAndVideoCall);

                    /** AudioAndVideoCall durationType. */
                    public durationType: com.joinu.im.protobuf.AudioAndVideoCall.DurationType;

                    /** AudioAndVideoCall content. */
                    public content: string;

                    /** AudioAndVideoCall metingId. */
                    public metingId: string;

                    /** AudioAndVideoCall attach. */
                    public attach: string;

                    /**
                     * Creates a new AudioAndVideoCall instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns AudioAndVideoCall instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IAudioAndVideoCall): com.joinu.im.protobuf.AudioAndVideoCall;

                    /**
                     * Encodes the specified AudioAndVideoCall message. Does not implicitly {@link com.joinu.im.protobuf.AudioAndVideoCall.verify|verify} messages.
                     * @param message AudioAndVideoCall message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IAudioAndVideoCall, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified AudioAndVideoCall message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.AudioAndVideoCall.verify|verify} messages.
                     * @param message AudioAndVideoCall message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IAudioAndVideoCall, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an AudioAndVideoCall message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns AudioAndVideoCall
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.AudioAndVideoCall;

                    /**
                     * Decodes an AudioAndVideoCall message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns AudioAndVideoCall
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.AudioAndVideoCall;

                    /**
                     * Verifies an AudioAndVideoCall message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an AudioAndVideoCall message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns AudioAndVideoCall
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.AudioAndVideoCall;

                    /**
                     * Creates a plain object from an AudioAndVideoCall message. Also converts values to other types if specified.
                     * @param message AudioAndVideoCall
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.AudioAndVideoCall, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this AudioAndVideoCall to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for AudioAndVideoCall
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                namespace AudioAndVideoCall {

                    /** DurationType enum. */
                    enum DurationType {
                        NULL = 0,
                        VOICE = 1,
                        VIDEO = 2
                    }
                }

                /** Properties of a Withdraw. */
                interface IWithdraw {

                    /** Withdraw msgId */
                    msgId?: (string|null);
                }

                /** 撤回消息 */
                class Withdraw implements IWithdraw {

                    /**
                     * Constructs a new Withdraw.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IWithdraw);

                    /** Withdraw msgId. */
                    public msgId: string;

                    /**
                     * Creates a new Withdraw instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Withdraw instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IWithdraw): com.joinu.im.protobuf.Withdraw;

                    /**
                     * Encodes the specified Withdraw message. Does not implicitly {@link com.joinu.im.protobuf.Withdraw.verify|verify} messages.
                     * @param message Withdraw message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IWithdraw, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Withdraw message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.Withdraw.verify|verify} messages.
                     * @param message Withdraw message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IWithdraw, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a Withdraw message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Withdraw
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.Withdraw;

                    /**
                     * Decodes a Withdraw message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Withdraw
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.Withdraw;

                    /**
                     * Verifies a Withdraw message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a Withdraw message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Withdraw
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.Withdraw;

                    /**
                     * Creates a plain object from a Withdraw message. Also converts values to other types if specified.
                     * @param message Withdraw
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.Withdraw, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Withdraw to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for Withdraw
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a ReadMsg. */
                interface IReadMsg {

                    /** ReadMsg msgId */
                    msgId?: (string[]|null);
                }

                /** Represents a ReadMsg. */
                class ReadMsg implements IReadMsg {

                    /**
                     * Constructs a new ReadMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IReadMsg);

                    /** ReadMsg msgId. */
                    public msgId: string[];

                    /**
                     * Creates a new ReadMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ReadMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IReadMsg): com.joinu.im.protobuf.ReadMsg;

                    /**
                     * Encodes the specified ReadMsg message. Does not implicitly {@link com.joinu.im.protobuf.ReadMsg.verify|verify} messages.
                     * @param message ReadMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IReadMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ReadMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.ReadMsg.verify|verify} messages.
                     * @param message ReadMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IReadMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a ReadMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ReadMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.ReadMsg;

                    /**
                     * Decodes a ReadMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ReadMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.ReadMsg;

                    /**
                     * Verifies a ReadMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a ReadMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ReadMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.ReadMsg;

                    /**
                     * Creates a plain object from a ReadMsg message. Also converts values to other types if specified.
                     * @param message ReadMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.ReadMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ReadMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for ReadMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a C2CMsg. */
                interface IC2CMsg {

                    /** C2CMsg from */
                    from?: (string|null);

                    /** C2CMsg conversationId */
                    conversationId?: (number|null);

                    /** C2CMsg receiver */
                    receiver?: (string|null);

                    /** C2CMsg msg */
                    msg?: (string|null);

                    /** C2CMsg image */
                    image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** C2CMsg voice */
                    voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** C2CMsg location */
                    location?: (com.joinu.im.protobuf.ILocation|null);

                    /** C2CMsg video */
                    video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** C2CMsg file */
                    file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** C2CMsg withdraw */
                    withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** C2CMsg read */
                    read?: (com.joinu.im.protobuf.IReadMsg|null);

                    /** C2CMsg ext */
                    ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** C2CMsg call */
                    call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);
                }

                /** Represents a C2CMsg. */
                class C2CMsg implements IC2CMsg {

                    /**
                     * Constructs a new C2CMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IC2CMsg);

                    /** C2CMsg from. */
                    public from: string;

                    /** C2CMsg conversationId. */
                    public conversationId: number;

                    /** C2CMsg receiver. */
                    public receiver: string;

                    /** C2CMsg msg. */
                    public msg?: (string|null);

                    /** C2CMsg image. */
                    public image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** C2CMsg voice. */
                    public voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** C2CMsg location. */
                    public location?: (com.joinu.im.protobuf.ILocation|null);

                    /** C2CMsg video. */
                    public video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** C2CMsg file. */
                    public file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** C2CMsg withdraw. */
                    public withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** C2CMsg read. */
                    public read?: (com.joinu.im.protobuf.IReadMsg|null);

                    /** C2CMsg ext. */
                    public ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** C2CMsg call. */
                    public call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);

                    /** C2CMsg Context. */
                    public Context?: ("msg"|"image"|"voice"|"location"|"video"|"file"|"withdraw"|"read"|"ext"|"call");

                    /**
                     * Creates a new C2CMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns C2CMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IC2CMsg): com.joinu.im.protobuf.C2CMsg;

                    /**
                     * Encodes the specified C2CMsg message. Does not implicitly {@link com.joinu.im.protobuf.C2CMsg.verify|verify} messages.
                     * @param message C2CMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IC2CMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified C2CMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.C2CMsg.verify|verify} messages.
                     * @param message C2CMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IC2CMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a C2CMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns C2CMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.C2CMsg;

                    /**
                     * Decodes a C2CMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns C2CMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.C2CMsg;

                    /**
                     * Verifies a C2CMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a C2CMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns C2CMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.C2CMsg;

                    /**
                     * Creates a plain object from a C2CMsg message. Also converts values to other types if specified.
                     * @param message C2CMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.C2CMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this C2CMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for C2CMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a C2CMsgRequest. */
                interface IC2CMsgRequest {

                    /** C2CMsgRequest receiver */
                    receiver?: (string|null);

                    /** C2CMsgRequest msg */
                    msg?: (string|null);

                    /** C2CMsgRequest image */
                    image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** C2CMsgRequest voice */
                    voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** C2CMsgRequest location */
                    location?: (com.joinu.im.protobuf.ILocation|null);

                    /** C2CMsgRequest withdraw */
                    withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** C2CMsgRequest read */
                    read?: (com.joinu.im.protobuf.IReadMsg|null);

                    /** C2CMsgRequest ext */
                    ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** C2CMsgRequest video */
                    video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** C2CMsgRequest file */
                    file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** C2CMsgRequest call */
                    call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);
                }

                /** Represents a C2CMsgRequest. */
                class C2CMsgRequest implements IC2CMsgRequest {

                    /**
                     * Constructs a new C2CMsgRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IC2CMsgRequest);

                    /** C2CMsgRequest receiver. */
                    public receiver: string;

                    /** C2CMsgRequest msg. */
                    public msg?: (string|null);

                    /** C2CMsgRequest image. */
                    public image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** C2CMsgRequest voice. */
                    public voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** C2CMsgRequest location. */
                    public location?: (com.joinu.im.protobuf.ILocation|null);

                    /** C2CMsgRequest withdraw. */
                    public withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** C2CMsgRequest read. */
                    public read?: (com.joinu.im.protobuf.IReadMsg|null);

                    /** C2CMsgRequest ext. */
                    public ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** C2CMsgRequest video. */
                    public video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** C2CMsgRequest file. */
                    public file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** C2CMsgRequest call. */
                    public call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);

                    /** C2CMsgRequest Context. */
                    public Context?: ("msg"|"image"|"voice"|"location"|"withdraw"|"read"|"ext"|"video"|"file"|"call");

                    /**
                     * Creates a new C2CMsgRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns C2CMsgRequest instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IC2CMsgRequest): com.joinu.im.protobuf.C2CMsgRequest;

                    /**
                     * Encodes the specified C2CMsgRequest message. Does not implicitly {@link com.joinu.im.protobuf.C2CMsgRequest.verify|verify} messages.
                     * @param message C2CMsgRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IC2CMsgRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified C2CMsgRequest message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.C2CMsgRequest.verify|verify} messages.
                     * @param message C2CMsgRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IC2CMsgRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a C2CMsgRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns C2CMsgRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.C2CMsgRequest;

                    /**
                     * Decodes a C2CMsgRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns C2CMsgRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.C2CMsgRequest;

                    /**
                     * Verifies a C2CMsgRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a C2CMsgRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns C2CMsgRequest
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.C2CMsgRequest;

                    /**
                     * Creates a plain object from a C2CMsgRequest message. Also converts values to other types if specified.
                     * @param message C2CMsgRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.C2CMsgRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this C2CMsgRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for C2CMsgRequest
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** 消息发送失败的原因列表 */
                enum FailType {
                    UNKNOWN = 0,
                    IN_BLACKLIST = 1,
                    TARGET_NOT_LOGIN = 2,
                    MSG_SAVE_FAILURE = 3,
                    TARGET_IS_EMPTY = 4,
                    GROUP_NOT_EXIST = 5,
                    GROUP_MUTED = 6,
                    GROUP_USER_MUTED = 7,
                    USER_NOT_IN_GROUP = 8
                }

                /** Properties of a C2CMsgResponse. */
                interface IC2CMsgResponse {

                    /** C2CMsgResponse receiver */
                    receiver?: (string|null);

                    /** C2CMsgResponse success */
                    success?: (boolean|null);

                    /** C2CMsgResponse failType */
                    failType?: (com.joinu.im.protobuf.FailType|null);

                    /** C2CMsgResponse failMessage */
                    failMessage?: (string|null);
                }

                /** Represents a C2CMsgResponse. */
                class C2CMsgResponse implements IC2CMsgResponse {

                    /**
                     * Constructs a new C2CMsgResponse.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IC2CMsgResponse);

                    /** C2CMsgResponse receiver. */
                    public receiver: string;

                    /** C2CMsgResponse success. */
                    public success: boolean;

                    /** C2CMsgResponse failType. */
                    public failType: com.joinu.im.protobuf.FailType;

                    /** C2CMsgResponse failMessage. */
                    public failMessage: string;

                    /**
                     * Creates a new C2CMsgResponse instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns C2CMsgResponse instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IC2CMsgResponse): com.joinu.im.protobuf.C2CMsgResponse;

                    /**
                     * Encodes the specified C2CMsgResponse message. Does not implicitly {@link com.joinu.im.protobuf.C2CMsgResponse.verify|verify} messages.
                     * @param message C2CMsgResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IC2CMsgResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified C2CMsgResponse message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.C2CMsgResponse.verify|verify} messages.
                     * @param message C2CMsgResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IC2CMsgResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a C2CMsgResponse message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns C2CMsgResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.C2CMsgResponse;

                    /**
                     * Decodes a C2CMsgResponse message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns C2CMsgResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.C2CMsgResponse;

                    /**
                     * Verifies a C2CMsgResponse message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a C2CMsgResponse message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns C2CMsgResponse
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.C2CMsgResponse;

                    /**
                     * Creates a plain object from a C2CMsgResponse message. Also converts values to other types if specified.
                     * @param message C2CMsgResponse
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.C2CMsgResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this C2CMsgResponse to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for C2CMsgResponse
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a PushMsg. */
                interface IPushMsg {

                    /** PushMsg msg */
                    msg?: (string|null);

                    /** PushMsg type */
                    type?: (number|null);
                }

                /** Represents a PushMsg. */
                class PushMsg implements IPushMsg {

                    /**
                     * Constructs a new PushMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IPushMsg);

                    /** PushMsg msg. */
                    public msg: string;

                    /** PushMsg type. */
                    public type: number;

                    /**
                     * Creates a new PushMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns PushMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IPushMsg): com.joinu.im.protobuf.PushMsg;

                    /**
                     * Encodes the specified PushMsg message. Does not implicitly {@link com.joinu.im.protobuf.PushMsg.verify|verify} messages.
                     * @param message PushMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IPushMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified PushMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.PushMsg.verify|verify} messages.
                     * @param message PushMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IPushMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a PushMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns PushMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.PushMsg;

                    /**
                     * Decodes a PushMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns PushMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.PushMsg;

                    /**
                     * Verifies a PushMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a PushMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns PushMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.PushMsg;

                    /**
                     * Creates a plain object from a PushMsg message. Also converts values to other types if specified.
                     * @param message PushMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.PushMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this PushMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for PushMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a GroupMsg. */
                interface IGroupMsg {

                    /** GroupMsg appId */
                    appId?: (string|null);

                    /** GroupMsg groupId */
                    groupId?: (string|null);

                    /** GroupMsg groupName */
                    groupName?: (string|null);

                    /** GroupMsg groupLogo */
                    groupLogo?: (string|null);

                    /** GroupMsg msg */
                    msg?: (string|null);

                    /** GroupMsg image */
                    image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** GroupMsg voice */
                    voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** GroupMsg location */
                    location?: (com.joinu.im.protobuf.ILocation|null);

                    /** GroupMsg ext */
                    ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** GroupMsg withdraw */
                    withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** GroupMsg video */
                    video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** GroupMsg file */
                    file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** GroupMsg call */
                    call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);

                    /** GroupMsg noticeMsg */
                    noticeMsg?: (com.joinu.im.protobuf.INoticeMsg|null);
                }

                /** Represents a GroupMsg. */
                class GroupMsg implements IGroupMsg {

                    /**
                     * Constructs a new GroupMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IGroupMsg);

                    /** GroupMsg appId. */
                    public appId: string;

                    /** GroupMsg groupId. */
                    public groupId: string;

                    /** GroupMsg groupName. */
                    public groupName: string;

                    /** GroupMsg groupLogo. */
                    public groupLogo: string;

                    /** GroupMsg msg. */
                    public msg?: (string|null);

                    /** GroupMsg image. */
                    public image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** GroupMsg voice. */
                    public voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** GroupMsg location. */
                    public location?: (com.joinu.im.protobuf.ILocation|null);

                    /** GroupMsg ext. */
                    public ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** GroupMsg withdraw. */
                    public withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** GroupMsg video. */
                    public video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** GroupMsg file. */
                    public file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** GroupMsg call. */
                    public call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);

                    /** GroupMsg noticeMsg. */
                    public noticeMsg?: (com.joinu.im.protobuf.INoticeMsg|null);

                    /** GroupMsg Context. */
                    public Context?: ("msg"|"image"|"voice"|"location"|"ext"|"withdraw"|"video"|"file"|"call"|"noticeMsg");

                    /**
                     * Creates a new GroupMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GroupMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IGroupMsg): com.joinu.im.protobuf.GroupMsg;

                    /**
                     * Encodes the specified GroupMsg message. Does not implicitly {@link com.joinu.im.protobuf.GroupMsg.verify|verify} messages.
                     * @param message GroupMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IGroupMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GroupMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.GroupMsg.verify|verify} messages.
                     * @param message GroupMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IGroupMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GroupMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GroupMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.GroupMsg;

                    /**
                     * Decodes a GroupMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GroupMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.GroupMsg;

                    /**
                     * Verifies a GroupMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GroupMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GroupMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.GroupMsg;

                    /**
                     * Creates a plain object from a GroupMsg message. Also converts values to other types if specified.
                     * @param message GroupMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.GroupMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GroupMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for GroupMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of an ExtMsg. */
                interface IExtMsg {

                    /** ExtMsg ext */
                    ext?: (google.protobuf.IAny|null);

                    /** ExtMsg ext1 */
                    ext1?: (string|null);

                    /** ExtMsg ext2 */
                    ext2?: (string|null);

                    /** ExtMsg ext3 */
                    ext3?: (string|null);
                }

                /** Represents an ExtMsg. */
                class ExtMsg implements IExtMsg {

                    /**
                     * Constructs a new ExtMsg.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IExtMsg);

                    /** ExtMsg ext. */
                    public ext?: (google.protobuf.IAny|null);

                    /** ExtMsg ext1. */
                    public ext1: string;

                    /** ExtMsg ext2. */
                    public ext2: string;

                    /** ExtMsg ext3. */
                    public ext3: string;

                    /**
                     * Creates a new ExtMsg instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ExtMsg instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IExtMsg): com.joinu.im.protobuf.ExtMsg;

                    /**
                     * Encodes the specified ExtMsg message. Does not implicitly {@link com.joinu.im.protobuf.ExtMsg.verify|verify} messages.
                     * @param message ExtMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IExtMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ExtMsg message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.ExtMsg.verify|verify} messages.
                     * @param message ExtMsg message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IExtMsg, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an ExtMsg message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ExtMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.ExtMsg;

                    /**
                     * Decodes an ExtMsg message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ExtMsg
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.ExtMsg;

                    /**
                     * Verifies an ExtMsg message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an ExtMsg message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ExtMsg
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.ExtMsg;

                    /**
                     * Creates a plain object from an ExtMsg message. Also converts values to other types if specified.
                     * @param message ExtMsg
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.ExtMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ExtMsg to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for ExtMsg
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a GroupMsgRequest. */
                interface IGroupMsgRequest {

                    /** GroupMsgRequest groupId */
                    groupId?: (string|null);

                    /** GroupMsgRequest groupName */
                    groupName?: (string|null);

                    /** GroupMsgRequest groupLogo */
                    groupLogo?: (string|null);

                    /** GroupMsgRequest msg */
                    msg?: (string|null);

                    /** GroupMsgRequest image */
                    image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** GroupMsgRequest voice */
                    voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** GroupMsgRequest location */
                    location?: (com.joinu.im.protobuf.ILocation|null);

                    /** GroupMsgRequest ext */
                    ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** GroupMsgRequest withdraw */
                    withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** GroupMsgRequest video */
                    video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** GroupMsgRequest file */
                    file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** GroupMsgRequest call */
                    call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);
                }

                /** Represents a GroupMsgRequest. */
                class GroupMsgRequest implements IGroupMsgRequest {

                    /**
                     * Constructs a new GroupMsgRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IGroupMsgRequest);

                    /** GroupMsgRequest groupId. */
                    public groupId: string;

                    /** GroupMsgRequest groupName. */
                    public groupName: string;

                    /** GroupMsgRequest groupLogo. */
                    public groupLogo: string;

                    /** GroupMsgRequest msg. */
                    public msg?: (string|null);

                    /** GroupMsgRequest image. */
                    public image?: (com.joinu.im.protobuf.IImgMsg|null);

                    /** GroupMsgRequest voice. */
                    public voice?: (com.joinu.im.protobuf.IVoice|null);

                    /** GroupMsgRequest location. */
                    public location?: (com.joinu.im.protobuf.ILocation|null);

                    /** GroupMsgRequest ext. */
                    public ext?: (com.joinu.im.protobuf.IExtMsg|null);

                    /** GroupMsgRequest withdraw. */
                    public withdraw?: (com.joinu.im.protobuf.IWithdraw|null);

                    /** GroupMsgRequest video. */
                    public video?: (com.joinu.im.protobuf.IVideoMsg|null);

                    /** GroupMsgRequest file. */
                    public file?: (com.joinu.im.protobuf.IFileMsg|null);

                    /** GroupMsgRequest call. */
                    public call?: (com.joinu.im.protobuf.IAudioAndVideoCall|null);

                    /** GroupMsgRequest Context. */
                    public Context?: ("msg"|"image"|"voice"|"location"|"ext"|"withdraw"|"video"|"file"|"call");

                    /**
                     * Creates a new GroupMsgRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GroupMsgRequest instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IGroupMsgRequest): com.joinu.im.protobuf.GroupMsgRequest;

                    /**
                     * Encodes the specified GroupMsgRequest message. Does not implicitly {@link com.joinu.im.protobuf.GroupMsgRequest.verify|verify} messages.
                     * @param message GroupMsgRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IGroupMsgRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GroupMsgRequest message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.GroupMsgRequest.verify|verify} messages.
                     * @param message GroupMsgRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IGroupMsgRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GroupMsgRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GroupMsgRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.GroupMsgRequest;

                    /**
                     * Decodes a GroupMsgRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GroupMsgRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.GroupMsgRequest;

                    /**
                     * Verifies a GroupMsgRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GroupMsgRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GroupMsgRequest
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.GroupMsgRequest;

                    /**
                     * Creates a plain object from a GroupMsgRequest message. Also converts values to other types if specified.
                     * @param message GroupMsgRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.GroupMsgRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GroupMsgRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for GroupMsgRequest
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }

                /** Properties of a GroupMsgResponse. */
                interface IGroupMsgResponse {

                    /** GroupMsgResponse groupId */
                    groupId?: (string|null);

                    /** GroupMsgResponse success */
                    success?: (boolean|null);

                    /** GroupMsgResponse failType */
                    failType?: (com.joinu.im.protobuf.FailType|null);

                    /** GroupMsgResponse failMessage */
                    failMessage?: (string|null);
                }

                /** Represents a GroupMsgResponse. */
                class GroupMsgResponse implements IGroupMsgResponse {

                    /**
                     * Constructs a new GroupMsgResponse.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: com.joinu.im.protobuf.IGroupMsgResponse);

                    /** GroupMsgResponse groupId. */
                    public groupId: string;

                    /** GroupMsgResponse success. */
                    public success: boolean;

                    /** GroupMsgResponse failType. */
                    public failType: com.joinu.im.protobuf.FailType;

                    /** GroupMsgResponse failMessage. */
                    public failMessage: string;

                    /**
                     * Creates a new GroupMsgResponse instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GroupMsgResponse instance
                     */
                    public static create(properties?: com.joinu.im.protobuf.IGroupMsgResponse): com.joinu.im.protobuf.GroupMsgResponse;

                    /**
                     * Encodes the specified GroupMsgResponse message. Does not implicitly {@link com.joinu.im.protobuf.GroupMsgResponse.verify|verify} messages.
                     * @param message GroupMsgResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: com.joinu.im.protobuf.IGroupMsgResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GroupMsgResponse message, length delimited. Does not implicitly {@link com.joinu.im.protobuf.GroupMsgResponse.verify|verify} messages.
                     * @param message GroupMsgResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: com.joinu.im.protobuf.IGroupMsgResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GroupMsgResponse message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GroupMsgResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): com.joinu.im.protobuf.GroupMsgResponse;

                    /**
                     * Decodes a GroupMsgResponse message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GroupMsgResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): com.joinu.im.protobuf.GroupMsgResponse;

                    /**
                     * Verifies a GroupMsgResponse message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GroupMsgResponse message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GroupMsgResponse
                     */
                    public static fromObject(object: { [k: string]: any }): com.joinu.im.protobuf.GroupMsgResponse;

                    /**
                     * Creates a plain object from a GroupMsgResponse message. Also converts values to other types if specified.
                     * @param message GroupMsgResponse
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: com.joinu.im.protobuf.GroupMsgResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GroupMsgResponse to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };

                    /**
                     * Gets the default type url for GroupMsgResponse
                     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                     * @returns The default type url
                     */
                    public static getTypeUrl(typeUrlPrefix?: string): string;
                }
            }
        }
    }
}

/** Namespace google. */
export namespace google {

    /** Namespace protobuf. */
    namespace protobuf {

        /** Properties of an Any. */
        interface IAny {

            /** Any type_url */
            type_url?: (string|null);

            /** Any value */
            value?: (Uint8Array|null);
        }

        /** Represents an Any. */
        class Any implements IAny {

            /**
             * Constructs a new Any.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IAny);

            /** Any type_url. */
            public type_url: string;

            /** Any value. */
            public value: Uint8Array;

            /**
             * Creates a new Any instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Any instance
             */
            public static create(properties?: google.protobuf.IAny): google.protobuf.Any;

            /**
             * Encodes the specified Any message. Does not implicitly {@link google.protobuf.Any.verify|verify} messages.
             * @param message Any message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IAny, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Any message, length delimited. Does not implicitly {@link google.protobuf.Any.verify|verify} messages.
             * @param message Any message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IAny, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an Any message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Any
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Any;

            /**
             * Decodes an Any message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Any
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.Any;

            /**
             * Verifies an Any message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an Any message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Any
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Any;

            /**
             * Creates a plain object from an Any message. Also converts values to other types if specified.
             * @param message Any
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Any, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Any to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Any
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }
    }
}
