import { log } from '../../util/Log';
import { router } from '@kit.ArkUI';
import web_webview from '@ohos.web.webview';
import { JSON } from '@kit.ArkTS';
import { ClientFindDeviceInfo, ClientIsAlreadyBody, TransImage2Web, UserData,
  WebPreviewImageEntity } from './WebParamEntity';
import { UserManager } from '../../user_manage/UserManage';
import { DeviceUtils } from '../../util/DeviceUtil';
import picker from '@ohos.file.picker';
import { BufferUtils } from '../../util/BufferUtils';
import { AppUtil } from '../../util/AppUtil';
import { abilityAccessCtrl, bundleManager, common } from '@kit.AbilityKit';
import { PermissionManager } from '../../util/PermissionManager';
import { NetUtil, WifiInfo } from '../../util/NetUtil';
import { ImageViewerUtil } from '../../util/ImageViewerUtil';


// 定义用户信息接口（根据实际数据结构）
interface UserInfo {
  address: string;
  avatar: string;
  birthday: string;
  companyId: string;
  email: string;
  gender: string;
  intro: string;
  mobile: string;
  name: string;
  nickName: string;
  openId: string;
  profession: string;
  userId: string;
}

// 定义公司信息接口（根据实际数据结构）
interface CompanyInfo {
  companyId: string;
  content: string;
  deptId: string;
  haveApprove: number;
  isOuter: boolean;
  isSelected: boolean;
  logo: string;
  name: string;
  noticeId: string;
  power: string;
  rejectInvitation: number;
  rejectJoin: number;
}

// 定义客户端信息接口（根据实际数据结构）
interface ClientInfo {
  statusHeight: number;
  company: CompanyInfo;
  user: UserInfo;
  token: string;
}

// 定义响应数据接口
interface ResponseData {
  success: boolean;
  message: string;
  data?: ESObject;
}




// 基础配置 - 与Android保持一致
// 注入时使用的对象名（对应Android的ConsKeys.JS_TRANS_PORT）
export const BASE_WIN_OBJ = 'DDBESOFFICE';
// 默认的回调对象名（对应Android的初始objName）
export const DEFAULT_CALLBACK_OBJ = 'ddbes_web';

export class JsProxy {

  // 可变 - 对应Android的objName变量，初始值为默认回调对象名
  currentObj: string = DEFAULT_CALLBACK_OBJ
  controller?: web_webview.WebviewController

  constructor(controller: web_webview.WebviewController) {
    this.controller = controller;
  }

  public client_isAlready(message: string) {
    log(` === client_isAlready 方法被调用 === `);
    try {
      let o = JSON.parse(message) as ClientIsAlreadyBody;
      log(`解析后的数据: ${JSON.stringify(o)}`);

      if (o && o.objName) {
        if (o.objName.trim() !== '') {
          // 更新当前对象名
          this.currentObj = o.objName;
        }
      }
      this.updateClientInfo();
    } catch (error) {
    }
  }

  public client_getStatusBarHeight() {
    log(` === client_getStatusBarHeight 方法被调用 ===`);

    // 获取状态栏高度后 通过 ‘updateStatusBarHeight’
    // let statusBarHeight = DeviceUtils.getStatusBarHeight();
    let statusBarHeight = AppUtil.getStatusBarHeight();
    let statusBarHeightVP =  px2vp(statusBarHeight)
    this.callJavaScriptMethod('updateStatusBarHeight' , `${statusBarHeightVP}`)
  }

  /**
   * web 调用原生 获取当前设备信息
   * web接收方法，findDeviceInfo(json:String)
   * 返回json格式：{"device":"设备唯一标识","deviceModel":"设备型号信息"}
   */
  public async client_getDeviceInfo(){
    let deviceInfo = DeviceUtils.getDeviceInfo();
    let deviceId = await DeviceUtils.getDeviceAAID()
    let json : ClientFindDeviceInfo = {
      'device': deviceId,
      'deviceModel': deviceInfo,
    };
    this.callJavaScriptMethod('findDeviceInfo' , `${JSON.stringify(json)}}`)
  }

  public client_copy(txt: string) {
    DeviceUtils.copy2PasteBoard(txt)
  }

  /**
   *  exa: 'https://www.ddbes.com/AI/index.html?agent=DdbesAI&platform=1&appVersion=50007'
   * json : {"count":1,"type":"attachment"}
   * @param json
   */
  public client_choosePic(json:string) {
    log(` === client_choosePic 方法被调用 === ${json}`);
    let x = JSON.parse(json) as TransImage2Web
    log(`解析 ${x.type} , ${x.count}`)
    this.selectPhoto(x)
  }

  async selectPhoto(transImage: TransImage2Web) {
    try {
      // 创建选择图片的参数
      let photoOptions = new picker.PhotoSelectOptions;
      photoOptions.MIMEType = picker.PhotoViewMIMETypes.IMAGE_TYPE;
      photoOptions.maxSelectNumber = transImage.count;
      // 选择图片的组件
      let photoPicker = new picker.PhotoViewPicker()
      // 调用选择图片
      let res = await photoPicker.select(photoOptions)
      let images = res.photoUris
      log(`返回图片： ${images}`)

      let base64Images = await BufferUtils.convertMultiplePathsToBase64(images)

      let newTrans : TransImage2Web = {
        "count" : transImage.count,
        "type" : transImage.type,
        "images" : base64Images,
      }
      this.transBase64ImageToWeb(newTrans)

    }catch (err) {
      console.error(`PhotoViewPicker failed with err: ${err.code}, ${err.message}`);
    }

  }

  public client_openCamera(json: string){
    //TODO
    // this.transBase64ImageToWeb()

  }

  // 该方法不实现
  public client_getToken(message: string) {
    log(` === client_getToken 方法被调用 === `);
    // DO NOTHING
  }

  public client_getUserInfo(message: string) {
    log(` === client_getUserInfo 方法被调用 === `);

    const responseData: UserData = {
      userId: 'user123',
      userName: '用户名',
      avatar: 'avatar_url'
    };
    let json = JSON.stringify(responseData)
    log(`updateUserInfo 传递的数据 = ${json}`)
    this.callJavaScriptMethod("updateUserInfo" , json);
  }

  public client_goBack(message: string) {
    log(` === client_goBack 方法被调用 === `);
    try {
      router.back();
      const successResponse: ResponseData = { success: true, message: '页面返回成功' };
    } catch (error) {
      log(`页面返回失败: ${error}`);
      const errorResponse: ResponseData = { success: false, message: '页面返回失败' };
    }
  }

  public client_popBack(message: string) {
    log(`=== client_popBack 方法被调用 ===`);
    log(`接收到的消息: ${message}`);

    try {
      if (this.controller) {
        if (this.controller.accessBackward()) {
          this.controller.backward();
        } else {
          router.back();
        }
      } else {
        router.back();
      }
    } catch (error) {
    }
  }

  // js 跳转到 二维码扫描
  public client_jumpQr() {
    // todo 需要调用 Flutter
  }

  // 获取公司信息
  public client_getCompanyInfo(){
    this.injectionCompanyInfo()
  }

  // web 需要 wifi 信息
  public client_getWifiInfo(){
    this.injectionWifi()
  }

  /**
   * 预览图片 json 内容格式：{"index":Int,"list":["图片链接",""]}
   *          list  文件链接图片
   *          index 多张图片时，指定图片数组中预览哪一张图片
   */
  public client_previewPics(json: string) {
    try {
      log(`🔔🔔🔔 === client_previewPics 方法被调用 === 🔔🔔🔔`);
      log(`📨 接收到的JSON: ${json}`);

      // 解析JSON参数
      const previewData: WebPreviewImageEntity = JSON.parse(json) as WebPreviewImageEntity;

      log(`📋 解析结果: index=${previewData.index}, list长度=${previewData.list?.length || 0}`);

      // 验证参数
      if (!previewData.list || previewData.list.length === 0) {
        log(`❌ 图片列表为空`);
        return;
      }

      const index = previewData.index || 0;
      const imageList = previewData.list;

      // 验证索引范围
      if (index < 0 || index >= imageList.length) {
        log(`❌ 索引超出范围: ${index}, 列表长度: ${imageList.length}`);
        return;
      }

      // 调整索引（如果原索引对应的图片被过滤掉了）
      let adjustedIndex = index;
      if (index >= imageList.length) {
        adjustedIndex = 0;
      }

      // 打开图片浏览器
      // ImageViewerUtil.openImageViewer(this ,adjustedIndex, imageList);

    } catch (error) {
    }
  }

  /**
   *  {"bssid":"wifi设备地址","ssid":"wifi名称","wifiState":0} 未获取到wifi信息时，ssid 为空字符串
   *  wifiState： 1 获取到wifi信息 0 未获取到 -1 无权限
   */
  private async injectionWifi() {
    let wifiInfo: WifiInfo = {
      bssid: '',
      ssid: '',
      wifiState: -1
    };

    try {
      // 检查WiFi权限
      const hasPermission = await PermissionManager.getInstance().checkWifiPermission();
      if (!hasPermission) {
        wifiInfo = {
          bssid: '',
          ssid: '',
          wifiState: -1  // 无权限
        };
      }else {
        wifiInfo = await NetUtil.getCurrentWifiInfo();
      }
    } catch (error) {
      wifiInfo = {
        bssid: '',
        ssid: '',
        wifiState: 0  // 未获取到
      };
    }
    this.callJavaScriptMethod('findWifiInfo', JSON.stringify(wifiInfo));
  }





  // 模拟Android的updateClientInfo方法 - 使用真实数据结构
  /**
   * 数据样式
   * {"statusHeight":31,"company":{"companyId":"2603725684413039614","content":"去","deptId":"0","haveApprove":0,"isOuter":false,"isSelected":false,"logo":"https://cdn.ddbes.com/LOGO/1701321231053.jpg","name":"ljt企业","noticeId":"2603778546870518782","power":"123456","rejectInvitation":0,"rejectJoin":0},"user":{"address":"北京市北京市朝阳区","avatar":"https://cdn.ddbes.com/HEAD/2596847832233149437_I7CqmAMmsNNIHQUvcw_1738715060045.png","birthday":"2006-12-23","companyId":"805","email":"<EMAIL>","gender":"1","intro":"","mobile":"18600244913","name":"梁江涛","nickName":"Sprit^Moon","openId":"oRiTw02SL08CnSJckEPGbSDSIdKY","profession":"外汇经纪人","userId":"2596847832233149437"},"token":"Bearer 185406c3-9188-4afe-84f2-6f7aa60cd640"}
   */
  private updateClientInfo(): void {
    try {
      log(`📤 开始向Web页面回调客户端信息...`);

      // 构建用户信息（根据实际数据结构）
      const userInfo: UserInfo = {
        address: "北京市北京市朝阳区",
        avatar: "https://cdn.ddbes.com/HEAD/2468510355769497597_p95cNwSX3WCVIbLLSV_1587695423782.png",
        birthday: "2006-12-23",
        companyId: "8888",
        email: "<EMAIL>",
        gender: "1",
        intro: "",
        mobile: "18600244913",
        name: "梁江涛",
        nickName: "Sprit^Moon",
        openId: "",
        profession: "外汇经纪人",
        userId: UserManager.getUserId()
      };

      // 构建公司信息（根据实际数据结构）
      const companyInfo: CompanyInfo = {
        companyId: "8888",
        content: "去",
        deptId: "0",
        haveApprove: 0,
        isOuter: false,
        isSelected: false,
        logo: "https://cdn.ddbes.com/LOGO/1701321231053.jpg",
        name: "ljt企业",
        noticeId: "2603778546870518782",
        power: "123456",
        rejectInvitation: 0,
        rejectJoin: 0
      };

      // 构建完整的客户端信息
      const clientInfo: ClientInfo = {
        statusHeight: 31,
        company: companyInfo,
        user: userInfo,
        token: `Bearer ${UserManager.getUserToken()}`
      };

      log(`📋 客户端信息: ${JSON.stringify(clientInfo)}`);

      // 调用JavaScript的updateClientInfo方法
      this.callJavaScriptMethod('updateClientInfo', JSON.stringify(clientInfo));

    } catch (error) {
      log(`❌ updateClientInfo执行失败: ${error}`);
    }
  }

  // 注入js 公司信息
  private injectionCompanyInfo() {
    //TODO
    let companyInfo: CompanyInfo = {
      "companyId": '',
      content: '',
      deptId: '',
      haveApprove: 0,
      isOuter: false,
      isSelected: false,
      logo: '',
      name: '',
      noticeId: '',
      power: '',
      rejectInvitation: 0,
      rejectJoin: 0
    }
    this.callJavaScriptMethod('updateCompanyInfo' , `${JSON.stringify(companyInfo)}`)
  }

  /**
   * 选图或拍照后 返回给web端的数据为
   *  {"count":1,"type":"attachment", "images": [
   *     "dfieofejfo","fefefe"
   * ]}
   * 其中图片要转为 base64格式的
   *
   * @param json
   */
  private transBase64ImageToWeb(transImage: TransImage2Web) {
    let newTrans : TransImage2Web = {
      "count" : transImage.count,
      "type" : transImage.type,
      "images" : transImage.images,
    }
    let j = JSON.stringify(newTrans)
    this.callJavaScriptMethod("getBase64String" , j)
  }

  // 调用JavaScript方法的辅助函数
  private callJavaScriptMethod(methodName: string, param: string): void {
    try {
      if (this.controller) {

        const jsCode = `javascript:window.${this.currentObj}.${methodName}('${param}')`;
        log(` 调用JavaScript: ${jsCode}`);
        this.controller.runJavaScript(jsCode);
      } else {
        log(` WebView控制器未初始化`);
      }
    } catch (error) {
      log(`调用JavaScript方法失败: ${error}`);
    }
  }
}

export const JSMethodArray: string[] = [
  "client_isAlready",
  "client_getToken",
  "client_getUserInfo",
  "client_goBack",
  "client_popBack",
  "client_getStatusBarHeight",
  "client_getDeviceInfo",
  "client_copy",
  "client_choosePic",
  "client_openCamera",
  "client_jumpQr",
  "client_getCompanyInfo",
  "client_getWifiInfo",
  "client_previewPics",
];

