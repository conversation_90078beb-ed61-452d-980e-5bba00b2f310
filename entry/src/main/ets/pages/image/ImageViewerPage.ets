import { router } from '@kit.ArkUI';
import { ImageViewerParam } from './ImageViewerParam';
import { log } from '../../util/Log';
import { CommonToolbar } from '../../components/CommonToolbar';

@Entry
@Component
struct ImageViewerPage {
  @State currentIndex: number = 0;
  @State imageList: string[] = [];
  @State isLoading: boolean = true;
  @State loadError: boolean = false;
  @State showIndicator: boolean = true;
  
  // 手势相关状态
  @State offsetX: number = 0;
  @State scale1: number = 1;
  @State positionX: number = 0;
  @State positionY: number = 0;

  // 页面参数
  private viewerParam: ImageViewerParam = new ImageViewerParam();

  aboutToAppear() {
    this.initPageParams();
  }

  /**
   * 初始化页面参数
   */
  private initPageParams() {
    try {
      const params = this.getUIContext().getRouter().getParams() as ImageViewerParam;
      this.viewerParam = params
      log(`>>>>>>>>>> ${this.viewerParam.list}`)

      this.currentIndex = this.viewerParam.index;
      this.imageList = this.viewerParam.list;
      log(`图片浏览器初始化: 当前索引=${this.currentIndex}, 总数=${this.imageList.length}`);
    } catch (error) {
      log(`图片浏览器初始化失败: ${error}`);
    }
  }

  /**
   * 返回上一页
   */
  private goBack() {
    router.back();
  }

  /**
   * 切换到指定索引的图片
   */
  private switchToImage(index: number) {
    if (index >= 0 && index < this.imageList.length) {
      this.currentIndex = index;
      this.resetImageTransform();
      log(`切换到图片: ${index + 1}/${this.imageList.length}`);
    }
  }

  /**
   * 重置图片变换状态
   */
  private resetImageTransform() {
    this.scale1 = 1;
    this.positionX = 0;
    this.positionY = 0;
    this.offsetX = 0;
  }

  /**
   * 切换指示器显示状态
   */
  private toggleIndicator() {
    this.showIndicator = !this.showIndicator;
  }

  build() {
    Stack({ alignContent: Alignment.TopStart }) {
      if (this.loadError) {
        // 错误状态
        this.buildErrorView();
      } else if (this.imageList.length > 0) {
        // 正常图片浏览
        this.buildImageViewer();
      } else {
        // 加载状态
        this.buildLoadingView();
      }

      // 顶部工具栏
      if (this.showIndicator) {
        this.buildTopToolbar();
      }

      // 底部指示器
      if (this.showIndicator && this.imageList.length > 1) {
        this.buildBottomIndicator();
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.Black)
  }

  /**
   * 构建图片浏览器
   */
  @Builder
  buildImageViewer() {
    Swiper() {
      ForEach(this.imageList, (imageUrl: string, index: number) => {
        this.buildImageItem(imageUrl, index);
      }, (imageUrl: string, index: number) => `${index}_${imageUrl}`)
    }
    .width('100%')
    .height('100%')
    .index(this.currentIndex)
    .indicator(false)
    .loop(false)  // 禁用循环，避免索引混乱
    .duration(300)
    .autoPlay(false)
    .curve(Curve.EaseInOut)
    .itemSpace(0)
    .cachedCount(2)  // 缓存前后各2张图片
    .onChange((index: number) => {
      this.currentIndex = index;
      this.resetImageTransform();
      log(`图片切换到: ${index + 1}/${this.imageList.length}`);
    })
    .onGestureSwipe((index: number, extraInfo: SwiperAnimationEvent) => {
      log(`手势滑动到: ${index + 1}/${this.imageList.length}`);
    })
  }

  /**
   * 构建单个图片项
   */
  @Builder
  buildImageItem(imageUrl: string, index: number) {
    Stack() {
      Image(imageUrl)
        .width('100%')
        .height('100%')
        .objectFit(ImageFit.Contain)
        .scale({ x: this.scale1, y: this.scale1 })
        .translate({ x: this.positionX, y: this.positionY })
        .onClick(() => {
          // 单击切换指示器显示状态
          this.toggleIndicator();
        })
        .gesture(
          GestureGroup(GestureMode.Exclusive, // 使用互斥模式，优先处理缩放
            // 缩放手势
            PinchGesture()
              .onActionStart(() => {
                log('开始缩放');
              })
              .onActionUpdate((event) => {
                this.scale1 = Math.max(0.5, Math.min(3, event.scale));
              })
              .onActionEnd(() => {
                if (this.scale1 < 1) {
                  this.scale1 = 1;
                  this.positionX = 0;
                  this.positionY = 0;
                }
                log(`缩放结束: ${this.scale1}`);
              }),

            // 双击手势
            TapGesture({ count: 2 })
              .onAction(() => {
                if (this.scale1 === 1) {
                  this.scale1 = 2;
                } else {
                  this.scale1 = 1;
                  this.positionX = 0;
                  this.positionY = 0;
                }
                log(`双击缩放: ${this.scale1}`);
              }),

            // 平移手势 - 只在缩放状态下生效
            PanGesture()
              .onActionStart(() => {
                log('开始平移');
              })
              .onActionUpdate((event) => {
                if (this.scale1 > 1) {
                  this.positionX += event.offsetX;
                  this.positionY += event.offsetY;
                }
              })
              .onActionEnd(() => {
                log('平移结束');
              })
          )
        )
        .onComplete(() => {
          log(`图片加载完成: ${index}`);
          this.isLoading = false;
        })
        .onError(() => {
          log(`图片加载失败: ${index}`);
          this.loadError = true;
          this.isLoading = false;
        })
    }
    .width('100%')
    .height('100%')
    .gesture(
      // 添加一个额外的滑动手势，只在非缩放状态下生效
      PanGesture()
        .direction(PanDirection.Horizontal) // 只允许水平滑动
        .onActionStart((event) => {
          if (this.scale1 === 1) {
            // 记录起始偏移量
            this.offsetX = 0;
          }
        })
        .onActionUpdate((event) => {
          if (this.scale1 === 1) {
            // 更新水平偏移量
            this.offsetX += event.offsetX;
          }
        })
        .onActionEnd(() => {
          if (this.scale1 === 1) {
            // 根据偏移量决定是否切换图片
            if (this.offsetX > 100 && this.currentIndex > 0) {
              // 向右滑动，显示上一张
              this.switchToImage(this.currentIndex - 1);
            } else if (this.offsetX < -100 && this.currentIndex < this.imageList.length - 1) {
              // 向左滑动，显示下一张
              this.switchToImage(this.currentIndex + 1);
            }
            this.offsetX = 0;
          }
        })
    )
  }

  /**
   * 构建顶部工具栏
   */
  @Builder
  buildTopToolbar() {
    Row() {
      // 返回按钮
      CommonToolbar(
        { config : {title: '图片浏览'}}
      )

      Blank()

      // 图片计数
      Text(`${this.currentIndex + 1}/${this.imageList.length}`)
        .fontSize(16)
        .fontColor(Color.White)
        .fontWeight(FontWeight.Medium)
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16, top: 8, bottom: 8 })
    .backgroundColor('rgba(0, 0, 0, 0.5)')
  }

  /**
   * 构建底部指示器
   */
  @Builder
  buildBottomIndicator() {
    Row() {
      ForEach(this.imageList, (imageUrl: string, index: number) => {
        Circle()
          .width(8)
          .height(8)
          .fill(index === this.currentIndex ? Color.White : 'rgba(255, 255, 255, 0.5)')
          .margin({ left: 4, right: 4 })
      }, (imageUrl: string, index: number) => `indicator_${index}`)
    }
    .justifyContent(FlexAlign.Center)
    .width('100%')
    .height(40)
    .position({ x: 0, y: '100%' })
    .translate({ y: -60 })
    .backgroundColor('rgba(0, 0, 0, 0.3)')
  }

  /**
   * 构建加载视图
   */
  @Builder
  buildLoadingView() {
    Column() {
      LoadingProgress()
        .width(48)
        .height(48)
        .color(Color.White)
      
      Text('加载中...')
        .fontSize(16)
        .fontColor(Color.White)
        .margin({ top: 16 })
    }
    .justifyContent(FlexAlign.Center)
    .width('100%')
    .height('100%')
  }

  /**
   * 构建错误视图
   */
  @Builder
  buildErrorView() {
    Column() {
      Image('/resources/base/media/im_newsHome_search.png')
        .width(64)
        .height(64)
        .fillColor(Color.White)
      
      Text('图片加载失败')
        .fontSize(16)
        .fontColor(Color.White)
        .margin({ top: 16 })
      
      Button('返回')
        .fontSize(16)
        .fontColor(Color.White)
        .backgroundColor('rgba(255, 255, 255, 0.2)')
        .borderRadius(8)
        .padding({ left: 24, right: 24, top: 8, bottom: 8 })
        .margin({ top: 24 })
        .onClick(() => {
          this.goBack();
        })
    }
    .justifyContent(FlexAlign.Center)
    .width('100%')
    .height('100%')
  }
}
