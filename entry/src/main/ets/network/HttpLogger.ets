import { NetworkConfig } from './NetworkConfig';

/**
 * HTTP请求日志工具类
 */
export class HttpLogger {

  /**
   * 打印请求日志
   */
  static logRequest(config: Object, requestId: string): void {
    if (!NetworkConfig.ENABLE_REQUEST_LOG) {
      return;
    }

    const timestamp = new Date().toISOString();
    const configObj = config as Record<string, Object>;
    const method = (configObj.method as string)?.toUpperCase() || 'GET';
    const baseURL = configObj.baseURL as string || '';
    const url = configObj.url as string || '';
    const fullUrl = `${baseURL}${url}`;
    console.info(`baseURL ===> ${baseURL}`)

    console.info('='.repeat(80));
    console.info(`🚀 [REQUEST ${requestId}] ${timestamp}`);
    console.info(`📍 ${method} ${fullUrl}`);

    // 打印请求头
    const headers = configObj.headers as Record<string, string>;
    if (headers && Object.keys(headers).length > 0) {
      console.info('📋 Headers:');
      Object.keys(headers).forEach(key => {
        const value = headers[key];
        if (key.toLowerCase() === 'authorization') {
          console.info(`  ${key}: ${HttpLogger.maskSensitiveData(value)}`);
        } else {
          console.info(`  ${key}: ${value}`);
        }
      });
    }

    // 打印查询参数
    const params = configObj.params as Record<string, Object>;
    if (params && Object.keys(params).length > 0) {
      console.info('🔍 Query Params:');
      Object.keys(params).forEach(key => {
        console.info(`  ${key}: ${params[key]}`);
      });
    }

    // 打印请求体
    if (configObj.data) {
      console.info('📦 Request Body:');
      HttpLogger.logData(configObj.data);
    }

    console.info('='.repeat(80));
  }
  
  /**
   * 打印响应日志
   */
  static logResponse(response: Object, requestId: string, duration: number): void {
    if (!NetworkConfig.ENABLE_RESPONSE_LOG) {
      return;
    }

    const timestamp = new Date().toISOString();
    const responseObj = response as Record<string, Object>;
    const config = responseObj.config as Record<string, Object>;
    const method = (config?.method as string)?.toUpperCase() || 'GET';

    const baseURL = config.baseURL as string || '';
    const url = config?.url as string || '';
    const status = responseObj.status as number;
    const statusText = responseObj.statusText as string;

    console.info('='.repeat(80));
    console.info(`✅ [RESPONSE ${requestId}] ${timestamp} (${duration}ms)`);
    console.info(`📍 ${method} ${baseURL || '?baseUrl'} ${url}`);
    console.info(`📊 Status: ${status} ${statusText}`);

    // 打印响应数据
    console.info('📦 Response Data:');
    if (responseObj.data) {
      HttpLogger.logData(responseObj.data);
    } else {
      console.info('  (empty response)');
    }

    console.info('='.repeat(80));
  }
  
  /**
   * 打印错误日志
   */
  static logError(error: Object, requestId: string, duration: number): void {
    const timestamp = new Date().toISOString();
    const errorObj = error as Record<string, Object>;

    console.error('='.repeat(80));
    console.error(`❌ [ERROR ${requestId}] ${timestamp} (${duration}ms)`);

    if (errorObj.response) {
      // 服务器响应了错误状态码
      const config = errorObj.config as Record<string, Object>;
      const response = errorObj.response as Record<string, Object>;
      const method = (config?.method as string)?.toUpperCase() || 'GET';
      const url = config?.url as string || '';

      console.error(`📍 ${method} ${url}`);
      console.error(`📊 Status: ${response.status} ${response.statusText}`);
      console.error('📦 Error Response:');
      HttpLogger.logData(response.data);

    } else if (errorObj.request) {
      // 请求已发出但没有收到响应
      const config = errorObj.config as Record<string, Object>;
      const method = (config?.method as string)?.toUpperCase() || 'GET';
      const url = config?.url as string || '';

      console.error(`📍 ${method} ${url}`);
      console.error('📡 Network Error: No response received');
      console.error('🔍 Error Details:', errorObj.message);

    } else {
      // 其他错误
      console.error('⚠️ Request Setup Error:', errorObj.message);
    }

    console.error('='.repeat(80));
  }
  
  /**
   * 打印数据（支持大数据截断）
   */
  private static logData(data: Object): void {
    if (!data) {
      console.info('  (null)');
      return;
    }

    try {
      const dataStr = JSON.stringify(data, null, 2);

      if (dataStr.length > NetworkConfig.LOG_MAX_BODY_SIZE) {
        // 数据太大，进行截断
        const truncated = dataStr.substring(0, NetworkConfig.LOG_MAX_BODY_SIZE);
        console.info(truncated + '\n  ... (truncated)');
        console.info(`📏 Full data size: ${dataStr.length} characters`);

        // 如果是对象，尝试打印结构概览
        if (typeof data === 'object') {
          HttpLogger.logDataStructure(data);
        }
      } else {
        console.info(dataStr);
      }
    } catch (error) {
      console.info('  (unable to stringify data)');
      console.info('  Type:', typeof data);
      console.info('  Value:', data);
    }
  }
  
  /**
   * 打印数据结构概览
   */
  private static logDataStructure(data: Object, prefix: string = ''): void {
    if (!data || typeof data !== 'object') {
      return;
    }

    console.info(`📋 Data Structure${prefix}:`);

    if (Array.isArray(data)) {
      const dataArray = data as Object[];
      console.info(`  Array[${dataArray.length}]`);
      if (dataArray.length > 0) {
        console.info(`  First item type: ${typeof dataArray[0]}`);
        if (typeof dataArray[0] === 'object') {
          const keys = Object.keys(dataArray[0] as Record<string, Object>);
          console.info(`  First item keys: [${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}]`);
        }
      }
    } else {
      const dataObj = data as Record<string, Object>;
      const keys = Object.keys(dataObj);
      console.info(`  Object keys: [${keys.slice(0, 10).join(', ')}${keys.length > 10 ? '...' : ''}]`);

      // 显示每个key的类型
      keys.slice(0, 5).forEach(key => {
        const value = dataObj[key];
        if (Array.isArray(value)) {
          const valueArray = value as Object[];
          console.info(`  ${key}: Array[${valueArray.length}]`);
        } else {
          console.info(`  ${key}: ${typeof value}`);
        }
      });
    }
  }
  
  /**
   * 隐藏敏感数据
   */
  private static maskSensitiveData(data: string): string {
    if (!data || typeof data !== 'string') {
      return '****';
    }
    
    // 如果是Bearer token
    if (data.startsWith('Bearer ')) {
      const token = data.substring(7);
      if (token.length > 10) {
        return `Bearer ${token.substring(0, 6)}****${token.substring(token.length - 4)}`;
      }
      return 'Bearer ****';
    }
    
    // 其他敏感数据
    if (data.length > 10) {
      return `${data.substring(0, 4)}****${data.substring(data.length - 4)}`;
    }
    
    return '****';
  }
  
  /**
   * 生成请求ID
   */
  static generateRequestId(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
  
  /**
   * 格式化时间戳
   */
  static formatTimestamp(timestamp?: number): string {
    const date = timestamp ? new Date(timestamp) : new Date();
    return date.toISOString();
  }
  
  /**
   * 计算请求耗时
   */
  static calculateDuration(startTime: number): number {
    return Date.now() - startTime;
  }
}
