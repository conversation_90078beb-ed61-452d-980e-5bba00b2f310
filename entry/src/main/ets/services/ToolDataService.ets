import { toolApiService } from './ToolApiService';
import { dataOrmService } from '../database/DataOrmService';
import { ToolGridItem, WorkBenchUIData, SeparatedToolsData } from '../models/ToolModels';
import { NetworkError, NetworkErrorType } from '../network/NetworkConfig';

/**
 * 工具统计信息
 */
interface ToolStats {
  total: number;
  withBadge: number;
  totalBadges: number;
}

/**
 * 工具数据服务类
 * 结合API请求和本地缓存，提供统一的数据访问接口
 */
export class ToolDataService {
  private static instance: ToolDataService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): ToolDataService {
    if (!ToolDataService.instance) {
      ToolDataService.instance = new ToolDataService();
    }
    return ToolDataService.instance;
  }

  /**
   * 获取工具列表（优先使用缓存，缓存不存在时从API获取）
   * @param companyId 公司ID
   * @param forceRefresh 是否强制刷新（忽略缓存）
   * @param token 访问令牌（可选）
   * @returns Promise<ToolGridItem[]>
   */
  public async getTools(companyId: string, forceRefresh: boolean = false, token?: string): Promise<ToolGridItem[]> {
    try {
      console.info(`ToolDataService: 获取工具列表，公司ID: ${companyId}, 强制刷新: ${forceRefresh}`);

      // 如果不强制刷新，先尝试从缓存获取
      if (!forceRefresh) {
        const hasCache = await dataOrmService.hasToolCache(companyId);
        if (hasCache) {
          console.info('ToolDataService: 从缓存获取工具列表');
          const cachedTools = await dataOrmService.getCachedTools(companyId);
          if (cachedTools.length > 0) {
            console.info(`ToolDataService: 缓存命中，返回${cachedTools.length}个工具`);
            return cachedTools;
          }
        }
      }

      // 从API获取数据
      console.info('ToolDataService: 从API获取工具列表');
      const apiTools = await toolApiService.getCompanyTools(companyId, token);
      
      // 缓存到本地数据库
      const cacheSuccess = await dataOrmService.cacheTools(apiTools, companyId);
      if (cacheSuccess) {
        console.info(`ToolDataService: 成功缓存${apiTools.length}个工具`);
      } else {
        console.warn('ToolDataService: 缓存工具失败');
      }

      return apiTools;
    } catch (error) {
      console.error('ToolDataService: 获取工具列表失败', error);
      
      // 如果是网络错误，尝试从缓存获取
      if (error instanceof NetworkError && 
          (error.type === NetworkErrorType.NETWORK_ERROR || error.type === NetworkErrorType.TIMEOUT)) {
        console.info('ToolDataService: 网络错误，尝试从缓存获取');
        const cachedTools = await dataOrmService.getCachedTools(companyId);
        if (cachedTools.length > 0) {
          console.info(`ToolDataService: 缓存降级成功，返回${cachedTools.length}个工具`);
          return cachedTools;
        }
      }
      
      throw new Error(`获取工具列表失败: ${error}`);
    }
  }

  /**
   * 刷新工具列表
   * @param companyId 公司ID
   * @param token 访问令牌（可选）
   * @returns Promise<ToolGridItem[]>
   */
  public async refreshTools(companyId: string, token?: string): Promise<ToolGridItem[]> {
    console.info(`ToolDataService: 刷新工具列表，公司ID: ${companyId}`);
    return this.getTools(companyId, true, token);
  }

  /**
   * 获取工具详情
   * @param toolKey 工具key
   * @param companyId 公司ID
   * @param token 访问令牌（可选）
   * @returns Promise<ToolGridItem | null>
   */
  public async getToolDetail(toolKey: string, companyId: string, token?: string): Promise<ToolGridItem | null> {
    try {
      console.info(`ToolDataService: 获取工具详情，工具key: ${toolKey}, 公司ID: ${companyId}`);
      
      // 先从缓存中查找
      const cachedTools = await dataOrmService.getCachedTools(companyId);
      const cachedTool = cachedTools.find(tool => tool.key === toolKey);
      
      if (cachedTool) {
        console.info(`ToolDataService: 从缓存获取工具详情: ${cachedTool.name}`);
        return cachedTool;
      }
      
      // 缓存中没有，从API获取
      return await toolApiService.getToolDetail(toolKey, companyId, token);
    } catch (error) {
      console.error('ToolDataService: 获取工具详情失败', error);
      throw new Error(`获取工具详情失败: ${error}`);
    }
  }

  /**
   * 更新工具徽章数
   * @param toolKey 工具Key
   * @param companyId 公司ID
   * @param badge 徽章数
   * @returns Promise<boolean>
   */
  public async updateToolBadge(toolKey: string, companyId: string, badge: number): Promise<boolean> {
    try {
      console.info(`ToolDataService: 更新工具徽章，工具Key: ${toolKey}, 徽章数: ${badge}`);
      return await dataOrmService.updateToolBadge(toolKey, companyId, badge);
    } catch (error) {
      console.error('ToolDataService: 更新工具徽章失败', error);
      return false;
    }
  }

  /**
   * 清除工具缓存
   * @param companyId 公司ID
   * @returns Promise<boolean>
   */
  public async clearCache(companyId: string): Promise<boolean> {
    try {
      console.info(`ToolDataService: 清除工具缓存，公司ID: ${companyId}`);
      return await dataOrmService.clearToolCache(companyId);
    } catch (error) {
      console.error('ToolDataService: 清除工具缓存失败', error);
      return false;
    }
  }

  /**
   * 检查缓存是否存在
   * @param companyId 公司ID
   * @returns Promise<boolean>
   */
  public async hasCache(companyId: string): Promise<boolean> {
    try {
      return await dataOrmService.hasToolCache(companyId);
    } catch (error) {
      console.error('ToolDataService: 检查缓存失败', error);
      return false;
    }
  }

  /**
   * 获取工具统计信息
   * @param companyId 公司ID
   * @returns Promise<ToolStats>
   */
  public async getToolStats(companyId: string): Promise<ToolStats> {
    try {
      const tools = await dataOrmService.getCachedTools(companyId);
      const total = tools.length;
      const withBadge = tools.filter(tool => tool.badge && tool.badge > 0).length;
      const totalBadges = tools.reduce((sum, tool) => sum + (tool.badge || 0), 0);
      
      return { total, withBadge, totalBadges };
    } catch (error) {
      console.error('ToolDataService: 获取工具统计失败', error);
      return { total: 0, withBadge: 0, totalBadges: 0 };
    }
  }

  /**
   * 按类型筛选工具
   * @param companyId 公司ID
   * @param type 工具类型
   * @returns Promise<ToolGridItem[]>
   */
  public async getToolsByType(companyId: string, type: 'tool' | 'management'): Promise<ToolGridItem[]> {
    try {
      const allTools = await dataOrmService.getCachedTools(companyId);
      return allTools.filter(tool => tool.type === type);
    } catch (error) {
      console.error('ToolDataService: 按类型筛选工具失败', error);
      return [];
    }
  }

  /**
   * 获取分离的工具和管理数据
   * @param companyId 公司ID
   * @param forceRefresh 是否强制刷新
   * @param token 访问令牌
   * @returns Promise<{tools: WorkBenchUIData[], management: WorkBenchUIData[]}>
   */
  public async getToolsAndManagement(
    companyId: string,
    forceRefresh: boolean = false,
    token?: string
  ): Promise<SeparatedToolsData> {
    try {
      console.info(`ToolDataService: 获取分离的工具数据，公司ID: ${companyId}, 强制刷新: ${forceRefresh}`);

      // 获取所有工具数据
      const allTools = await this.getTools(companyId, forceRefresh, token);

      // 分离工具和管理数据
      const tools: WorkBenchUIData[] = [];
      const management: WorkBenchUIData[] = [];

      allTools.forEach(tool => {
        const uiData = new WorkBenchUIData(tool);
        if (uiData.isTool()) {
          tools.push(uiData);
        } else {
          management.push(uiData);
        }
      });

      console.info(`ToolDataService: 分离完成，工具: ${tools.length}个, 管理: ${management.length}个`);
      const result: SeparatedToolsData = { tools, management };
      return result;

    } catch (error) {
      console.error('ToolDataService: 获取分离工具数据失败', error);
      const errorResult: SeparatedToolsData = { tools: [], management: [] };
      return errorResult;
    }
  }

  /**
   * 刷新分离的工具和管理数据
   * @param companyId 公司ID
   * @param token 访问令牌
   * @returns Promise<{tools: WorkBenchUIData[], management: WorkBenchUIData[]}>
   */
  public async refreshToolsAndManagement(
    companyId: string,
    token?: string
  ): Promise<SeparatedToolsData> {
    console.info(`ToolDataService: 刷新分离的工具数据，公司ID: ${companyId}`);
    return this.getToolsAndManagement(companyId, true, token);
  }
}

// 导出单例实例
export const toolDataService = ToolDataService.getInstance();
