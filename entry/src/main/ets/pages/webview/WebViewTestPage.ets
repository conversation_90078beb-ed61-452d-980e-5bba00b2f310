import { CommonToolbar, ToolbarPresets } from "../../components/CommonToolbar";
import { WebViewParam } from "../../util/WebViewHelper";
import web_webview from '@ohos.web.webview';
import { BASE_WIN_OBJ, JSMethodArray, JsProxy } from './WebJS';
import { log } from "../../util/Log";

@Entry
@ComponentV2
export struct WebViewTestPage {
  private controller = new web_webview.WebviewController();
  private jsProxy: JsProxy = new JsProxy(this.controller);
  
  @Local progress: number = 0;
  @Local isProgressVisible: boolean = false;
  @Local testResults: string[] = [];

  aboutToAppear() {
    log(`WebView测试页面初始化`);
    this.initJavaScriptProxy();
  }

  // 初始化JavaScript代理
  private initJavaScriptProxy() {
    log(`初始化WebView测试页面的JavaScript代理`);
  }

  private proxy: JavaScriptProxy = {
    object: this.jsProxy,
    name: BASE_WIN_OBJ,
    methodList: J<PERSON>ethodArray,
    controller: this.controller
  };

  // 添加测试结果
  private addTestResult(result: string) {
    this.testResults.push(`[${new Date().toLocaleTimeString()}] ${result}`);
    log(`测试结果: ${result}`);
  }

  // 运行基础测试
  private runBasicTest() {
    this.addTestResult("开始基础测试...");
    
    const testScript = `
      (function() {
        console.log('=== 基础测试开始 ===');
        
        // 检查对象
        if (typeof window.${BASE_WIN_OBJ} !== 'undefined') {
          console.log('✅ ${BASE_WIN_OBJ} 对象存在');
          
          // 测试 client_isAlready
          if (typeof window.${BASE_WIN_OBJ}.client_isAlready === 'function') {
            console.log('✅ client_isAlready 方法存在');
            
            var testData = {
              title: '基础测试',
              subTitle: '测试副标题',
              objName: '${BASE_WIN_OBJ}',
              useTitleBar: true
            };
            
            console.log('📤 调用 client_isAlready');
            window.${BASE_WIN_OBJ}.client_isAlready(JSON.stringify(testData));
            
            return 'BASIC_TEST_SUCCESS';
          } else {
            console.log('❌ client_isAlready 方法不存在');
            return 'METHOD_NOT_FOUND';
          }
        } else {
          console.log('❌ ${BASE_WIN_OBJ} 对象不存在');
          return 'OBJECT_NOT_FOUND';
        }
      })();
    `;
    
    this.controller.runJavaScript(testScript)
      .then((result) => {
        this.addTestResult(`基础测试结果: ${result}`);
      })
      .catch((error: Error) => {
        this.addTestResult(`基础测试失败: ${error.message}`);
      });
  }

  build() {
    Column() {
      CommonToolbar({ config: ToolbarPresets.simple('WebView JavaScript 测试') })
      
      // 测试控制区域
      Column() {
        Text('WebView JavaScript 调用测试')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 16 })
        
        Row() {
          Button('运行基础测试')
            .onClick(() => this.runBasicTest())
            .backgroundColor('#1976D2')
            .margin({ right: 8 })
          
          Button('清空结果')
            .onClick(() => {
              this.testResults = [];
            })
            .backgroundColor('#FF9500')
        }
        .margin({ bottom: 16 })
        
        // 测试结果显示
        Text('测试结果:')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })
        
        Scroll() {
          Column() {
            ForEach(this.testResults, (result: string, index: number) => {
              Text(result)
                .fontSize(12)
                .fontFamily('monospace')
                .padding(4)
                .width('100%')
                .backgroundColor(index % 2 === 0 ? '#F5F5F5' : '#FFFFFF')
            })
          }
        }
        .height(150)
        .backgroundColor('#F8F8F8')
        .border({ width: 1, color: '#E0E0E0' })
        .borderRadius(4)
      }
      .padding(16)
      .backgroundColor('#FFFFFF')
      
      // 进度条
      Progress({
        value: this.progress,
        total: 100,
        type: ProgressType.Linear
      })
        .width('100%')
        .height(2)
        .color(Color.Blue)
        .visibility(this.isProgressVisible ? Visibility.Visible : Visibility.Hidden)

      // WebView区域
      Web({
        src: 'resource://rawfile/test_webview.html',
        controller: this.controller
      })
        .layoutWeight(1)
        .javaScriptAccess(true)
        .domStorageAccess(true)
        .fileAccess(true)
        .imageAccess(true)
        .onlineImageAccess(true)
        .geolocationAccess(true)
        .cacheMode(CacheMode.Online)
        .mixedMode(MixedMode.All)
        .databaseAccess(true)
        .javaScriptProxy(this.proxy)
        .onPageBegin((e) => {
          log(`测试页面开始加载: ${e?.url}`);
          this.addTestResult(`页面开始加载: ${e?.url}`);
        })
        .onPageEnd((e) => {
          log(`测试页面加载完成: ${e?.url}`);
          this.addTestResult(`页面加载完成: ${e?.url}`);
          
          // 自动运行测试
          setTimeout(() => {
            this.runBasicTest();
          }, 1000);
        })
        .onErrorReceive((e) => {
          log(`测试页面加载错误: ${e?.error?.getErrorInfo()}`);
          this.addTestResult(`页面加载错误: ${e?.error?.getErrorInfo()}`);
        })
        .onProgressChange((e) => {
          if (e) {
            this.progress = e.newProgress;
            this.isProgressVisible = this.progress > 0 && this.progress < 100;
            
            if (this.progress === 100) {
              setTimeout(() => {
                this.isProgressVisible = false;
              }, 300);
            }
          }
        })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F6FA')
  }
}
