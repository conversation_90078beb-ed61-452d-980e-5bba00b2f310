import { MineViewModel } from './MineViewModel';
import { MineItem } from './MineItem';

@ComponentV2
export struct MineTab {
  private viewModel: MineViewModel = new MineViewModel();

  build() {
    Column() {
      // 顶部标题
      Row() {
        Blank()
        Text('我的').fontSize(20).fontWeight(FontWeight.Bold)
        Blank()
      }
      .height(56)
      .alignItems(VerticalAlign.Center)

      // 个人信息卡片
      Row() {
        Image($r('app.media.avatar_placeholder')).width(48).height(48).borderRadius(24)
        Column() {
          Text('梁江涛').fontSize(16).fontWeight(FontWeight.Medium)
          Text('加优科技有限公司').fontSize(12).fontColor('#888')
        }
        .margin({ left: 12 })
        Blank()
        Button('编辑资料').fontSize(12).width(72).height(28)
          .onClick(() => this.viewModel.onEditProfile())
      }
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor('#fff')
      .borderRadius(12)
      .margin({ top: 12, left: 12, right: 12, bottom: 12 })

      // 功能列表
      Column() {
        MineItem({ icon: $r('app.media.ic_qrcode'), title: '二维码', onItemClick: (): void => this.viewModel.onQRCode() })
        MineItem({ icon: $r('app.media.ic_gift'), title: '员工福利', onItemClick: (): void => this.viewModel.onWelfare() })
        MineItem({ icon: $r('app.media.ic_verify'), title: '实名认证', tag: '未实名', tagColor: '#bbb', onItemClick: (): void => this.viewModel.onVerify() })
        MineItem({ icon: $r('app.media.ic_settings'), title: '设置', onItemClick: (): void => this.viewModel.onSettings() })
        MineItem({ icon: $r('app.media.ic_about'), title: '关于担当', onItemClick: (): void => this.viewModel.onAbout() })
      }
      .backgroundColor('#fff')
      .borderRadius(12)
      .margin({ left: 12, right: 12 })

      Blank()
    }
    .backgroundColor('#f5f6fa')
    .width('100%').height('100%')
  }
}
