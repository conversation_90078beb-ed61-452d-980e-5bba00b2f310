import router from '@ohos.router';
import web_webview from '@ohos.web.webview'
import { CommonToolbar, ToolbarPresets } from '../../components/CommonToolbar';
import { WebViewViewModel } from './WebViewViewModel';
import { WebViewParam } from '../../util/WebViewHelper';
import { BASE_WIN_OBJ, JSMethodArray, JsProxy } from './WebJS';
import { log } from '../../util/Log';
import { DeviceUtils } from '../../util/DeviceUtil';


/// 通用 web 加载组件
@Entry
@Component
struct WebViewPage {

  private controller = new web_webview.WebviewController();
  private vm = new WebViewViewModel();
  private jsProxy: JsProxy = new JsProxy(this.controller);

  // 用于控制进度条的状态变量
  @State progress: number = 0;
  @State isProgressVisible: boolean = false;

  aboutToAppear() {
    const json = this.getUIContext().getRouter().getParams() as Record<string, Object | string | number> ;
    log(`🔧 WebView 原始参数: ${JSON.stringify(json)}`);

    this.vm.param = WebViewParam.fromJson(json)

    log(`🌐 WebView 即将加载URL: ${this.getWebViewUrl()}`);



    console.log(`DeviceUtils.getDeviceInfo() = ${DeviceUtils.getDeviceInfo()}`)
  }

  // 安全获取WebView URL
  private getWebViewUrl(): string {
    try {
      if (this.vm.param) {
        const url = this.vm.param.getParamUrl();
        return url;
      } else {
        return 'about:blank';
      }
    } catch (error) {
      return 'about:blank';
    }
  }


  private proxy: JavaScriptProxy = {
    object: this.jsProxy,
    name: BASE_WIN_OBJ,
    methodList: JSMethodArray,
    controller: this.controller
  };

  build() {
    Column() {
      // 工具栏
      if ((this.vm.param?.isWebNavigation || 0) > 0) {
        CommonToolbar({ config: ToolbarPresets.simple(`${this.vm.title}`) });
      }
      // 顶部的加载进度条组件
      Progress({
        value: this.progress,
        total: 100,
        type: ProgressType.Linear
      })
        .width('100%')
        .height(1)
        .color(Color.Blue)
        .visibility(this.isProgressVisible ? Visibility.Visible : Visibility.Hidden)
        .align(Alignment.TopStart);

      Web({
        src: this.getWebViewUrl(),
        controller: this.controller
      })
        .javaScriptAccess(true)
        .domStorageAccess(true)
        .fileAccess(true)
        .imageAccess(true)
        .onlineImageAccess(true)
        .geolocationAccess(true)
        .cacheMode(CacheMode.Online)
        .mixedMode(MixedMode.All)
        .databaseAccess(true)
        .onTitleReceive((e) => {
          if (e) this.vm.title = e.title;
        })
        .onPageBegin((e) => {
          log(`🚀 WebView页面开始加载: ${e?.url}`);
          log(`📊 当前时间: ${new Date().toLocaleString()}`);
        })
        .onPageEnd((e) => {
          log(`✅ WebView页面加载完成: ${e?.url}`);
          log(`📊 加载完成时间: ${new Date().toLocaleString()}`);

        })
        .onErrorReceive((e) => {
          log(`❌ WebView加载错误: ${e?.error?.getErrorInfo()}`);
        })
        .javaScriptProxy(this.proxy)
        .onProgressChange((e) => {
          if (e) {
            this.progress = e.newProgress;
            // 当开始加载且进度未满100时，显示进度条
            if (this.progress > 0 && this.progress < 100) {
              this.isProgressVisible = true;
            }
            // 当加载完成时，延迟一小段时间再隐藏，以优化视觉效果
            else if (this.progress === 100) {
              setTimeout(() => {
                this.isProgressVisible = false;
              }, 300);
            }
          }
        });
    }
    .width('100%')
    .height('100%')
    .onAppear(() => {

    })
  }

  aboutToDisappear(): void {}

  onPageHide(): void {}
}