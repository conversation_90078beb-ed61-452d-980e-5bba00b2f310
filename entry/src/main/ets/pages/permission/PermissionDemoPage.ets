import { CommonToolbar, ToolbarPresets } from "../../components/CommonToolbar";
import { permissionManager } from "../../util/PermissionManager";
import { log } from "../../util/Log";
import { promptAction } from '@kit.ArkUI';
import { common } from '@kit.AbilityKit';
import { photoAccessHelper } from '@kit.MediaLibraryKit';
import { camera } from '@kit.CameraKit';
import { image } from '@kit.ImageKit';
import { fileIo } from '@kit.CoreFileKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { ImageHelper, ImageInfo } from '../../util/ImageHelper';


@Entry
@ComponentV2
export struct PermissionDemoPage {
  @Local permissionStatus: Map<string, boolean> = new Map();
  @Local isLoading: boolean = false;
  @Local capturedImageUri: string = '';
  @Local selectedImageUri: string = '';
  @Local capturedImageInfo: ImageInfo | null = null;
  @Local selectedImageInfo: ImageInfo | null = null;

  aboutToAppear(): void {
    this.checkAllPermissions();
  }

  // 检查所有权限状态
  async checkAllPermissions() {
    this.isLoading = true;
    try {
      const cameraGranted = await permissionManager.checkCameraPermission();
      const mediaPermissions = await permissionManager.checkMediaPermissions();
      const microphoneGranted = await permissionManager.checkMicrophonePermission();
      const locationGranted = await permissionManager.checkLocationPermission();

      this.permissionStatus.set('camera', cameraGranted);
      this.permissionStatus.set('microphone', microphoneGranted);
      this.permissionStatus.set('location', locationGranted);
      
      mediaPermissions.forEach((granted, permission) => {
        this.permissionStatus.set(permission, granted);
      });

      log(`权限状态检查完成: ${JSON.stringify(Array.from(this.permissionStatus.entries()))}`);
    } catch (error) {
      log(`检查权限状态失败: ${error}`);
    } finally {
      this.isLoading = false;
    }
  }

  // 申请相机权限
  async requestCameraPermission() {
    this.isLoading = true;
    try {
      const context = getContext(this) as common.UIAbilityContext;
      const granted = await permissionManager.requestCameraPermission(context);
      this.permissionStatus.set('camera', granted);

      if (granted) {
        promptAction.showToast({ message: '相机权限申请成功' });
      } else {
        promptAction.showToast({ message: '相机权限申请失败' });
      }
    } catch (error) {
      log(`申请相机权限失败: ${error}`);
      promptAction.showToast({ message: '申请相机权限出错' });
    } finally {
      this.isLoading = false;
    }
  }

  // 申请媒体权限
  async requestMediaPermissions() {
    this.isLoading = true;
    try {
      const context = getContext(this) as common.UIAbilityContext;
      const results = await permissionManager.requestMediaPermissions(context);

      results.forEach((granted, permission) => {
        this.permissionStatus.set(permission, granted);
      });

      const allGranted = Array.from(results.values()).every(granted => granted);
      if (allGranted) {
        promptAction.showToast({ message: '媒体权限申请成功' });
      } else {
        promptAction.showToast({ message: '部分媒体权限申请失败' });
      }
    } catch (error) {
      log(`申请媒体权限失败: ${error}`);
      promptAction.showToast({ message: '申请媒体权限出错' });
    } finally {
      this.isLoading = false;
    }
  }

  // 拍照功能 - 简化实现
  async takePhoto() {
    try {
      // 检查相机权限
      const hasPermission = await permissionManager.checkCameraPermission();
      if (!hasPermission) {
        const context = getContext(this) as common.UIAbilityContext;
        const granted = await permissionManager.requestCameraPermission(context);
        if (!granted) {
          promptAction.showToast({ message: '需要相机权限才能拍照' });
          return;
        }
      }

      // 模拟拍照功能 - 使用示例图片
      // 在实际项目中，这里应该调用相机API
      log('模拟拍照功能');

      // 使用一个示例图片URI（实际开发中应该是相机拍摄的图片）
      const sampleImageUri = 'resource://base/media/sample_image.jpg';

      this.capturedImageUri = sampleImageUri;

      // 模拟图片信息
      this.capturedImageInfo = {
        width: 1920,
        height: 1080,
        mimeType: 'image/jpeg',
        size: { width: 1920, height: 1080 },
        uri: sampleImageUri
      };

      log(`模拟拍照成功，图片URI: ${this.capturedImageUri}`);
      promptAction.showToast({ message: '拍照成功（模拟）' });

    } catch (error) {
      log(`拍照失败: ${error}`);
      promptAction.showToast({ message: '拍照失败' });
    }
  }

  // 选择相册图片 - 简化实现
  async selectFromGallery() {
    try {
      // 检查媒体权限
      const mediaPermissions = await permissionManager.checkMediaPermissions();
      const hasReadPermission = mediaPermissions.get('ohos.permission.READ_IMAGEVIDEO') || false;

      if (!hasReadPermission) {
        const context = getContext(this) as common.UIAbilityContext;
        const results = await permissionManager.requestMediaPermissions(context);
        const granted = results.get('ohos.permission.READ_IMAGEVIDEO') || false;
        if (!granted) {
          promptAction.showToast({ message: '需要读取图片权限才能选择相册' });
          return;
        }
      }

      // 模拟相册选择功能
      // 在实际项目中，这里应该调用系统相册选择器
      log('模拟相册选择功能');

      // 使用一个示例图片URI（实际开发中应该是用户选择的图片）
      const sampleImageUri = 'resource://base/media/sample_gallery.jpg';

      this.selectedImageUri = sampleImageUri;

      // 模拟图片信息
      this.selectedImageInfo = {
        width: 1280,
        height: 720,
        mimeType: 'image/jpeg',
        size: { width: 1280, height: 720 },
        uri: sampleImageUri
      };

      log(`模拟选择图片成功，图片URI: ${this.selectedImageUri}`);
      promptAction.showToast({ message: '选择图片成功（模拟）' });

    } catch (error) {
      log(`选择相册图片失败: ${error}`);
      promptAction.showToast({ message: '选择相册图片失败' });
    }
  }

  // 压缩图片
  async compressImage(uri: string, isFromCamera: boolean = true) {
    try {
      if (!uri) {
        promptAction.showToast({ message: '请先选择图片' });
        return;
      }

      this.isLoading = true;
      promptAction.showToast({ message: '正在压缩图片...' });

      const compressedUri = await ImageHelper.compressImage(uri, 70, 1280, 720);

      if (compressedUri) {
        if (isFromCamera) {
          this.capturedImageUri = compressedUri;
          this.capturedImageInfo = await ImageHelper.getImageInfo(compressedUri);
        } else {
          this.selectedImageUri = compressedUri;
          this.selectedImageInfo = await ImageHelper.getImageInfo(compressedUri);
        }

        log(`图片压缩成功: ${compressedUri}`);
        promptAction.showToast({ message: '图片压缩成功' });
      } else {
        promptAction.showToast({ message: '图片压缩失败' });
      }
    } catch (error) {
      log(`压缩图片失败: ${error}`);
      promptAction.showToast({ message: '压缩图片失败' });
    } finally {
      this.isLoading = false;
    }
  }

  // 转换为Base64
  async convertToBase64(uri: string) {
    try {
      if (!uri) {
        promptAction.showToast({ message: '请先选择图片' });
        return;
      }

      this.isLoading = true;
      promptAction.showToast({ message: '正在转换Base64...' });

      const base64 = await ImageHelper.imageToBase64(uri);

      if (base64) {
        log(`Base64转换成功，长度: ${base64.length}`);
        promptAction.showToast({ message: `Base64转换成功，长度: ${base64.length}` });

        // 这里可以将base64数据传递给其他组件或保存
        // 例如：回调给WebView或保存到本地
      } else {
        promptAction.showToast({ message: 'Base64转换失败' });
      }
    } catch (error) {
      log(`Base64转换失败: ${error}`);
      promptAction.showToast({ message: 'Base64转换失败' });
    } finally {
      this.isLoading = false;
    }
  }

  // 权限状态组件
  @Builder
  PermissionStatusItem(title: string, permission: string) {
    Row() {
      Text(title)
        .fontSize(16)
        .layoutWeight(1)
      
      Text(this.permissionStatus.get(permission) ? '已授权' : '未授权')
        .fontSize(14)
        .fontColor(this.permissionStatus.get(permission) ? '#07C160' : '#FF6B6B')
        .margin({ right: 8 })
      
      if (this.permissionStatus.get(permission)) {
        Image('/resources/base/media/im_newsHome_search.png')
          .width(20)
          .height(20)
          .fillColor('#07C160')
      } else {
        Image('/resources/base/media/im_newsHome_search.png')
          .width(20)
          .height(20)
          .fillColor('#FF6B6B')
      }
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
    .borderRadius(8)
    .margin({ bottom: 8 })
  }

  build() {
    Column() {
      CommonToolbar({ config: ToolbarPresets.simple('权限使用示例') })
      
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
          Text('检查权限中...')
            .fontSize(14)
            .margin({ top: 8 })
        }
        .justifyContent(FlexAlign.Center)
        .layoutWeight(1)
      } else {
        Scroll() {
          Column() {
            // 权限状态展示
            Text('权限状态')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ bottom: 16 })
              .alignSelf(ItemAlign.Start)

            this.PermissionStatusItem('相机权限', 'camera')
            this.PermissionStatusItem('麦克风权限', 'microphone')
            this.PermissionStatusItem('位置权限', 'location')
            this.PermissionStatusItem('读取媒体文件', 'ohos.permission.READ_MEDIA')
            this.PermissionStatusItem('读取图片视频', 'ohos.permission.READ_IMAGEVIDEO')

            // 权限申请按钮
            Text('权限申请')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 24, bottom: 16 })
              .alignSelf(ItemAlign.Start)

            Button('申请相机权限')
              .width('100%')
              .height(44)
              .backgroundColor('#1976D2')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.requestCameraPermission())

            Button('申请媒体权限')
              .width('100%')
              .height(44)
              .backgroundColor('#1976D2')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.requestMediaPermissions())

            // 功能演示
            Text('功能演示')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 24, bottom: 16 })
              .alignSelf(ItemAlign.Start)

            Button('拍照')
              .width('100%')
              .height(44)
              .backgroundColor('#07C160')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.takePhoto())

            Button('选择相册图片')
              .width('100%')
              .height(44)
              .backgroundColor('#07C160')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.selectFromGallery())

            Button('刷新权限状态')
              .width('100%')
              .height(44)
              .backgroundColor('#FF9500')
              .borderRadius(8)
              .margin({ bottom: 12 })
              .onClick(() => this.checkAllPermissions())

            // 图片显示区域
            Text('图片预览')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 24, bottom: 16 })
              .alignSelf(ItemAlign.Start)

            // 拍照图片显示
            if (this.capturedImageUri) {
              Column() {
                Row() {
                  Text('拍照图片')
                    .fontSize(14)
                    .fontColor('#666666')
                    .layoutWeight(1)

                  if (this.capturedImageInfo) {
                    Text(`${this.capturedImageInfo.width}×${this.capturedImageInfo.height}`)
                      .fontSize(12)
                      .fontColor('#999999')
                  }
                }
                .width('100%')
                .margin({ bottom: 8 })

                Image(this.capturedImageUri)
                  .width('100%')
                  .height(200)
                  .objectFit(ImageFit.Contain)
                  .backgroundColor('#F0F0F0')
                  .borderRadius(8)
                  .border({ width: 1, color: '#E0E0E0' })

                if (this.capturedImageInfo) {
                  Text(`类型: ${this.capturedImageInfo.mimeType}`)
                    .fontSize(12)
                    .fontColor('#999999')
                    .margin({ top: 4 })
                    .alignSelf(ItemAlign.Start)
                }

                Text(`URI: ${this.capturedImageUri}`)
                  .fontSize(12)
                  .fontColor('#999999')
                  .margin({ top: 4 })
                  .maxLines(2)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .alignSelf(ItemAlign.Start)

                // 操作按钮
                Row() {
                  Button('压缩')
                    .fontSize(12)
                    .height(32)
                    .backgroundColor('#4CAF50')
                    .layoutWeight(1)
                    .margin({ right: 4 })
                    .onClick(() => this.compressImage(this.capturedImageUri, true))

                  Button('转Base64')
                    .fontSize(12)
                    .height(32)
                    .backgroundColor('#2196F3')
                    .layoutWeight(1)
                    .margin({ left: 4 })
                    .onClick(() => this.convertToBase64(this.capturedImageUri))
                }
                .width('100%')
                .margin({ top: 8 })
              }
              .width('100%')
              .margin({ bottom: 16 })
              .alignItems(HorizontalAlign.Start)
            }

            // 相册图片显示
            if (this.selectedImageUri) {
              Column() {
                Row() {
                  Text('相册图片')
                    .fontSize(14)
                    .fontColor('#666666')
                    .layoutWeight(1)

                  if (this.selectedImageInfo) {
                    Text(`${this.selectedImageInfo.width}×${this.selectedImageInfo.height}`)
                      .fontSize(12)
                      .fontColor('#999999')
                  }
                }
                .width('100%')
                .margin({ bottom: 8 })

                Image(this.selectedImageUri)
                  .width('100%')
                  .height(200)
                  .objectFit(ImageFit.Contain)
                  .backgroundColor('#F0F0F0')
                  .borderRadius(8)
                  .border({ width: 1, color: '#E0E0E0' })

                if (this.selectedImageInfo) {
                  Text(`类型: ${this.selectedImageInfo.mimeType}`)
                    .fontSize(12)
                    .fontColor('#999999')
                    .margin({ top: 4 })
                    .alignSelf(ItemAlign.Start)
                }

                Text(`URI: ${this.selectedImageUri}`)
                  .fontSize(12)
                  .fontColor('#999999')
                  .margin({ top: 4 })
                  .maxLines(2)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
                  .alignSelf(ItemAlign.Start)

                // 操作按钮
                Row() {
                  Button('压缩')
                    .fontSize(12)
                    .height(32)
                    .backgroundColor('#4CAF50')
                    .layoutWeight(1)
                    .margin({ right: 4 })
                    .onClick(() => this.compressImage(this.selectedImageUri, false))

                  Button('转Base64')
                    .fontSize(12)
                    .height(32)
                    .backgroundColor('#2196F3')
                    .layoutWeight(1)
                    .margin({ left: 4 })
                    .onClick(() => this.convertToBase64(this.selectedImageUri))
                }
                .width('100%')
                .margin({ top: 8 })
              }
              .width('100%')
              .margin({ bottom: 16 })
              .alignItems(HorizontalAlign.Start)
            }

            // 清除图片按钮
            if (this.capturedImageUri || this.selectedImageUri) {
              Button('清除所有图片')
                .width('100%')
                .height(44)
                .backgroundColor('#FF6B6B')
                .borderRadius(8)
                .margin({ bottom: 12 })
                .onClick(() => {
                  this.capturedImageUri = '';
                  this.selectedImageUri = '';
                  this.capturedImageInfo = null;
                  this.selectedImageInfo = null;
                  promptAction.showToast({ message: '图片已清除' });
                })
            }
          }
          .padding(16)
        }
        .layoutWeight(1)
      }
    }
    .backgroundColor('#F5F6FA')
    .width('100%')
    .height('100%')
  }
}
