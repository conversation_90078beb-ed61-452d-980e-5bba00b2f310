# HarmonyOS axios拦截器日志系统使用指南

## 概述

为HarmonyOS项目的axios网络请求框架添加了完整的拦截器日志系统，支持详细的请求和响应日志记录，包括参数打印、敏感信息隐藏、性能监控等功能。

## 功能特性

### ✅ **完整的日志记录**
- 🚀 **请求日志**：记录请求方法、URL、头部、参数、请求体
- ✅ **响应日志**：记录响应状态、耗时、响应数据
- ❌ **错误日志**：记录错误详情、请求信息、错误类型

### ✅ **智能日志管理**
- 🔒 **敏感信息保护**：自动隐藏Authorization token
- 📏 **大数据截断**：超长响应数据自动截断显示
- ⏱️ **性能监控**：自动计算请求耗时
- 🎯 **请求追踪**：每个请求分配唯一ID便于追踪

### ✅ **可配置选项**
- 🎛️ **开关控制**：可独立控制请求和响应日志
- 📊 **大小限制**：可配置日志数据的最大显示长度
- 🎨 **格式美化**：使用emoji和分隔线美化日志输出

## 配置说明

### 网络配置 (NetworkConfig.ets)
```typescript
export class NetworkConfig {
  // 日志配置
  static readonly ENABLE_REQUEST_LOG: boolean = true;   // 启用请求日志
  static readonly ENABLE_RESPONSE_LOG: boolean = true;  // 启用响应日志
  static readonly LOG_MAX_BODY_SIZE: number = 1000;     // 最大日志数据长度
}
```

### 配置选项说明
- **ENABLE_REQUEST_LOG**: 控制是否打印请求日志
- **ENABLE_RESPONSE_LOG**: 控制是否打印响应日志  
- **LOG_MAX_BODY_SIZE**: 超过此长度的数据将被截断显示

## 日志输出格式

### 请求日志示例
```
================================================================================
🚀 [REQUEST A1B2C3] 2024-01-15T10:30:45.123Z
📍 GET http://*********:8768/org/company/v4/group/8888
📋 Headers:
  Content-Type: application/json
  Accept: application/json
  Authorization: Bearer e9deb3****872e
🔍 Query Params:
{
  "page": 1,
  "size": 20
}
📦 Request Body:
{
  "companyId": "8888"
}
================================================================================
```

### 响应日志示例
```
================================================================================
✅ [RESPONSE A1B2C3] 2024-01-15T10:30:45.456Z (333ms)
📍 GET /org/company/v4/group/8888
📊 Status: 200 OK
📦 Response Data:
{
  "code": 1,
  "msg": "请求成功",
  "data": {
    "toolsList": [
      {
        "icon": "https://cdn.ddbes.com/WORK_FLOW_ICON/workflow_home_attendance_up.png",
        "name": "考勤",
        "unreadInt": 0
      }
    ]
  }
}
📏 Full response size: 2345 characters
================================================================================
```

### 错误日志示例
```
================================================================================
❌ [ERROR A1B2C3] 2024-01-15T10:30:45.789Z (666ms)
📍 GET /org/company/v4/group/8888
📊 Status: 500 Internal Server Error
📦 Error Response:
{
  "code": 0,
  "msg": "服务器内部错误",
  "data": null
}
================================================================================
```

## 敏感信息保护

### Token隐藏机制
```typescript
// 原始token
Authorization: Bearer e9deb3c3-982b-43f4-b2bd-42506dd6872e

// 日志中显示
Authorization: Bearer e9deb3****872e
```

### 保护规则
- **Bearer Token**: 只显示前6位和后4位，中间用****替代
- **其他敏感数据**: 只显示前4位和后4位，中间用****替代
- **短数据**: 长度小于10的数据完全隐藏为****

## 性能监控

### 请求耗时统计
- 自动记录请求开始时间
- 计算请求总耗时（包括网络传输和服务器处理）
- 在响应日志中显示耗时信息

### 请求追踪
- 每个请求分配6位随机ID（如：A1B2C3）
- 请求和响应日志使用相同ID便于关联
- 错误日志也包含对应的请求ID

## 使用示例

### 基本使用
```typescript
// 发送请求（自动记录日志）
const response = await httpClient.get<CompanyToolsData>(
  '/org/company/v4/group/8888',
  undefined,
  'your-token'
);
```

### 配置日志级别
```typescript
// 只启用响应日志
NetworkConfig.ENABLE_REQUEST_LOG = false;
NetworkConfig.ENABLE_RESPONSE_LOG = true;

// 调整日志数据大小限制
NetworkConfig.LOG_MAX_BODY_SIZE = 2000;
```

### 在开发和生产环境中的使用
```typescript
// 开发环境：启用详细日志
if (process.env.NODE_ENV === 'development') {
  NetworkConfig.ENABLE_REQUEST_LOG = true;
  NetworkConfig.ENABLE_RESPONSE_LOG = true;
  NetworkConfig.LOG_MAX_BODY_SIZE = 5000;
}

// 生产环境：关闭或简化日志
if (process.env.NODE_ENV === 'production') {
  NetworkConfig.ENABLE_REQUEST_LOG = false;
  NetworkConfig.ENABLE_RESPONSE_LOG = false;
}
```

## 日志分析和调试

### 通过请求ID追踪问题
1. 在日志中找到出错的请求ID（如：A1B2C3）
2. 搜索该ID的请求日志，查看请求参数
3. 查看对应的响应或错误日志，分析问题原因

### 性能分析
1. 观察请求耗时，识别慢请求
2. 分析网络延迟和服务器响应时间
3. 优化请求参数和接口性能

### 数据验证
1. 检查请求参数是否正确
2. 验证响应数据格式和内容
3. 确认API接口的行为符合预期

## 最佳实践

### 1. 日志配置建议
```typescript
// 开发阶段：详细日志
NetworkConfig.ENABLE_REQUEST_LOG = true;
NetworkConfig.ENABLE_RESPONSE_LOG = true;
NetworkConfig.LOG_MAX_BODY_SIZE = 2000;

// 测试阶段：关键日志
NetworkConfig.ENABLE_REQUEST_LOG = false;
NetworkConfig.ENABLE_RESPONSE_LOG = true;
NetworkConfig.LOG_MAX_BODY_SIZE = 1000;

// 生产阶段：错误日志
NetworkConfig.ENABLE_REQUEST_LOG = false;
NetworkConfig.ENABLE_RESPONSE_LOG = false;
```

### 2. 敏感信息处理
- 确保所有认证信息都被正确隐藏
- 避免在日志中暴露用户隐私数据
- 定期检查日志输出，确保安全性

### 3. 性能优化
- 在生产环境中关闭详细日志以提升性能
- 合理设置LOG_MAX_BODY_SIZE避免内存占用过大
- 使用日志级别控制减少不必要的日志输出

### 4. 调试技巧
- 使用请求ID快速定位问题请求
- 结合耗时信息分析性能瓶颈
- 通过完整的请求响应日志复现问题

## 技术实现

### 核心组件
- **HttpClient**: 主要的网络请求客户端
- **NetworkConfig**: 配置管理
- **日志方法**: logRequest、logResponse、logError

### 关键特性
- **类型安全**: 使用TypeScript确保类型安全
- **错误处理**: 完善的错误捕获和日志记录
- **性能优化**: 避免不必要的字符串操作和对象创建

## 总结

axios拦截器日志系统为HarmonyOS项目提供了：

✅ **完整的请求追踪**：从请求发送到响应接收的全过程记录
✅ **智能的信息保护**：自动隐藏敏感数据确保安全
✅ **灵活的配置选项**：可根据环境和需求调整日志级别
✅ **美观的输出格式**：使用emoji和格式化提升可读性
✅ **强大的调试能力**：通过详细日志快速定位和解决问题

这套日志系统将大大提升开发效率和问题排查能力！🎉
