import util from '@ohos.util';

export class BufferUtils {
  // 任意 Protobuf 对象 → ArrayBuffer
  static objectToArrayBuffer(obj: object): ArrayBuffer {
    const jsonStr: string = JSON.stringify(obj);
    console.info(`objectToArrayBuffer ==> ${obj}`)
    const textBytes: Uint8Array = new util.TextEncoder().encode(jsonStr);

    const base64Helper = new util.Base64Helper();
    const base64Str: string = base64Helper.encodeToStringSync(textBytes);

    const finalBytes: Uint8Array = new util.TextEncoder().encode(base64Str);
    return finalBytes.buffer;
  }

  // ArrayBuffer → 任意对象（由调用方反序列化）
  static arrayBufferToObject<T>(buffer: ArrayBuffer): T {
    const base64Bytes = new Uint8Array(buffer);
    const base64Str = new util.TextDecoder().decode(base64Bytes);

    const base64Helper = new util.Base64Helper();
    const decodedBytes: Uint8Array = base64Helper.decodeSync(base64Str);

    const jsonStr = new util.TextDecoder().decode(decodedBytes);
    return JSON.parse(jsonStr) as T;
  }

  static stringToArrayBuffer(str: string): ArrayBuffer {
    const encoder = new util.TextEncoder();
    // encode() 方法返回一个 Uint8Array
    const encodedUint8Array = encoder.encode(str);

    // Uint8Array 的 buffer 属性就是其底层的 ArrayBuffer
    return encodedUint8Array.buffer;
  }

}
