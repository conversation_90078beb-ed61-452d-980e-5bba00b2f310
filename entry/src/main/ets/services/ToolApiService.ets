import { HttpClient, httpClient } from '../network/HttpClient';
import { NetworkConfig, ApiResponse } from '../network/NetworkConfig';
import { CompanyToolsData, ToolGridItem, ToolDataConverter } from '../models/ToolModels';
import { APIConfig } from '../network/API';

/**
 * 批量请求结果
 */
class BatchResult {
  companyId: string;
  success: boolean;
  tools: ToolGridItem[];
  error?: Error;

  constructor(
    companyId: string,
    success: boolean,
    tools: ToolGridItem[],
    error?: Error
  ) {
    this.companyId = companyId;
    this.success = success;
    this.tools = tools;
    this.error = error;
  }
}

/**
 * 工具API服务类
 */
export class ToolApiService {
  private static instance: ToolApiService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): ToolApiService {
    if (!ToolApiService.instance) {
      ToolApiService.instance = new ToolApiService();
    }
    return ToolApiService.instance;
  }

  /**
   * 获取公司工具列表
   * @param companyId 公司ID
   * @param token 访问令牌（可选，默认使用配置中的token）
   * @returns Promise<ToolGridItem[]>
   */
  public async getCompanyTools(companyId: string, token?: string): Promise<ToolGridItem[]> {
    try {
      console.info(`ToolApiService: 开始获取公司工具列表，公司ID: ${companyId}`);
      
      const url = `${APIConfig.COMPANY_TOOLS_PATH}/${companyId}`;
      const response: ApiResponse<CompanyToolsData> = await HttpClient.getInstance(APIConfig.BASE_URL).get(
        url,
        undefined,
        token
      );

      console.info(`ToolApiService: 成功获取工具数据，工具数量: ${response.data.toolsList.length}, 管理工具数量: ${response.data.managementOrgtList.length}`);

      // 转换为网格显示数据
      const gridItems = ToolDataConverter.convertToGridItems(response.data);
      
      console.info(`ToolApiService: 转换完成，总计网格项: ${gridItems.length}`);
      
      return gridItems;
    } catch (error) {
      console.error('ToolApiService: 获取公司工具列表失败', error);
      throw new Error(`获取公司工具列表失败: ${error}`);
    }
  }

  /**
   * 刷新工具数据
   * @param companyId 公司ID
   * @param token 访问令牌（可选）
   * @returns Promise<ToolGridItem[]>
   */
  public async refreshCompanyTools(companyId: string, token?: string): Promise<ToolGridItem[]> {
    console.info(`ToolApiService: 刷新公司工具数据，公司ID: ${companyId}`);
    return this.getCompanyTools(companyId, token);
  }

  /**
   * 获取工具详情（如果需要单独的详情接口）
   * @param toolKey 工具key
   * @param companyId 公司ID
   * @param token 访问令牌（可选）
   * @returns Promise<ToolGridItem | null>
   */
  public async getToolDetail(toolKey: string, companyId: string, token?: string): Promise<ToolGridItem | null> {
    try {
      console.info(`ToolApiService: 获取工具详情，工具key: ${toolKey}, 公司ID: ${companyId}`);
      
      // 先获取所有工具，然后筛选出指定的工具
      const allTools = await this.getCompanyTools(companyId, token);
      const tool = allTools.find(item => item.key === toolKey);
      
      if (tool) {
        console.info(`ToolApiService: 找到工具详情: ${tool.name}`);
        return tool;
      } else {
        console.warn(`ToolApiService: 未找到工具，key: ${toolKey}`);
        return null;
      }
    } catch (error) {
      console.error('ToolApiService: 获取工具详情失败', error);
      throw new Error(`获取工具详情失败: ${error}`);
    }
  }

  /**
   * 批量获取多个公司的工具（如果需要）
   * @param companyIds 公司ID数组
   * @param token 访问令牌（可选）
   * @returns Promise<Map<string, ToolGridItem[]>>
   */
  public async getBatchCompanyTools(companyIds: string[], token?: string): Promise<Map<string, ToolGridItem[]>> {
    console.info(`ToolApiService: 批量获取公司工具，公司数量: ${companyIds.length}`);
    
    const results = new Map<string, ToolGridItem[]>();
    
    // 并发请求所有公司的工具数据
    const promises = companyIds.map(async (companyId) => {
      try {
        const tools = await this.getCompanyTools(companyId, token);
        results.set(companyId, tools);
        return new BatchResult(companyId, true, tools, undefined);
      } catch (error) {
        console.error(`ToolApiService: 获取公司${companyId}工具失败`, error);
        results.set(companyId, []);
        return new BatchResult(companyId, false, [], error as Error);
      }
    });

    await Promise.allSettled(promises);
    
    console.info(`ToolApiService: 批量获取完成，成功获取${results.size}个公司的工具数据`);
    
    return results;
  }
}

// 导出单例实例
export const toolApiService = ToolApiService.getInstance();
