/**
 * 工具参数
 */
export interface ToolArgument {
  companyId?: string;
  orgId?: string;
  name?: string;
  index: number;
  isManager: boolean;
}

/**
 * 工具项接口
 */
export interface ToolItem {
  icon: string;
  darkModeIcon: string;
  name: string;
  unreadInt: number;
  showInt: number;
  webUrl: string;
  typeInt: number;
  key: string;
  refreshTime: number;
  backspaceKey: number;
  kingdeeTokenType: number;
  argument: ToolArgument;
  pcLinkMode?: number;
  pcUrlMethod?: number;
}

/**
 * 公司工具数据
 */
export interface CompanyToolsData {
  toolsList: ToolItem[];
  managementOrgtList: ToolItem[];
}

/**
 * 工具网格项（用于UI显示）
 */
export interface ToolGridItem {
  id: string;
  name: string;
  icon: string;
  darkModeIcon?: string;
  badge?: number;
  webUrl: string;
  type: 'tool' | 'management';
  key: string;
  argument?: ToolArgument;
}

/**
 * 分离的工具数据接口
 */
export interface SeparatedToolsData {
  tools: WorkBenchUIData[];
  management: WorkBenchUIData[];
}

export class WorkBenchUIData implements ToolGridItem {
  id: string = "";
  name: string = "";
  icon: string = "";
  darkModeIcon?: string | undefined;
  badge?: number | undefined;
  webUrl: string = "";
  type: "tool" | "management" = "tool";
  key: string = "";
  argument?: ToolArgument | undefined;

  constructor(data?: Partial<ToolGridItem>) {
    if (data) {
      this.id = data.id || "";
      this.name = data.name || "";
      this.icon = data.icon || "";
      this.darkModeIcon = data.darkModeIcon;
      this.badge = data.badge;
      this.webUrl = data.webUrl || "";
      this.type = data.type || "tool";
      this.key = data.key || "";
      this.argument = data.argument;
    }
  }

  isTool(): boolean {
    return this.type === 'tool';
  }

  isManagement(): boolean {
    return this.type === 'management';
  }

  hasRedDot(): boolean {
    return true;
    // return this.badge !== undefined && this.badge > 0;
  }

  getBadgeText(): string {
    return "";
    // if (!this.badge || this.badge <= 0) return '';
    // return this.badge > 99 ? '99+' : this.badge.toString();
  }
}


/**
 * 工具数据转换工具类
 */
export class ToolDataConverter {
  /**
   * 将API返回的工具数据转换为网格显示数据
   */
  static convertToGridItems(data: CompanyToolsData): WorkBenchUIData[] {
    const gridItems: WorkBenchUIData[] = [];

    // 转换工具列表
    data.toolsList.forEach((tool, index) => {
      gridItems.push(new WorkBenchUIData({
        id: `tool_${tool.key}_${index}`,
        name: tool.name,
        icon: tool.icon,
        darkModeIcon: tool.darkModeIcon,
        badge: tool.unreadInt > 0 ? tool.unreadInt : undefined,
        webUrl: tool.webUrl,
        type: 'tool',
        key: tool.key,
        argument: tool.argument
      }));
    });

    // 转换管理工具列表
    data.managementOrgtList.forEach((tool, index) => {
      gridItems.push(new WorkBenchUIData({
        id: `management_${tool.key}_${index}`,
        name: tool.name,
        icon: tool.icon,
        darkModeIcon: tool.darkModeIcon,
        badge: tool.unreadInt > 0 ? tool.unreadInt : undefined,
        webUrl: tool.webUrl,
        type: 'management',
        key: tool.key,
        argument: tool.argument
      }));
    });

    return gridItems;
  }

  /**
   * 分离工具数据和管理数据
   */
  static separateToolsAndManagement(data: CompanyToolsData): SeparatedToolsData {
    const tools: WorkBenchUIData[] = [];
    const management: WorkBenchUIData[] = [];

    // 转换工具列表
    data.toolsList.forEach((tool, index) => {
      tools.push(new WorkBenchUIData({
        id: `tool_${tool.key}_${index}`,
        name: tool.name,
        icon: tool.icon,
        darkModeIcon: tool.darkModeIcon,
        badge: tool.unreadInt > 0 ? tool.unreadInt : undefined,
        webUrl: tool.webUrl,
        type: 'tool',
        key: tool.key,
        argument: tool.argument
      }));
    });

    // 转换管理工具列表
    data.managementOrgtList.forEach((tool, index) => {
      management.push(new WorkBenchUIData({
        id: `management_${tool.key}_${index}`,
        name: tool.name,
        icon: tool.icon,
        darkModeIcon: tool.darkModeIcon,
        badge: tool.unreadInt > 0 ? tool.unreadInt : undefined,
        webUrl: tool.webUrl,
        type: 'management',
        key: tool.key,
        argument: tool.argument
      }));
    });

    const result: SeparatedToolsData = { tools, management };
    return result;
  }
  
  /**
   * 将网格项转换回工具项
   */
  static convertToToolItem(gridItem: ToolGridItem): ToolItem {
    return {
      icon: gridItem.icon,
      darkModeIcon: gridItem.darkModeIcon || gridItem.icon,
      name: gridItem.name,
      unreadInt: gridItem.badge || 0,
      showInt: 0,
      webUrl: gridItem.webUrl,
      typeInt: gridItem.type === 'tool' ? 3 : 4,
      key: gridItem.key,
      refreshTime: Date.now(),
      backspaceKey: 0,
      kingdeeTokenType: 1,
      argument: gridItem.argument || {
        companyId: undefined,
        orgId: undefined,
        name: undefined,
        index: 0,
        isManager: true
      },
      pcLinkMode: 0,
      pcUrlMethod: 0
    };
  }
}
