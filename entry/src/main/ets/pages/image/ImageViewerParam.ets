/**
 * 图片浏览器参数
 */
export class ImageViewerParam {
  index: number;        // 当前显示的图片索引
  list: string[];       // 图片链接列表

  constructor(index: number = 0, list: string[] = []) {
    this.index = index;
    this.list = list;
  }

  /**
   * 从JSON字符串创建参数对象
   */
  static fromJson(jsonStr: string): ImageViewerParam {
    try {
      const data = JSON.parse(jsonStr) as ImageViewerParam;
      return new ImageViewerParam(data.index || 0, data.list || []);
    } catch (error) {
      console.error('解析图片浏览器参数失败:', error);
      return new ImageViewerParam();
    }
  }

  /**
   * 转换为JSON字符串
   */
  toJson(): string {
    return JSON.stringify({
      index: this.index,
      list: this.list
    });
  }

  /**
   * 验证参数是否有效
   */
  isValid(): boolean {
    return true;
    return this.list.length > 0 &&
           this.index >= 0 && 
           this.index < this.list.length;
  }

  /**
   * 获取当前图片URL
   */
  getCurrentImageUrl(): string {
    if (this.isValid()) {
      return this.list[this.index];
    }
    return '';
  }

  /**
   * 获取图片总数
   */
  getTotalCount(): number {
    return this.list.length;
  }
}
