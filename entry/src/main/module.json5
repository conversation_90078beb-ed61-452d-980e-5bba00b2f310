{
  "module": {
    "name": "entry",
    "type": "entry",
    "description": "$string:module_desc",
    "mainElement": "EntryAbility",
    "deviceTypes": [
      "phone",
      "tablet",
      "2in1",
      "wearable"
    ],
    "requestPermissions": [
      {
        "name": "ohos.permission.INTERNET"
      },
      {
        "name": "ohos.permission.CAMERA",
        "reason": "$string:permission_camera_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.READ_MEDIA",
        "reason": "$string:permission_read_media_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.WRITE_MEDIA",
        "reason": "$string:permission_write_media_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.MEDIA_LOCATION",
        "reason": "$string:permission_media_location_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.READ_IMAGEVIDEO",
        "reason": "$string:permission_read_imagevideo_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.WRITE_IMAGEVIDEO",
        "reason": "$string:permission_write_imagevideo_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.MICROPHONE",
        "reason": "$string:permission_microphone_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.LOCATION",
        "reason": "$string:permission_location_reason",
        "usedScene": {
          "abilities": [
            "DDMainbility"
          ],
          "when": "inuse"
        }
      }
    ],
    "deliveryWithInstall": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "DDMainbility",
        "srcEntry": "./ets/entryability/DDMainbility.ets",
        "description": "$string:C_desc",
        "icon": "$media:layered_image",
        "label": "$string:C_label",
        "startWindowIcon": "$media:startIcon",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          {
            "entities": [
              "entity.system.home"
            ],
            "actions": [
              "action.system.home"
            ]
          }
        ]
      },
//      {
//        "name": "ChatAbility",
//        "srcEntry": "./ets/entryability/ChatAbility.ets",
//        "description": "$string:ChatAbility_desc",
//        "icon": "$media:layered_image",
//        "label": "$string:ChatAbility_label",
//        "startWindowIcon": "$media:startIcon",
//        "startWindowBackground": "$color:start_window_background"
//      },
//      {
//        "name": "WebViewAbility",
//        "srcEntry": "./ets/entryability/WebViewAbility.ets",
//        "description": "$string:WebViewAbility_desc",
//        "icon": "$media:layered_image",
//        "label": "$string:WebViewAbility_label",
//        "startWindowIcon": "$media:startIcon",
//        "startWindowBackground": "$color:start_window_background"
//      }
    ],
    "extensionAbilities": [
      {
        "name": "EntryBackupAbility",
        "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets",
        "type": "backup",
        "exported": false,
        "metadata": [
          {
            "name": "ohos.extension.backup",
            "resource": "$profile:backup_config"
          }
        ],
      }
    ]
  }
}