import { UIAbility, AbilityConstant, Want } from '@kit.AbilityKit';
import { window } from '@kit.ArkUI';
import { dataOrmService } from '../database/DataOrmService';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { AppUtil } from '../util/AppUtil';
import { log } from '../util/Log';

const DOMAIN = 0x0000;

export default class CAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // 异步初始化数据库
    this.initializeDatabase();
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await dataOrmService.initialize(this.context);
    } catch (error) {
      hilog.error(DOMAIN, 'testTag', 'DataORM initialization failed: %{public}s', JSON.stringify(error));
    }
  }

  onDestroy(): void {}
  onWindowStageCreate(windowStage: window.WindowStage): void {
    windowStage.loadContent('pages/DDMainPage');
    AppUtil.init(this.context , windowStage)
  }
  onWindowStageDestroy(): void {}
  onForeground(): void {}
  onBackground(): void {}
} 