
// 【client_isAlready】 方法 传递过来的参数
export interface  ClientIsAlreadyBody {
  title?:string;
  subTitle?:string;
  objName?:string;
  useTitleBar?:boolean;

}


// 定义Token数据接口
export interface TokenData {
  token: string;
  expireTime: number;
}

// 定义用户数据接口
export interface UserData {
  userId: string;
  userName: string;
  avatar: string;
}

export interface ClientFindDeviceInfo {
  device: string;
  deviceModel: string;
}

export interface TransImage2Web {
  count?:number;
  type?: string;
  images?: string[];
}


export interface CompanyInfo {
  companyId?:string;
  deptId?:string;
  logo?:string;
  name?:string;
  content?:string;
  noticeId?:string;
  power?:string;
  rejectInvitation?:number;
  rejectJoin?:number;
  isSelected?:boolean;
  haveApprove?:number;
  isOuter?:number;
}