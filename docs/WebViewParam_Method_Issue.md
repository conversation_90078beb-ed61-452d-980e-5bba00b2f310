# WebViewParam 方法调用问题分析

## 🤔 **问题现象**

```typescript
public getParamUrl(): string {
    return `${this.url}?platform=1`
}
```

这个简单的方法调用 `this.vm.param?.getParamUrl()` 会报错：`is not callable`

## 🔍 **根本原因**

### 问题在于对象序列化和反序列化

当通过路由传递参数时，发生了以下过程：

1. **发送端**：
   ```typescript
   // 创建WebViewParam实例
   const param = new WebViewParam(url, title, 0);
   routeTo(context, 'pages/webview/WebViewPage', param);
   ```

2. **路由系统**：
   ```typescript
   // RouteHelper.ets
   export function routeTo(context: UIContext, path: string, param?: Object) {
     context.getRouter().pushUrl({
       url: path,
       params: param  // 这里发生了序列化
     });
   }
   ```

3. **接收端**：
   ```typescript
   // WebViewPage.ets
   const params = this.getUIContext().getRouter().getParams() as WebViewParam;
   // 这里得到的是普通对象，不是WebViewParam实例！
   ```

### 序列化过程中丢失了什么？

- ✅ **属性保留**: `url`, `title`, `isWebNavigation` 等属性正常传递
- ❌ **方法丢失**: `getParamUrl()` 方法在序列化过程中丢失
- ❌ **原型链断裂**: 对象不再是 `WebViewParam` 的实例

## 🔧 **解决方案**

### 方案1: 重新创建实例（推荐）

```typescript
aboutToAppear() {
  const params = this.getUIContext().getRouter().getParams();
  
  if (params && typeof params === 'object') {
    const paramObj = params as Record<string, ESObject>;
    if (paramObj.url && paramObj.title !== undefined && paramObj.isWebNavigation !== undefined) {
      // 从普通对象重新创建WebViewParam实例
      this.vm.param = new WebViewParam(
        paramObj.url as string,
        paramObj.title as string,
        paramObj.isWebNavigation as number
      );
    }
  }
}
```

### 方案2: 直接拼接URL

```typescript
private getWebViewUrl(): string {
  if (this.vm.param && this.vm.param.url) {
    return `${this.vm.param.url}?platform=1`;
  }
  return 'about:blank';
}
```

### 方案3: 修改RouteHelper（更彻底的解决方案）

```typescript
export function routeTo(context: UIContext, path: string, param?: WebViewParam) {
  // 如果是WebViewParam，转换为普通对象
  let routeParams = param;
  if (param instanceof WebViewParam) {
    routeParams = {
      url: param.url,
      title: param.title,
      isWebNavigation: param.isWebNavigation,
      // 可以添加一个标识
      _isWebViewParam: true
    };
  }
  
  context.getRouter().pushUrl({
    url: path,
    params: routeParams
  });
}
```

## 🎯 **为什么会这样？**

### HarmonyOS路由系统的特点

1. **JSON序列化**: 路由参数通过JSON序列化传递
2. **只保留数据**: 只有可序列化的属性被保留
3. **方法丢失**: 函数/方法无法序列化，会丢失
4. **原型链断裂**: 反序列化后是普通对象，不是原始类的实例

### 这是正常行为

这不是bug，而是路由系统的设计特点：
- 确保数据的可靠传递
- 避免复杂对象引用问题
- 保持跨页面的数据一致性

## 🚀 **最佳实践**

### 1. **简单数据传递**
```typescript
// ✅ 推荐：传递简单数据
routeTo(context, 'page', { url: 'xxx', title: 'yyy' });
```

### 2. **接收端重建对象**
```typescript
// ✅ 推荐：接收端重建复杂对象
const data = getParams();
const instance = new MyClass(data.prop1, data.prop2);
```

### 3. **避免依赖方法**
```typescript
// ✅ 推荐：在接收端实现逻辑
private getUrlWithParams(baseUrl: string): string {
  return `${baseUrl}?platform=1`;
}
```

## 📊 **验证修复**

修复后应该看到：
```
🔧 WebView 原始参数: {"url":"xxx","title":"yyy","isWebNavigation":0}
🔧 参数类型: object
🔧 重新创建WebViewParam实例成功
🔧 getParamUrl方法: true
✅ 成功获取URL: xxx?platform=1
```

这样就能正常调用 `getParamUrl()` 方法了！
