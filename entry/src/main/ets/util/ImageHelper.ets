import { image } from '@kit.ImageKit';
import { fileIo } from '@kit.CoreFileKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { log } from './Log';

/**
 * 图片处理工具类
 * 提供图片压缩、格式转换、信息获取等功能
 */
export class ImageHelper {
  
  /**
   * 获取图片信息
   * @param uri 图片URI
   * @returns Promise<ImageInfo | null> 图片信息
   */
  static async getImageInfo(uri: string): Promise<ImageInfo | null> {
    try {
      const imageSource = image.createImageSource(uri);
      const imageInfo = await imageSource.getImageInfo();
      
      return {
        width: imageInfo.size.width,
        height: imageInfo.size.height,
        mimeType: imageInfo.mimeType,
        size: imageInfo.size,
        uri: uri
      };
    } catch (error) {
      log(`获取图片信息失败: ${error}`);
      return null;
    }
  }

  /**
   * 压缩图片
   * @param uri 原图片URI
   * @param quality 压缩质量 (0-100)
   * @param maxWidth 最大宽度
   * @param maxHeight 最大高度
   * @returns Promise<string | null> 压缩后的图片URI
   */
  static async compressImage(
    uri: string, 
    quality: number = 80, 
    maxWidth: number = 1920, 
    maxHeight: number = 1080
  ): Promise<string | null> {
    try {
      const imageSource = image.createImageSource(uri);
      const imageInfo = await imageSource.getImageInfo();
      
      // 计算压缩比例
      let scale = 1;
      if (imageInfo.size.width > maxWidth || imageInfo.size.height > maxHeight) {
        const scaleX = maxWidth / imageInfo.size.width;
        const scaleY = maxHeight / imageInfo.size.height;
        scale = Math.min(scaleX, scaleY);
      }

      const decodingOptions: image.DecodingOptions = {
        sampleSize: Math.ceil(1 / scale),
        editable: true,
        desiredSize: {
          width: Math.floor(imageInfo.size.width * scale),
          height: Math.floor(imageInfo.size.height * scale)
        }
      };

      const pixelMap = await imageSource.createPixelMap(decodingOptions);
      
      // 创建图片打包器
      const imagePackerApi = image.createImagePacker();
      const packOpts: image.PackingOption = {
        format: 'image/jpeg',
        quality: quality
      };

      const compressedData = await imagePackerApi.packing(pixelMap, packOpts);
      
      // 保存压缩后的图片
      const compressedUri = await saveImageData(compressedData, 'compressed_image.jpg');
      
      // 释放资源
      pixelMap.release();
      imagePackerApi.release();
      
      return compressedUri;
    } catch (error) {
      log(`压缩图片失败: ${error}`);
      return null;
    }
  }

  /**
   * 将图片转换为Base64
   * @param uri 图片URI
   * @returns Promise<string | null> Base64字符串
   */
  static async imageToBase64(uri: string): Promise<string | null> {
    try {
      const file = fileIo.openSync(uri, fileIo.OpenMode.READ_ONLY);
      const buffer = new ArrayBuffer(fileIo.statSync(uri).size);
      fileIo.readSync(file.fd, buffer);
      fileIo.closeSync(file);
      
      // 转换为Base64
      const uint8Array = new Uint8Array(buffer);
      let binary = '';
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      
      return btoa(binary);
    } catch (error) {
      log(`图片转Base64失败: ${error}`);
      return null;
    }
  }

  /**
   * 保存图片数据到文件
   * @param data 图片数据
   * @param fileName 文件名
   * @returns Promise<string | null> 保存后的文件URI
   */
  public static async saveImageData(data: ArrayBuffer, fileName: string): Promise<string | null> {
    try {
      // 使用应用缓存目录
      const cacheDir = '/data/storage/el2/base/cache'; // 简化的缓存路径
      const filePath = `${cacheDir}/${fileName}`;

      const file = fileIo.openSync(filePath, fileIo.OpenMode.READ_WRITE | fileIo.OpenMode.CREATE);
      fileIo.writeSync(file.fd, data);
      fileIo.closeSync(file);

      return `file://${filePath}`;
    } catch (error) {
      log(`保存图片数据失败: ${error}`);
      return null;
    }
  }

  /**
   * 获取图片文件大小
   * @param uri 图片URI
   * @returns Promise<number> 文件大小（字节）
   */
  static async getImageSize(uri: string): Promise<number> {
    try {
      const stat = fileIo.statSync(uri);
      return stat.size;
    } catch (error) {
      log(`获取图片大小失败: ${error}`);
      return 0;
    }
  }

  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查是否为图片文件
   * @param uri 文件URI
   * @returns boolean 是否为图片
   */
  static isImageFile(uri: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const lowerUri = uri.toLowerCase();
    return imageExtensions.some(ext => lowerUri.endsWith(ext));
  }
}

/**
 * 图片信息接口
 */
export interface ImageInfo {
  width: number;
  height: number;
  mimeType: string;
  size: image.Size;
  uri: string;
}

/**
 * 图片处理选项接口
 */
export interface ImageProcessOptions {
  quality?: number;        // 压缩质量 (0-100)
  maxWidth?: number;       // 最大宽度
  maxHeight?: number;      // 最大高度
  format?: string;         // 输出格式
}
