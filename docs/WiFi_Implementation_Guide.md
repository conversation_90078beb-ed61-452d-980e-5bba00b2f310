# WiFi信息获取功能实现

## 🎯 **功能概述**

已为WebView中的JavaScript调用实现了完整的WiFi信息获取功能，支持获取当前连接的WiFi网络信息。

## 🔧 **实现的功能**

### 1. **JavaScript调用接口**
```javascript
// 在Web页面中调用
window.DDBESOFFICE.client_getWifiInfo();

// 接收回调
window.ddbes_web.findWifiInfo = function(wifiInfoJson) {
  const wifiInfo = JSON.parse(wifiInfoJson);
  console.log('WiFi信息:', wifiInfo);
};
```

### 2. **返回数据格式**
```typescript
interface WifiInfo {
  bssid: string;        // WiFi设备地址 (如: "aa:bb:cc:dd:ee:ff")
  ssid: string;         // WiFi名称 (如: "MyWiFi")
  wifiState: number;    // WiFi状态
}
```

### 3. **WiFi状态说明**
- **1**: 获取到WiFi信息 - 成功连接到WiFi网络
- **0**: 未获取到 - WiFi未启用或未连接到网络
- **-1**: 无权限 - 应用没有WiFi权限

## 🏗️ **技术实现**

### 1. **权限检查**
```typescript
private async checkWifiPermission(): Promise<boolean> {
  const context = getContext(this) as common.UIAbilityContext;
  const atManager = abilityAccessCtrl.createAtManager();
  const bundleInfo = await bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
  
  const wifiStatePermission = await atManager.checkAccessToken(
    bundleInfo.appInfo.accessTokenId, 
    'ohos.permission.GET_WIFI_INFO'
  );
  
  return wifiStatePermission === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED;
}
```

### 2. **WiFi信息获取**
```typescript
private async getCurrentWifiInfo(): Promise<WifiInfo> {
  // 检查WiFi是否启用
  const isWifiEnabled = wifiManager.isWifiEnabled();
  
  if (!isWifiEnabled) {
    return { bssid: '', ssid: '', wifiState: 0 };
  }
  
  // 获取当前连接的WiFi信息
  const wifiLinkedInfo = wifiManager.getLinkedInfo();
  
  if (wifiLinkedInfo && wifiLinkedInfo.ssid && wifiLinkedInfo.bssid) {
    // 清理SSID中的引号
    let cleanSsid = wifiLinkedInfo.ssid;
    if (cleanSsid.startsWith('"') && cleanSsid.endsWith('"')) {
      cleanSsid = cleanSsid.slice(1, -1);
    }
    
    // 检查是否为有效的WiFi连接
    if (cleanSsid !== '<unknown ssid>' && wifiLinkedInfo.bssid !== '00:00:00:00:00:00') {
      return {
        bssid: wifiLinkedInfo.bssid,
        ssid: cleanSsid,
        wifiState: 1
      };
    }
  }
  
  return { bssid: '', ssid: '', wifiState: 0 };
}
```

### 3. **完整的调用流程**
```typescript
private async injectionWifi() {
  try {
    // 1. 检查WiFi权限
    const hasPermission = await this.checkWifiPermission();
    
    if (!hasPermission) {
      const wifiInfo: WifiInfo = { bssid: '', ssid: '', wifiState: -1 };
      this.callJavaScriptMethod('findWifiInfo', JSON.stringify(wifiInfo));
      return;
    }
    
    // 2. 获取WiFi信息
    const wifiInfo = await this.getCurrentWifiInfo();
    
    // 3. 返回给JavaScript
    this.callJavaScriptMethod('findWifiInfo', JSON.stringify(wifiInfo));
    
  } catch (error) {
    // 4. 错误处理
    const wifiInfo: WifiInfo = { bssid: '', ssid: '', wifiState: 0 };
    this.callJavaScriptMethod('findWifiInfo', JSON.stringify(wifiInfo));
  }
}
```

## 📱 **权限配置**

### 1. **module.json5配置**
```json
{
  "requestPermissions": [
    {
      "name": "ohos.permission.GET_WIFI_INFO",
      "reason": "$string:permission_wifi_reason",
      "usedScene": {
        "abilities": ["DDMainbility"],
        "when": "inuse"
      }
    }
  ]
}
```

### 2. **字符串资源配置**
```json
{
  "name": "permission_wifi_reason",
  "value": "需要WiFi权限来获取当前连接的WiFi网络信息"
}
```

## 🎨 **使用示例**

### 1. **Web页面调用**
```html
<!DOCTYPE html>
<html>
<head>
    <title>WiFi信息获取测试</title>
</head>
<body>
    <button onclick="getWifiInfo()">获取WiFi信息</button>
    <div id="result"></div>

    <script>
        // 定义回调函数
        window.ddbes_web = window.ddbes_web || {};
        window.ddbes_web.findWifiInfo = function(wifiInfoJson) {
            const wifiInfo = JSON.parse(wifiInfoJson);
            const resultDiv = document.getElementById('result');
            
            if (wifiInfo.wifiState === 1) {
                resultDiv.innerHTML = `
                    <h3>WiFi信息获取成功</h3>
                    <p>WiFi名称: ${wifiInfo.ssid}</p>
                    <p>设备地址: ${wifiInfo.bssid}</p>
                `;
            } else if (wifiInfo.wifiState === 0) {
                resultDiv.innerHTML = '<p>未连接到WiFi网络</p>';
            } else if (wifiInfo.wifiState === -1) {
                resultDiv.innerHTML = '<p>没有WiFi权限</p>';
            }
        };
        
        // 调用原生方法
        function getWifiInfo() {
            if (window.DDBESOFFICE && window.DDBESOFFICE.client_getWifiInfo) {
                window.DDBESOFFICE.client_getWifiInfo();
            } else {
                alert('原生方法不可用');
            }
        }
    </script>
</body>
</html>
```

### 2. **返回数据示例**

#### 成功获取WiFi信息
```json
{
  "bssid": "aa:bb:cc:dd:ee:ff",
  "ssid": "MyHomeWiFi",
  "wifiState": 1
}
```

#### 未连接WiFi
```json
{
  "bssid": "",
  "ssid": "",
  "wifiState": 0
}
```

#### 无权限
```json
{
  "bssid": "",
  "ssid": "",
  "wifiState": -1
}
```

## 🔍 **调试和日志**

### 日志输出示例
```
🔔🔔🔔 === 开始获取WiFi信息 === 🔔🔔🔔
📋 WiFi权限检查结果: true
✅ 获取到WiFi信息: SSID=MyWiFi, BSSID=aa:bb:cc:dd:ee:ff
📶 WiFi信息获取结果: {"bssid":"aa:bb:cc:dd:ee:ff","ssid":"MyWiFi","wifiState":1}
```

### 错误情况日志
```
🔔🔔🔔 === 开始获取WiFi信息 === 🔔🔔🔔
❌ 没有WiFi权限
```

## ✅ **功能特性**

### 1. **完整的权限管理**
- 自动检查WiFi权限
- 权限不足时返回相应状态码
- 详细的权限检查日志

### 2. **智能数据处理**
- 自动清理SSID中的引号
- 过滤无效的WiFi连接信息
- 处理各种边界情况

### 3. **错误处理机制**
- 完善的异常捕获
- 降级处理策略
- 详细的错误日志

### 4. **兼容性保证**
- 与Android版本保持一致的接口
- 相同的数据格式和状态码
- 统一的JavaScript回调方法

## 🚀 **测试建议**

### 1. **基本功能测试**
- 连接WiFi时获取信息
- 未连接WiFi时的状态
- 权限被拒绝时的处理

### 2. **边界情况测试**
- WiFi名称包含特殊字符
- 切换WiFi网络
- WiFi开关状态变化

### 3. **权限测试**
- 首次安装时的权限申请
- 权限被拒绝后的处理
- 权限恢复后的功能

## 📝 **总结**

WiFi信息获取功能已完整实现：

1. ✅ **JavaScript接口** - `client_getWifiInfo()`
2. ✅ **权限管理** - 自动检查和处理WiFi权限
3. ✅ **数据获取** - 获取SSID、BSSID和连接状态
4. ✅ **错误处理** - 完善的异常处理机制
5. ✅ **日志调试** - 详细的调试信息输出

现在Web页面可以通过JavaScript调用获取当前设备的WiFi连接信息了！
