@Component
export struct MineItem {
  private icon?: Resource;
  private title: string = '';
  private tag?: string;
  private tagColor?: string;
  private onItemClick?: () => void;

  build() {
    Row() {
      Image(this.icon).width(24).height(24).margin({ right: 12 })
      Text(this.title).fontSize(16).layoutWeight(1)
      if (this.tag) {
        Text(this.tag)
          .fontSize(12)
          .fontColor(this.tagColor ?? '#bbb')
          .backgroundColor('#f0f0f0')
          .borderRadius(8)
          .padding({ left: 8, right: 8, top: 2, bottom: 2 })
          .margin({ right: 8 })
      }
      Image($r('app.media.ic_public_arrow_right')).width(16).height(16).opacity(0.5)
    }
    .height(48)
    .padding({ left: 16, right: 16 })
    .onClick(() => { if (this.onItemClick) this.onItemClick(); })
  }
}
