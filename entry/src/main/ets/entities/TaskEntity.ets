import { Entity, Columns, Id, NotNull, Index, ColumnType } from '@ohos/dataorm';
import { Task } from '../data_source/SessionBody';

/**
 * Task实体类 - 任务数据
 */
@Entity('TASKS')
export class TaskEntity {
  @Id()
  @Columns({ columnName: 'ID', types: ColumnType.str })
  id: string;

  @NotNull()
  @Index()
  @Columns({ columnName: 'TITLE', types: ColumnType.str })
  title: string;

  @Columns({ columnName: 'DESCRIPTION', types: ColumnType.str })
  description: string;

  @NotNull()
  @Columns({ columnName: 'STATUS', types: ColumnType.str })
  status: string; // 'pending' | 'in-progress' | 'completed'

  @Columns({ columnName: 'PRIORITY', types: ColumnType.str })
  priority: string; // 'high' | 'medium' | 'low'

  @Columns({ columnName: 'DUE_DATE', types: ColumnType.str })
  dueDate: string;

  @Columns({ columnName: 'ASSIGNEE', types: ColumnType.str })
  assignee: string;

  @Columns({ columnName: 'CREATE_TIME', types: ColumnType.num })
  createTime: number;

  @Columns({ columnName: 'UPDATE_TIME', types: ColumnType.num })
  updateTime: number;

  constructor(
    id?: string,
    title?: string,
    description?: string,
    status?: string,
    priority?: string,
    dueDate?: string,
    assignee?: string,
    createTime?: number,
    updateTime?: number
  ) {
    this.id = id || '';
    this.title = title || '';
    this.description = description || '';
    this.status = status || 'pending';
    this.priority = priority || 'medium';
    this.dueDate = dueDate || '';
    this.assignee = assignee || '';
    this.createTime = createTime || Math.floor(Date.now() / 1000);
    this.updateTime = updateTime || Math.floor(Date.now() / 1000);
  }

  // Getter和Setter方法
  getId(): string {
    return this.id;
  }

  setId(id: string): void {
    this.id = id;
  }

  getTitle(): string {
    return this.title;
  }

  setTitle(title: string): void {
    this.title = title;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getDescription(): string {
    return this.description;
  }

  setDescription(description: string): void {
    this.description = description;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getStatus(): string {
    return this.status;
  }

  setStatus(status: string): void {
    this.status = status;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getPriority(): string {
    return this.priority;
  }

  setPriority(priority: string): void {
    this.priority = priority;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getDueDate(): string {
    return this.dueDate;
  }

  setDueDate(dueDate: string): void {
    this.dueDate = dueDate;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getAssignee(): string {
    return this.assignee;
  }

  setAssignee(assignee: string): void {
    this.assignee = assignee;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getCreateTime(): number {
    return this.createTime;
  }

  setCreateTime(createTime: number): void {
    this.createTime = createTime;
  }

  getUpdateTime(): number {
    return this.updateTime;
  }

  setUpdateTime(updateTime: number): void {
    this.updateTime = updateTime;
  }

  // 转换为Task接口的方法
  toTask(): Task {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      status: this.status as 'pending' | 'in-progress' | 'completed',
      priority: this.priority as 'high' | 'medium' | 'low',
      dueDate: this.dueDate,
      assignee: this.assignee
    };
  }

  // 从Task接口创建实体的静态方法
  static fromTask(task: Task): TaskEntity {
    return new TaskEntity(
      task.id,
      task.title,
      task.description,
      task.status,
      task.priority,
      task.dueDate,
      task.assignee,
      Math.floor(Date.now() / 1000),
      Math.floor(Date.now() / 1000)
    );
  }
}


