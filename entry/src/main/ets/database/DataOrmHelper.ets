import { Context } from '@kit.AbilityKit';
import { OpenHelper, Database, DaoMaster, DaoSession, AbstractDao } from '@ohos/dataorm';
import { SessionEntity } from '../entities/SessionEntity';
import { TaskEntity } from '../entities/TaskEntity';
import { WorkItemEntity } from '../entities/WorkItemEntity';
import { UserEntity } from '../entities/UserEntity';
import { ToolCacheEntity } from '../entities/ToolCacheEntity';
import { defaultSessions, defaultWorkItems } from './TestData';

/**
 * dataORM数据库帮助类
 */
export class DataOrmHelper extends OpenHelper {
  private static readonly DB_NAME = 'app_database.db';
  private static readonly DB_VERSION = 3;

  constructor(context: Context) {
    super(context, DataOrmHelper.DB_NAME);
  }

  /**
   * 数据库升级时调用
   */
  async onUpgradeDatabase(db: Database, oldVersion: number, newVersion: number): Promise<void> {
    console.info(`DataOrmHelper: 数据库升级 ${oldVersion} -> ${newVersion}`);
    // 这里可以添加数据库升级逻辑
  }

  /**
   * 数据库降级时调用
   */
  async onDowngradeDatabase(db: Database, oldVersion: number, newVersion: number): Promise<void> {
    console.info(`DataOrmHelper: 数据库降级 ${oldVersion} -> ${newVersion}`);
    // 这里可以添加数据库降级逻辑
  }

  /**
   * 获取数据库实例并初始化
   */
  public async initializeDatabase(context: Context): Promise<DaoSession> {
    try {
      console.info('DataOrmHelper: 开始初始化数据库');
      
      // 设置实体类
      this.setEntities(SessionEntity, TaskEntity, WorkItemEntity, UserEntity, ToolCacheEntity);
      
      // 获取可写数据库
      const db: Database = await this.getWritableDb();
      
      // 创建DaoSession
      const daoSession = new DaoMaster(db).newSession();
      
      console.info('DataOrmHelper: 数据库初始化成功');
      
      // 初始化默认数据
      await this.initializeDefaultData(daoSession);
      
      return daoSession;
    } catch (error) {
      console.error('DataOrmHelper: 数据库初始化失败', error);
      throw new Error(`数据库初始化失败: ${error}`);
    }
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(daoSession: DaoSession): Promise<void> {
    try {
      console.info('DataOrmHelper: 开始初始化默认数据');
      
      // 检查是否已有数据
      const sessionDao = daoSession.getBaseDao(SessionEntity) as AbstractDao<SessionEntity, number>;
      const existingSessions = await sessionDao.loadAll();
      
      if (existingSessions.length === 0) {
        console.info('DataOrmHelper: 插入默认会话数据');
        await this.insertDefaultSessions(daoSession);
      }
      
      // 检查任务数据
      const taskDao = daoSession.getBaseDao(TaskEntity) as AbstractDao<TaskEntity, string>;
      const existingTasks = await taskDao.loadAll();
      
      if (existingTasks.length === 0) {
        console.info('DataOrmHelper: 插入默认任务数据');
        await this.insertDefaultTasks(daoSession);
      }
      
      // 检查工作台应用数据
      const workItemDao = daoSession.getBaseDao(WorkItemEntity) as AbstractDao<WorkItemEntity, string>;
      const existingWorkItems = await workItemDao.loadAll();

      if (existingWorkItems.length === 0) {
        console.info('DataOrmHelper: 插入默认工作台应用数据');
        await this.insertDefaultWorkItems(daoSession);
      }

      // 检查用户数据
      const userDao = daoSession.getBaseDao(UserEntity) as AbstractDao<UserEntity, string>;
      const existingUsers = await userDao.loadAll();

      if (existingUsers.length === 0) {
        console.info('DataOrmHelper: 插入默认用户数据');
        await this.insertDefaultUsers(daoSession);
      }

      console.info('DataOrmHelper: 默认数据初始化完成');
    } catch (error) {
      console.error('DataOrmHelper: 初始化默认数据失败', error);
    }
  }

  /**
   * 插入默认会话数据(测试假数据)
   */
  private async insertDefaultSessions(daoSession: DaoSession): Promise<void> {
    const sessionDao = daoSession.getBaseDao(SessionEntity) as AbstractDao<SessionEntity, number>;
    
    const testDatas = defaultSessions;

    for (const session of testDatas) {
      await sessionDao.insert(session);
    }
  }

  /**
   * 插入默认任务数据
   */
  private async insertDefaultTasks(daoSession: DaoSession): Promise<void> {
    const taskDao = daoSession.getBaseDao(TaskEntity) as AbstractDao<TaskEntity, string>;
    
    const defaultTasks = [
      new TaskEntity('1', '完成项目文档', '编写项目技术文档和用户手册', 'in-progress', 'high', '2024-03-15', '张三'),
      new TaskEntity('2', '代码审查', '审查新功能的代码实现', 'pending', 'medium', '2024-03-10', '李四'),
      new TaskEntity('3', '测试用例编写', '为新功能编写单元测试', 'completed', 'medium', '2024-03-05', '王五'),
      new TaskEntity('4', '性能优化', '优化应用启动速度', 'pending', 'low', '2024-03-20', '赵六'),
    ];

    for (const task of defaultTasks) {
      await taskDao.insert(task);
    }
  }

  /**
   * 插入默认工作台应用数据
   */
  private async insertDefaultWorkItems(daoSession: DaoSession): Promise<void> {
    const workItemDao = daoSession.getBaseDao(WorkItemEntity) as AbstractDao<WorkItemEntity, string>;
    
    let workList = defaultWorkItems;

    for (const workItem of workList) {
      await workItemDao.insert(workItem);
    }
  }

  /**
   * 插入默认用户数据
   */
  private async insertDefaultUsers(daoSession: DaoSession): Promise<void> {
    const userDao = daoSession.getBaseDao(UserEntity) as AbstractDao<UserEntity, string>;

    const defaultUsers = [
      new UserEntity('user001', '张三', 'token_123456'),
      new UserEntity('user002', '李四', 'token_789012'),
    ];

    for (const user of defaultUsers) {
      await userDao.insert(user);
    }
  }
}
