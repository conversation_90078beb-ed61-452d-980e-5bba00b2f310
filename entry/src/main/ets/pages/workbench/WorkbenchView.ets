import { WorkItem } from '../../data_source/SessionBody';
import { toast } from '../../util/Toast';
import { WorkbenchViewModel } from './WorkbenchViewModel';
import { ToolGridItem, WorkBenchUIData } from '../../models/ToolModels';
import { promptAction } from '@kit.ArkUI';
import { CommonToolbar, ToolbarPresets } from '../../components/CommonToolbar';

/// 工作台tab
@ComponentV2
export struct WorkbenchView {

  viewModel: WorkbenchViewModel = new WorkbenchViewModel();

  aboutToAppear(): void {
    this.viewModel.fetchWorkbenchList();
  }

  aboutToDisappear(): void {
    // 清理资源
    this.viewModel.dispose();
  }

  build() {
    Column() {
      // 使用通用工具栏
      CommonToolbar({
        config: ToolbarPresets.noBack('工作台', [
          {
            text: '刷新',
            onClick: () => this.viewModel.refreshData()
          },
          {
            text: '⋯',
            onClick: () => this.showMoreMenu()
          }
        ])
      })

      // 主要内容区域
      Scroll() {
        Column() {
          // 宣传横幅
          this.BannerBuilder()

          // 常用工具区域（横向滚动）
          // this.FrequentToolsBuilder()

          // 管理企业区域
          this.ManagementSectionBuilder()

          // 全部应用区域
          this.AllToolsSectionBuilder()
        }
        .padding({ left: 16, right: 16 })
      }
      .layoutWeight(1)
      .backgroundColor('#F5F5F5')

    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  // 宣传横幅构建器
  @Builder
  BannerBuilder() {
    Stack() {
      // 背景渐变
      Row()
        .width('100%')
        .height(120)
        .borderRadius(12)
        .linearGradient({
          direction: GradientDirection.Right,
          colors: [['#667eea', 0.0], ['#764ba2', 1.0]]
        })

      // 内容
      Row() {
        Column() {
          Text('担当办公 全新起航')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#fff')
            .margin({ bottom: 4 })

          Text('Every job requires commitment.')
            .fontSize(12)
            .fontColor('#E8E8E8')
        }
        .alignItems(HorizontalAlign.Start)

        Blank()

        // 装饰图标
        Row() {
          ForEach([1, 2, 3, 4, 5, 6], (item: number) => {
            Circle({ width: 24, height: 24 })
              .fill(Color.White)
              .opacity(0.2)
              .margin({ left: item > 1 ? -8 : 0 })
          })
        }
      }
      .width('100%')
      .padding(16)
    }
    .width('100%')
    .margin({ top: 16, bottom: 16 })
  }

  // 常用工具构建器（横向滚动）
  @Builder
  FrequentToolsBuilder() {
    if (this.viewModel.isLoading) {
      this.LoadingBuilder()
    } else if (this.viewModel.errorMessage) {
      this.ErrorBuilder()
    } else {
      Column() {
        // 标题行
        Row() {
          Text('◀')
            .fontSize(14)
            .fontColor('#666')
            .margin({ right: 8 })

          Text('去')
            .fontSize(14)
            .fontColor('#666')

          Blank()

          Text('更多')
            .fontSize(12)
            .fontColor('#007AFF')
        }
        .width('100%')
        .margin({ bottom: 12 })

        // 横向滚动的工具列表
        Scroll() {
          Row() {
            ForEach(this.viewModel.toolsData.slice(0, 6), (tool: WorkBenchUIData) => {
              this.FrequentToolItemBuilder(tool)
            })
          }
          .padding({ left: 4, right: 4 })
        }
        .scrollable(ScrollDirection.Horizontal)
        .scrollBar(BarState.Off)
      }
      .width('100%')
      .margin({ bottom: 20 })
    }
  }

  // 常用工具项构建器
  @Builder
  FrequentToolItemBuilder(tool: WorkBenchUIData) {
    Column() {
      Stack() {
        // 工具图标背景
        Circle({ width: 48, height: 48 })
          .fill(this.getToolIconColor(tool.key))

        // 工具图标
        Image(tool.icon)
          .width(32)
          .height(32)
          .borderRadius(4)

        // 红点徽章
        if (tool.hasRedDot()) {
          Circle({ width: 16, height: 16 })
            .fill('#FF3B30')
            .position({ x: 32, y: -4 })

          Text(tool.getBadgeText())
            .fontSize(10)
            .fontColor('#fff')
            .position({ x: 32, y: -4 })
        }
      }

      Text(tool.name)
        .fontSize(12)
        .fontColor('#333')
        .margin({ top: 8 })
        .maxLines(1)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
    }
    .width(80)
    .padding(8)
    .onClick(() => {
      this.viewModel.onToolClick(this.getUIContext() ,tool);
      // toast(`点击了${tool.name}`);
    })
  }

  // 管理企业区域构建器
  @Builder
  ManagementSectionBuilder() {
    if (this.viewModel.managementData.length > 0) {
      Column() {
        // 标题
        Row() {
          Text('管理企业')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333')

          Blank()
        }
        .width('100%')
        .margin({ bottom: 12 })

        // 管理工具网格（2列）
        Grid() {
          ForEach(this.viewModel.managementData, (tool: WorkBenchUIData) => {
            GridItem() {
              this.ManagementItemBuilder(tool)
            }
            .onClick(() => {
              this.viewModel.onToolClick(this.getUIContext() , tool);
              // toast(`点击了${tool.name}`);
            })
          })
        }
        .columnsTemplate('1fr 1fr')
        .rowsGap(12)
        .columnsGap(12)
        .height(Math.ceil(this.viewModel.managementData.length / 2) * 80)
      }
      .width('100%')
      .margin({ bottom: 20 })
    }
  }

  // 管理工具项构建器
  @Builder
  ManagementItemBuilder(tool: WorkBenchUIData) {
    Row() {
      // 工具图标
      Stack() {
        Circle({ width: 40, height: 40 })
          .fill('#4A90E2')

        Image(tool.icon)
          .width(24)
          .height(24)
          .fillColor('#fff')

        if (tool.hasRedDot()) {
          Circle({ width: 12, height: 12 })
            .fill('#FF3B30')
            .position({ x: 28, y: -2 })
        }
      }
      .margin({ right: 12 })

      // 工具信息
      Column() {
        Text(tool.name)
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333')
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      Blank()
    }
    .width('100%')
    .height(60)
    .backgroundColor('#fff')
    .borderRadius(8)
    .padding({ left: 12, right: 12 })
    .shadow({
      radius: 2,
      color: '#1A000000',
      offsetX: 0,
      offsetY: 1
    })
  }

  // 全部应用区域构建器
  @Builder
  AllToolsSectionBuilder() {
    Column() {
      // 标题
      Row() {
        Text('全部应用')
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333')

        Blank()
      }
      .width('100%')
      .margin({ bottom: 12 })

      // 应用网格（4列）
      Grid() {
        ForEach(this.viewModel.toolsData, (tool: WorkBenchUIData) => {
          GridItem() {
            this.AppItemBuilder(tool)
          }
          .onClick(() => {
            this.viewModel.onToolClick(this.getUIContext() ,tool);
            // toast(`点击了${tool.name}`);
          })
        })
      }
      .columnsTemplate('1fr 1fr 1fr 1fr')
      .rowsGap(16)
      .columnsGap(12)
      .height(Math.ceil(this.viewModel.toolsData.length / 4) * 100)
    }
    .width('100%')
    .margin({ bottom: 20 })
  }

  // 应用项构建器
  @Builder
  AppItemBuilder(tool: WorkBenchUIData) {
    Column() {
      Stack() {
        // 应用图标
        Image(tool.icon)
          .width(48)
          .height(48)
          .borderRadius(12)

        // 红点徽章
        if (tool.hasRedDot()) {
          Circle({ width: 10, height: 10 })
            .fill('#FF3B30')
            .position({ x: 39, y: -4 })

          // todo 暂时不显示角标数
          Text(tool.getBadgeText())
            .fontSize(10)
            .fontColor('#fff')
            .position({ x: 36, y: -4 })
        }
      }

      Text(tool.name)
        .fontSize(12)
        .fontColor('#333')
        .margin({ top: 8 })
        .maxLines(1)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
        .textAlign(TextAlign.Center)
    }
    .width('100%')
    .height(80)
    .justifyContent(FlexAlign.Center)
  }

  // 加载状态构建器
  @Builder
  LoadingBuilder() {
    Column() {
      LoadingProgress()
        .width(24)
        .height(24)
      Text('加载中...')
        .fontSize(14)
        .fontColor('#666')
        .margin({ top: 8 })
    }
    .width('100%')
    .height(120)
    .justifyContent(FlexAlign.Center)
  }

  // 错误状态构建器
  @Builder
  ErrorBuilder() {
    Column() {
      Text('😞')
        .fontSize(32)
      Text(this.viewModel.errorMessage)
        .fontSize(14)
        .fontColor('#666')
        .margin({ top: 8 })
        .textAlign(TextAlign.Center)

      Button('重试')
        .fontSize(14)
        .height(32)
        .margin({ top: 16 })
        .onClick(() => {
          this.viewModel.refreshData();
        })
    }
    .width('100%')
    .height(120)
    .justifyContent(FlexAlign.Center)
  }

  // 获取工具图标颜色
  private getToolIconColor(key: string): string {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    const index = key.length % colors.length;
    return colors[index];
  }

  // 显示更多菜单
  private showMoreMenu(): void {
    promptAction.showDialog({
      title: '更多操作',
      message: '选择要执行的操作',
      buttons: [
        {
          text: '刷新数据',
          color: '#007AFF'
        },
        {
          text: '清除缓存',
          color: '#007AFF'
        },
        {
          text: '设置',
          color: '#007AFF'
        },
        {
          text: '取消',
          color: '#666666'
        }
      ]
    }).then((result) => {
      switch (result.index) {
        case 0:
          this.viewModel.refreshData();
          toast('正在刷新数据...');
          break;
        case 1:
          toast('缓存已清除');
          break;
        case 2:
          toast('打开设置页面');
          break;
        default:
          break;
      }
    }).catch((error: Error) => {
      console.error('显示菜单失败', error);
    });
  }
}