import { Entity, Columns, Id, NotNull, Index, ColumnType } from '@ohos/dataorm';
import { Session } from '../data_source/SessionBody';

/**
 * Session实体类 - 会话数据
 */
@Entity('SESSIONS')
export class SessionEntity {
  @Id({ autoincrement: true })
  @Columns({ columnName: 'ID', types: ColumnType.num })
  id?: number;

  @NotNull()
  @Index()
  @Columns({ columnName: 'NAME', types: ColumnType.str })
  name: string;

  @Columns({ columnName: 'AVATAR', types: ColumnType.str })
  avatar: string;

  @Columns({ columnName: 'MESSAGE', types: ColumnType.str })
  message: string;

  @Columns({ columnName: 'TIME', types: ColumnType.str })
  time: string;

  @Columns({ columnName: 'UNREAD', types: ColumnType.num })
  unread: number;

  @Columns({ columnName: 'TAG', types: ColumnType.str })
  tag?: string;

  @Columns({ columnName: 'TAG_COLOR', types: ColumnType.str })
  tagColor?: string;

  @Columns({ columnName: 'DOT', types: ColumnType.num })
  dot: number; // 0 = false, 1 = true

  @Columns({ columnName: 'SESSION_TYPE', types: ColumnType.num })
  sessionType: number; // 1 = 单聊, 2 = 群聊

  @Columns({ columnName: 'IS_TOP', types: ColumnType.num })
  isTop: number; // 0 = false, 1 = true

  @Columns({ columnName: 'CREATE_TIME', types: ColumnType.num })
  createTime: number;

  @Columns({ columnName: 'UPDATE_TIME', types: ColumnType.num })
  updateTime: number;

  constructor(
    id?: number,
    name?: string,
    avatar?: string,
    message?: string,
    time?: string,
    unread?: number,
    tag?: string,
    tagColor?: string,
    dot?: number,
    sessionType?: number,
    isTop?: number,
    createTime?: number,
    updateTime?: number
  ) {
    this.id = id;
    this.name = name || '';
    this.avatar = avatar || '';
    this.message = message || '';
    this.time = time || '';
    this.unread = unread || 0;
    this.tag = tag;
    this.tagColor = tagColor;
    this.dot = dot || 0;
    this.sessionType = sessionType || 1;
    this.isTop = isTop || 0;
    this.createTime = createTime || Math.floor(Date.now() / 1000);
    this.updateTime = updateTime || Math.floor(Date.now() / 1000);
  }

  // Getter和Setter方法
  getId(): number | undefined {
    return this.id;
  }

  setId(id: number): void {
    this.id = id;
  }

  getName(): string {
    return this.name;
  }

  setName(name: string): void {
    this.name = name;
  }

  getAvatar(): string {
    return this.avatar;
  }

  setAvatar(avatar: string): void {
    this.avatar = avatar;
  }

  getMessage(): string {
    return this.message;
  }

  setMessage(message: string): void {
    this.message = message;
  }

  getTime(): string {
    return this.time;
  }

  setTime(time: string): void {
    this.time = time;
  }

  getUnread(): number {
    return this.unread;
  }

  setUnread(unread: number): void {
    this.unread = unread;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getTag(): string | undefined {
    return this.tag;
  }

  setTag(tag: string | undefined): void {
    this.tag = tag;
  }

  getTagColor(): string | undefined {
    return this.tagColor;
  }

  setTagColor(tagColor: string | undefined): void {
    this.tagColor = tagColor;
  }

  getDot(): boolean {
    return this.dot === 1;
  }

  setDot(dot: boolean): void {
    this.dot = dot ? 1 : 0;
  }

  getSessionType(): number {
    return this.sessionType;
  }

  setSessionType(sessionType: number): void {
    this.sessionType = sessionType;
  }

  getIsTop(): boolean {
    return this.isTop === 1;
  }

  setIsTop(isTop: boolean): void {
    this.isTop = isTop ? 1 : 0;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getCreateTime(): number {
    return this.createTime;
  }

  setCreateTime(createTime: number): void {
    this.createTime = createTime;
  }

  getUpdateTime(): number {
    return this.updateTime;
  }

  setUpdateTime(updateTime: number): void {
    this.updateTime = updateTime;
  }

  // 转换为Session接口的方法
  toSession(): Session {
    return {
      name: this.name,
      avatar: this.avatar,
      message: this.message,
      time: this.time,
      unread: this.unread,
      tag: this.tag,
      tagColor: this.tagColor,
      dot: this.getDot(),
      sessionType: this.sessionType,
      isTop: this.getIsTop()
    };
  }

  // 从Session接口创建实体的静态方法
  static fromSession(session: Session): SessionEntity {
    return new SessionEntity(
      undefined, // id由数据库自动生成
      session.name,
      session.avatar,
      session.message,
      session.time,
      session.unread,
      session.tag,
      session.tagColor,
      session.dot ? 1 : 0,
      session.sessionType,
      session.isTop ? 1 : 0,
      Math.floor(Date.now() / 1000),
      Math.floor(Date.now() / 1000)
    );
  }
}


