{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/axios@^2.2.6": "@ohos/axios@2.2.6", "@ohos/dataorm@^2.2.6": "@ohos/dataorm@2.2.6", "@ohos/hamock@1.0.0": "@ohos/hamock@1.0.0", "@ohos/hypium@1.0.21": "@ohos/hypium@1.0.21", "@ohos/protobufjs@^2.1.0": "@ohos/protobufjs@2.1.0", "@protobufjs/aspromise@^1.1.1": "@protobufjs/aspromise@1.1.2", "@protobufjs/aspromise@^1.1.2": "@protobufjs/aspromise@1.1.2", "@protobufjs/base64@^1.1.2": "@protobufjs/base64@1.1.2", "@protobufjs/codegen@^2.0.4": "@protobufjs/codegen@2.0.4", "@protobufjs/eventemitter@^1.1.0": "@protobufjs/eventemitter@1.1.0", "@protobufjs/fetch@^1.1.0": "@protobufjs/fetch@1.1.0", "@protobufjs/float@^1.0.2": "@protobufjs/float@1.0.2", "@protobufjs/inquire@^1.1.0": "@protobufjs/inquire@1.1.0", "@protobufjs/path@^1.1.2": "@protobufjs/path@1.1.2", "@protobufjs/pool@^1.1.0": "@protobufjs/pool@1.1.0", "@protobufjs/utf8@^1.1.0": "@protobufjs/utf8@1.1.0", "@types/node@>=13.7.0": "@types/node@18.15.11", "base64-js@^1.3.1": "base64-js@1.5.1", "buffer@^6.0.3": "buffer@6.0.3", "ieee754@^1.2.1": "ieee754@1.2.1", "long@^5.0.0": "long@5.2.1", "long@^5.2.1": "long@5.2.1", "protobufjs@^7.2.4": "protobufjs@7.2.4", "reflect-metadata@^0.1.13": "reflect-metadata@0.2.1"}, "packages": {"@ohos/axios@2.2.6": {"name": "@ohos/axios", "version": "2.2.6", "integrity": "sha512-A1JqGe6XaeqWyjQETitFW4EkubQS7Fv7h0YG5a/ry3/a/vOgVGzwC4y5KAhvMzVv1tYjfY0ntMtV2kJGlmOHcQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/axios/-/axios-2.2.6.har", "registryType": "ohpm"}, "@ohos/dataorm@2.2.6": {"name": "@ohos/dataorm", "version": "2.2.6", "integrity": "sha512-qwAQnMxfuG5Imsrq3jjikEUCC/FGqddj0YXe40zUELGdhHegEqkDaxtTp9KIq4fg1ZU/c8fxYNfApx3XZ7E6cA==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/dataorm/-/dataorm-2.2.6.har", "registryType": "ohpm", "dependencies": {"reflect-metadata": "^0.1.13"}}, "@ohos/hamock@1.0.0": {"name": "@ohos/hamock", "version": "1.0.0", "integrity": "sha512-K6lDPYc6VkKe6ZBNQa9aoG+ZZMiwqfcR/7yAVFSUGIuOAhPvCJAo9+t1fZnpe0dBRBPxj2bxPPbKh69VuyAtDg==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hamock/-/hamock-1.0.0.har", "registryType": "ohpm"}, "@ohos/hypium@1.0.21": {"name": "@ohos/hypium", "version": "1.0.21", "integrity": "sha512-iyKGMXxE+9PpCkqEwu0VykN/7hNpb+QOeIuHwkmZnxOpI+dFZt6yhPB7k89EgV1MiSK/ieV/hMjr5Z2mWwRfMQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hypium/-/hypium-1.0.21.har", "registryType": "ohpm"}, "@ohos/protobufjs@2.1.0": {"name": "@ohos/protobufjs", "version": "2.1.0", "integrity": "sha512-L4GXODgXG5QPU56sXCAo3nOqNz2QLp/ObY9nrjuObBRE9jRUQRxYC24TVZbbuWJLiLctJ/7wzFoOYH2GmGN/uw==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/protobufjs/-/protobufjs-2.1.0.har", "registryType": "ohpm", "dependencies": {"@protobufjs/utf8": "^1.1.0", "@protobufjs/eventemitter": "^1.1.0", "@types/node": ">=13.7.0", "@protobufjs/path": "^1.1.2", "@protobufjs/float": "^1.0.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/base64": "^1.1.2", "long": "^5.2.1", "@protobufjs/aspromise": "^1.1.2", "buffer": "^6.0.3"}}, "@protobufjs/aspromise@1.1.2": {"name": "@protobufjs/aspromise", "version": "1.1.2", "integrity": "sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "shasum": "9b8b0cc663d669a7d8f6f5d0893a14d348f30fbf", "registryType": "ohpm"}, "@protobufjs/base64@1.1.2": {"name": "@protobufjs/base64", "version": "1.1.2", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/base64/-/base64-1.1.2.tgz", "shasum": "4c85730e59b9a1f1f349047dbf24296034bb2735", "registryType": "ohpm"}, "@protobufjs/codegen@2.0.4": {"name": "@protobufjs/codegen", "version": "2.0.4", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/codegen/-/codegen-2.0.4.tgz", "shasum": "7ef37f0d010fb028ad1ad59722e506d9262815cb", "registryType": "ohpm"}, "@protobufjs/eventemitter@1.1.0": {"name": "@protobufjs/eventemitter", "version": "1.1.0", "integrity": "sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "shasum": "355cbc98bafad5978f9ed095f397621f1d066b70", "registryType": "ohpm"}, "@protobufjs/fetch@1.1.0": {"name": "@protobufjs/fetch", "version": "1.1.0", "integrity": "sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/fetch/-/fetch-1.1.0.tgz", "shasum": "ba99fb598614af65700c1619ff06d454b0d84c45", "registryType": "ohpm", "dependencies": {"@protobufjs/inquire": "^1.1.0", "@protobufjs/aspromise": "^1.1.1"}}, "@protobufjs/float@1.0.2": {"name": "@protobufjs/float", "version": "1.0.2", "integrity": "sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/float/-/float-1.0.2.tgz", "shasum": "5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1", "registryType": "ohpm"}, "@protobufjs/inquire@1.1.0": {"name": "@protobufjs/inquire", "version": "1.1.0", "integrity": "sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/inquire/-/inquire-1.1.0.tgz", "shasum": "ff200e3e7cf2429e2dcafc1140828e8cc638f089", "registryType": "ohpm"}, "@protobufjs/path@1.1.2": {"name": "@protobufjs/path", "version": "1.1.2", "integrity": "sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/path/-/path-1.1.2.tgz", "shasum": "6cc2b20c5c9ad6ad0dccfd21ca7673d8d7fbf68d", "registryType": "ohpm"}, "@protobufjs/pool@1.1.0": {"name": "@protobufjs/pool", "version": "1.1.0", "integrity": "sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/pool/-/pool-1.1.0.tgz", "shasum": "09fd15f2d6d3abfa9b65bc366506d6ad7846ff54", "registryType": "ohpm"}, "@protobufjs/utf8@1.1.0": {"name": "@protobufjs/utf8", "version": "1.1.0", "integrity": "sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==", "resolved": "https://repo.harmonyos.com/ohpm/@protobufjs/utf8/-/utf8-1.1.0.tgz", "shasum": "a777360b5b39a1a2e5106f8e858f2fd2d060c570", "registryType": "ohpm"}, "@types/node@18.15.11": {"name": "@types/node", "version": "18.15.11", "integrity": "sha512-E5Kwq2n4SbMzQOn6wnmBjuK9ouqlURrcZDVfbo9ftDDTFt3nk7ZKK4GMOzoYgnpQJKcxwQw+lGaBvvlMo0qN/Q==", "resolved": "https://repo.harmonyos.com/ohpm/@types/node/-/node-18.15.11.tgz", "shasum": "b3b790f09cb1696cffcec605de025b088fa4225f", "registryType": "ohpm"}, "base64-js@1.5.1": {"name": "base64-js", "version": "1.5.1", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "resolved": "https://repo.harmonyos.com/ohpm/base64-js/-/base64-js-1.5.1.tgz", "shasum": "1b1b440160a5bf7ad40b650f095963481903930a", "registryType": "ohpm"}, "buffer@6.0.3": {"name": "buffer", "version": "6.0.3", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "resolved": "https://repo.harmonyos.com/ohpm/buffer/-/buffer-6.0.3.tgz", "shasum": "2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6", "registryType": "ohpm", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "ieee754@1.2.1": {"name": "ieee754", "version": "1.2.1", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "resolved": "https://repo.harmonyos.com/ohpm/ieee754/-/ieee754-1.2.1.tgz", "shasum": "8eb7a10a63fff25d15a57b001586d177d1b0d352", "registryType": "ohpm"}, "long@5.2.1": {"name": "long", "version": "5.2.1", "integrity": "sha512-GKSNGeNAtw8IryjjkhZxuKB3JzlcLTwjtiQCHKvqQet81I93kXslhDQruGI/QsddO83mcDToBVy7GqGS/zYf/A==", "resolved": "https://repo.harmonyos.com/ohpm/long/-/long-5.2.1.tgz", "shasum": "e27595d0083d103d2fa2c20c7699f8e0c92b897f", "registryType": "ohpm"}, "protobufjs@7.2.4": {"name": "protobufjs", "version": "7.2.4", "integrity": "sha512-AT+RJgD2sH8phPmCf7OUZR8xGdcJRga4+1cOaXJ64hvcSkVhNcRHOwIxUatPH15+nj59WAGTDv3LSGZPEQbJaQ==", "resolved": "https://repo.harmonyos.com/ohpm/protobufjs/-/protobufjs-7.2.4.tgz", "shasum": "3fc1ec0cdc89dd91aef9ba6037ba07408485c3ae", "registryType": "ohpm", "dependencies": {"@protobufjs/utf8": "^1.1.0", "@protobufjs/eventemitter": "^1.1.0", "@types/node": ">=13.7.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/inquire": "^1.1.0", "@protobufjs/codegen": "^2.0.4", "@protobufjs/path": "^1.1.2", "@protobufjs/float": "^1.0.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/base64": "^1.1.2", "long": "^5.0.0", "@protobufjs/aspromise": "^1.1.2"}}, "reflect-metadata@0.2.1": {"name": "reflect-metadata", "version": "0.2.1", "integrity": "sha512-i5lLI6iw9AU3Uu4szRNPPEkomnkjRTaVt9hy/bn5g/oSzekBSMeLZblcjP74AW0vBabqERLLIrz+gR8QYR54Tw==", "resolved": "https://repo.harmonyos.com/ohpm/reflect-metadata/-/reflect-metadata-0.2.1.tgz", "shasum": "8d5513c0f5ef2b4b9c3865287f3c0940c1f67f74", "registryType": "ohpm"}}}