import { abilityAccessCtrl, bundleManager, Permissions, common } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { log } from './Log';

/**
 * 权限管理工具类
 * 封装权限申请和检查逻辑
 */
export class PermissionManager {
  private static instance: PermissionManager;
  private atManager: abilityAccessCtrl.AtManager;

  private constructor() {
    this.atManager = abilityAccessCtrl.createAtManager();
  }

  public static getInstance(): PermissionManager {
    if (!PermissionManager.instance) {
      PermissionManager.instance = new PermissionManager();
    }
    return PermissionManager.instance;
  }

  /**
   * 检查单个权限状态
   * @param permission 权限名称
   * @returns Promise<boolean> 是否已授权
   */
  async checkPermission(permission: Permissions): Promise<boolean> {
    try {
      const bundleInfo = await bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      const tokenId = bundleInfo.appInfo.accessTokenId;
      const grantStatus = await this.atManager.checkAccessToken(tokenId, permission);
      return grantStatus === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED;
    } catch (error) {
      log(`检查权限失败: ${error}`);
      return false;
    }
  }

  /**
   * 检查多个权限状态
   * @param permissions 权限数组
   * @returns Promise<Map<string, boolean>> 权限状态映射
   */
  async checkPermissions(permissions: Permissions[]): Promise<Map<string, boolean>> {
    const result = new Map<string, boolean>();
    
    for (const permission of permissions) {
      const granted = await this.checkPermission(permission);
      result.set(permission, granted);
    }
    
    return result;
  }

  /**
   * 申请单个权限
   * @param context UIAbilityContext
   * @param permission 权限名称
   * @returns Promise<boolean> 是否授权成功
   */
  async requestPermission(context: common.UIAbilityContext, permission: Permissions): Promise<boolean> {
    try {
      // 先检查是否已经有权限
      const hasPermission = await this.checkPermission(permission);
      if (hasPermission) {
        log(`权限 ${permission} 已授权`);
        return true;
      }

      // 申请权限
      const result = await this.atManager.requestPermissionsFromUser(context, [permission]);
      const granted = result.authResults[0] === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED;
      
      if (granted) {
        log(`权限 ${permission} 申请成功`);
      } else {
        log(`权限 ${permission} 申请失败`);
      }
      
      return granted;
    } catch (error) {
      log(`申请权限失败: ${error}`);
      return false;
    }
  }

  /**
   * 申请多个权限
   * @param context UIAbilityContext
   * @param permissions 权限数组
   * @returns Promise<Map<string, boolean>> 权限申请结果映射
   */
  async requestPermissions(context: common.UIAbilityContext, permissions: Permissions[]): Promise<Map<string, boolean>> {
    const result = new Map<string, boolean>();
    
    try {
      // 先检查哪些权限还没有授权
      const needRequestPermissions: Permissions[] = [];
      for (const permission of permissions) {
        const hasPermission = await this.checkPermission(permission);
        if (hasPermission) {
          result.set(permission, true);
          log(`权限 ${permission} 已授权`);
        } else {
          needRequestPermissions.push(permission);
        }
      }

      // 如果有需要申请的权限，批量申请
      if (needRequestPermissions.length > 0) {
        const requestResult = await this.atManager.requestPermissionsFromUser(context, needRequestPermissions);
        
        for (let i = 0; i < needRequestPermissions.length; i++) {
          const permission = needRequestPermissions[i];
          const granted = requestResult.authResults[i] === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED;
          result.set(permission, granted);
          
          if (granted) {
            log(`权限 ${permission} 申请成功`);
          } else {
            log(`权限 ${permission} 申请失败`);
          }
        }
      }
    } catch (error) {
      log(`批量申请权限失败: ${error}`);
      // 如果批量申请失败，将所有权限标记为未授权
      permissions.forEach(permission => {
        if (!result.has(permission)) {
          result.set(permission, false);
        }
      });
    }
    
    return result;
  }

  /**
   * 相机权限相关方法
   */
  async checkCameraPermission(): Promise<boolean> {
    return await this.checkPermission('ohos.permission.CAMERA');
  }

  async requestCameraPermission(context: common.UIAbilityContext): Promise<boolean> {
    return await this.requestPermission(context, 'ohos.permission.CAMERA');
  }

  /**
   * 媒体权限相关方法
   */
  async checkMediaPermissions(): Promise<Map<string, boolean>> {
    const permissions: Permissions[] = [
      'ohos.permission.READ_MEDIA',
      'ohos.permission.WRITE_MEDIA',
      'ohos.permission.READ_IMAGEVIDEO',
      'ohos.permission.WRITE_IMAGEVIDEO'
    ];
    return await this.checkPermissions(permissions);
  }

  async requestMediaPermissions(context: common.UIAbilityContext): Promise<Map<string, boolean>> {
    const permissions: Permissions[] = [
      'ohos.permission.READ_MEDIA',
      'ohos.permission.WRITE_MEDIA',
      'ohos.permission.READ_IMAGEVIDEO',
      'ohos.permission.WRITE_IMAGEVIDEO'
    ];
    return await this.requestPermissions(context, permissions);
  }

  /**
   * 麦克风权限相关方法
   */
  async checkMicrophonePermission(): Promise<boolean> {
    return await this.checkPermission('ohos.permission.MICROPHONE');
  }

  async requestMicrophonePermission(context: common.UIAbilityContext): Promise<boolean> {
    return await this.requestPermission(context, 'ohos.permission.MICROPHONE');
  }

  /**
   * 位置权限相关方法
   */
  async checkLocationPermission(): Promise<boolean> {
    return await this.checkPermission('ohos.permission.LOCATION');
  }

  async requestLocationPermission(context: common.UIAbilityContext): Promise<boolean> {
    return await this.requestPermission(context, 'ohos.permission.LOCATION');
  }

  /**
   * 获取权限状态描述
   * @param permission 权限名称
   * @param granted 是否已授权
   * @returns 权限状态描述
   */
  getPermissionStatusText(permission: string, granted: boolean): string {
    const permissionNames: Record<string, string> = {
      'ohos.permission.CAMERA': '相机',
      'ohos.permission.READ_MEDIA': '读取媒体文件',
      'ohos.permission.WRITE_MEDIA': '写入媒体文件',
      'ohos.permission.READ_IMAGEVIDEO': '读取图片视频',
      'ohos.permission.WRITE_IMAGEVIDEO': '写入图片视频',
      'ohos.permission.MICROPHONE': '麦克风',
      'ohos.permission.LOCATION': '位置信息',
      'ohos.permission.MEDIA_LOCATION': '媒体位置信息'
    };

    const name = permissionNames[permission] || permission;
    return `${name}: ${granted ? '已授权' : '未授权'}`;
  }
}

/**
 * 导出单例实例
 */
export const permissionManager = PermissionManager.getInstance();
