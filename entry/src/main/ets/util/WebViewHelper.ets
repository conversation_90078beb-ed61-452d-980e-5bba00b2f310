


export class WebViewParam {
  url: string = '';
  title: string = '';
  isWebNavigation: number = 0

  constructor(url: string, title: string, isWebNavigation: number) {
    this.url = url;
    this.title = title;
    this.isWebNavigation = isWebNavigation;
  }

  public getParamUrl() : string{
    return `${this.url}?platform=1`
  }


  static fromJson(json: Record<string, Object | string | number>): WebViewParam {
    const url = typeof json.url === 'string' ? json.url : '';
    const title = typeof json.title === 'string' ? json.title : '';
    const isWebNavigation = typeof json.isWebNavigation === 'number' ? json.isWebNavigation : 0;
    return new WebViewParam(url, title, isWebNavigation);
  }

}