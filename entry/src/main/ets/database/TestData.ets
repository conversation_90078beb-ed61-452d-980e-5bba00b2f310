import { SessionEntity } from "../entities/SessionEntity";
import { WorkItemEntity } from "../entities/WorkItemEntity";


export const defaultSessions = [
  new SessionEntity(undefined, '杜啸', '/resources/base/media/ic_kingdee.png', '这种因景致美极而造成的深切撼动，我只...', '3月1日', 9, undefined, undefined, 0, 1, 0),
  new SessionEntity(undefined, '夏诗梦', '/resources/base/media/ic_kingdee.png', '上一年就买过，依旧好吃，蛋黄流油，莲蓉入...', '3月1日', 1, undefined, undefined, 0, 1, 0),
  new SessionEntity(undefined, '丁逸芯', '/resources/base/media/ic_kingdee.png', '推理经典系列书籍终于收齐了，希望以后出...', '3月1日', 0, undefined, undefined, 0, 1, 0),
  new SessionEntity(undefined, '崔诗禹', '/resources/base/media/ic_kingdee.png', '颜值够高，配件挺多，真心话，对个人家用...', '3月1日', 0, undefined, undefined, 0, 1, 0),
  new SessionEntity(undefined, '数据中心综合群', '/resources/base/media/ic_kingdee.png', '碳排放强度不断下降，绿色交通发展底色更...', '3月1日', 0, '全员', '#4A90E2', 0, 2, 0),
  new SessionEntity(undefined, '培训课程工作群', '/resources/base/media/ic_kingdee.png', '留下你的汗水，展现你的风采，演绎你的精...', '3月1日', 0, undefined, undefined, 0, 2, 0),
  new SessionEntity(undefined, '审批', '/resources/base/media/ic_kingdee.png', '夏梦诗提交的申请待审批', '', 10, '快速审批', '#FF9800', 0, 1, 1),
];


export  const defaultWorkItems = [
  new WorkItemEntity('1', '审批', '/resources/base/media/ic_kingdee.png', '待办审批事项', 5),
  new WorkItemEntity('2', '考勤', '/resources/base/media/ic_kingdee.png', '考勤打卡', 0),
  new WorkItemEntity('3', '日程', '/resources/base/media/ic_kingdee.png', '日程安排', 2),
  new WorkItemEntity('4', '文档', '/resources/base/media/ic_kingdee.png', '文档管理', 0),
  new WorkItemEntity('5', '报表', '/resources/base/media/ic_kingdee.png', '数据报表', 1),
  new WorkItemEntity('6', '通知公告', '/resources/base/media/ic_kingdee.png', '企业通知公告', 3),
];