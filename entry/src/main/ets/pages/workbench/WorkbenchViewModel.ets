import { WorkItem } from "../../data_source/SessionBody";
import { toolDataService } from '../../services/ToolDataService';
import { ToolGridItem, WorkBenchUIData } from '../../models/ToolModels';
import { NetworkError } from '../../network/NetworkConfig';
import { routeTo } from "../../util/RouteHelper";
import { WebViewParam } from "../../util/WebViewHelper";

@ObservedV2
export class WorkbenchViewModel {

  @Trace toolsData: WorkBenchUIData[] = [];
  @Trace managementData: WorkBenchUIData[] = [];
  @Trace isLoading: boolean = true;
  @Trace errorMessage: string = '';

  // 公司ID（实际项目中应该从用户信息获取）
  private readonly companyId: string = '8888';

  // 回调函数
  private _onDataChanged?: (data: WorkItem[]) => void;
  private _onToolsChanged?: (tools: WorkBenchUIData[], management: WorkBenchUIData[]) => void;


  getToolsData(): WorkBenchUIData[] {
    return this.toolsData;
  }

  getManagementData(): WorkBenchUIData[] {
    return this.managementData;
  }

  getIsLoading(): boolean {
    return this.isLoading;
  }

  getErrorMessage(): string {
    return this.errorMessage;
  }

  // 设置回调函数
  setOnDataChanged(callback: (data: WorkItem[]) => void): void {
    this._onDataChanged = callback;
  }

  setOnToolsChanged(callback: (tools: WorkBenchUIData[], management: WorkBenchUIData[]) => void): void {
    this._onToolsChanged = callback;
  }

  async fetchWorkbenchList(): Promise<void> {
    this.isLoading = true;
    this.errorMessage = '';

    try {
      // 获取分离的工具和管理数据
      const result = await toolDataService.getToolsAndManagement(this.companyId, false);

      this.toolsData = result.tools;
      this.managementData = result.management;

      // 添加本地工具
      this.addLocalTools();

      console.info('WorkbenchViewModel: 数据加载成功');
      console.info(`- 工具数量: ${this.toolsData.length}`);
      console.info(`- 管理数量: ${this.managementData.length}`);

      // 通知UI更新
      this._onToolsChanged?.(this.toolsData, this.managementData);

    } catch (error) {
      console.error('WorkbenchViewModel: 数据加载失败', error);

      let errorMsg = '加载数据失败';
      if (error instanceof NetworkError) {
        switch (error.type) {
          case 'TIMEOUT':
            errorMsg = '请求超时，请检查网络连接';
            break;
          case 'NETWORK_ERROR':
            errorMsg = '网络连接失败，请检查网络设置';
            break;
          case 'SERVER_ERROR':
            errorMsg = `服务器错误: ${error.message}`;
            break;
          default:
            errorMsg = error.message || '未知网络错误';
        }
      }

      this.errorMessage = errorMsg;
      this.toolsData = [];
      // 即使网络失败，也添加本地工具
      this.addLocalTools();
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 刷新数据
   */
  async refreshData(): Promise<void> {
    console.info('WorkbenchViewModel: 刷新数据');
    this.isLoading = true;
    this.errorMessage = '';

    try {
      // 强制刷新分离的工具数据
      const result = await toolDataService.refreshToolsAndManagement(this.companyId);
      this.toolsData = result.tools;
      this.managementData = result.management;

      // 添加本地工具
      this.addLocalTools();

      console.info('WorkbenchViewModel: 数据刷新成功');
      console.info(`- 工具数量: ${this.toolsData.length}`);
      console.info(`- 管理数量: ${this.managementData.length}`);

      // 通知UI更新
      this._onToolsChanged?.(this.toolsData, this.managementData);

    } catch (error) {
      console.error('WorkbenchViewModel: 数据刷新失败', error);
      this.errorMessage = '刷新失败，请重试';
      // 即使刷新失败，也添加本地工具
      this.addLocalTools();
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 点击工具项
   */
  onToolClick(context: UIContext ,tool: WorkBenchUIData): void {
    console.info(`WorkbenchViewModel: 点击工具 ${tool.name}, URL: ${tool.webUrl}`);

    // 如果有徽章，点击后清除角标
    if (tool.hasRedDot()) {
      this.updateToolBadge(tool.key, 0);
    }

    // 检查是否是本地页面
    if (tool.key === 'permission_demo') {
      // 跳转到权限示例页面
      routeTo(context, 'pages/permission/PermissionDemoPage');
    } else if (tool.key === 'webview_test') {
      // 跳转到WebView测试页面
      routeTo(context, 'pages/webview/WebViewPage', new WebViewParam(tool.webUrl, tool.name, 1));
    } else if (tool.key === 'webview_test_page') {
      // 跳转到专用的WebView测试页面
      routeTo(context, 'pages/webview/WebViewTestPage');
    } else {
      // 跳转到WebView页面
      routeTo(context , 'pages/webview/WebViewPage' , new WebViewParam(tool.webUrl,
        tool.name, 0))
    }
  }

  /**
   * 更新未读数
   */
  async updateToolBadge(toolKey: string, badge: number): Promise<void> {
    try {
      const success = await toolDataService.updateToolBadge(toolKey, this.companyId, badge);

      if (success) {
        // 更新本地数据 - 工具数据
        const updatedTools: WorkBenchUIData[] = [];
        for (const tool of this.toolsData) {
          if (tool.key === toolKey) {
            const updatedTool = new WorkBenchUIData({
              id: tool.id,
              name: tool.name,
              icon: tool.icon,
              darkModeIcon: tool.darkModeIcon,
              badge: badge > 0 ? badge : undefined,
              webUrl: tool.webUrl,
              type: tool.type,
              key: tool.key,
              argument: tool.argument
            });
            updatedTools.push(updatedTool);
          } else {
            updatedTools.push(tool);
          }
        }
        this.toolsData = updatedTools;

        // 更新本地数据 - 管理数据
        const updatedManagement: WorkBenchUIData[] = [];
        for (const tool of this.managementData) {
          if (tool.key === toolKey) {
            const updatedTool = new WorkBenchUIData({
              id: tool.id,
              name: tool.name,
              icon: tool.icon,
              darkModeIcon: tool.darkModeIcon,
              badge: badge > 0 ? badge : undefined,
              webUrl: tool.webUrl,
              type: tool.type,
              key: tool.key,
              argument: tool.argument
            });
            updatedManagement.push(updatedTool);
          } else {
            updatedManagement.push(tool);
          }
        }
        this.managementData = updatedManagement;

        this._onToolsChanged?.(this.toolsData, this.managementData);
      }
    } catch (error) {
      console.error('WorkbenchViewModel: 更新徽章失败', error);
    }
  }

  /**
   * 添加本地工具
   */
  private addLocalTools(): void {
    // 权限示例工具
    const permissionDemoTool = new WorkBenchUIData({
      id: 'local_permission_demo',
      name: '权限示例',
      icon: '/resources/base/media/im_newsHome_search.png',
      darkModeIcon: '/resources/base/media/im_newsHome_search.png',
      badge: undefined,
      webUrl: '', // 本地页面不需要webUrl
      type: 'tool',
      key: 'permission_demo',
      argument: {
        companyId: this.companyId,
        orgId: '',
        name: '权限示例',
        index: 999,
        isManager: false
      }
    });

    // WebView测试工具
    const webViewTestTool = new WorkBenchUIData({
      id: 'local_webview_test',
      name: 'WebView测试',
      icon: '/resources/base/media/im_newsHome_search.png',
      darkModeIcon: '/resources/base/media/im_newsHome_search.png',
      badge: undefined,
      webUrl: 'resource://rawfile/test_webview.html', // 测试页面URL
      type: 'tool',
      key: 'webview_test',
      argument: {
        companyId: this.companyId,
        orgId: '',
        name: 'WebView测试',
        index: 998,
        isManager: false
      }
    });

    // WebView专用测试页面工具
    const webViewTestPageTool = new WorkBenchUIData({
      id: 'local_webview_test_page',
      name: 'JS调用测试',
      icon: '/resources/base/media/im_newsHome_search.png',
      darkModeIcon: '/resources/base/media/im_newsHome_search.png',
      badge: undefined,
      webUrl: '', // 本地页面不需要webUrl
      type: 'tool',
      key: 'webview_test_page',
      argument: {
        companyId: this.companyId,
        orgId: '',
        name: 'JS调用测试',
        index: 997,
        isManager: false
      }
    });

    // 添加到工具列表
    this.toolsData.push(permissionDemoTool);
    // this.toolsData.push(webViewTestTool);
    // this.toolsData.push(webViewTestPageTool);

    console.info('WorkbenchViewModel: 已添加本地工具');
  }

  // 清理资源
  dispose(): void {
    this._onDataChanged = undefined;
    this._onToolsChanged = undefined;
  }
}