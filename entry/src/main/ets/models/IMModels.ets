

// 获取 SocketLink
export class  SocketLinkResp {

  url?: string

  constructor(url: string) {
    this.url = url;
  }

}


// 获取imToken response
export class IMTokenResp {

  appId?: string;
  appUid?: string;
  avatar?: string;
  id?: string;
  mobile?: string;
  nickname?: string;
  token?: string;

  constructor(appId: string, appUid: string, avatar: string, id: string, mobile: string, nickname: string,
    token: string) {
    this.appId = appId;
    this.appUid = appUid;
    this.avatar = avatar;
    this.id = id;
    this.mobile = mobile;
    this.nickname = nickname;
    this.token = token;
  }
}

