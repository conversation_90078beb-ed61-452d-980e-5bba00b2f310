import { Entity, Columns, Id, NotNull, Index, ColumnType, Unique } from '@ohos/dataorm';
import { ToolGridItem, ToolArgument } from '../models/ToolModels';

/**
 * 工具缓存实体类
 */
@Entity('TOOL_CACHE')
export class ToolCacheEntity {

  @NotNull()
  @Index()
  @Columns({ columnName: 'TOOL_ID', types: ColumnType.str })
  toolId: string;

  @NotNull()
  @Columns({ columnName: 'NAME', types: ColumnType.str })
  name: string;

  @NotNull()
  @Columns({ columnName: 'ICON', types: ColumnType.str })
  icon: string;

  @Columns({ columnName: 'DARK_MODE_ICON', types: ColumnType.str })
  darkModeIcon?: string;

  @Columns({ columnName: 'BADGE', types: ColumnType.num })
  badge?: number;

  @NotNull()
  @Columns({ columnName: 'WEB_URL', types: ColumnType.str })
  webUrl: string;

  @NotNull()
  @Columns({ columnName: 'TYPE', types: ColumnType.str })
  type: string; // 'tool' | 'management'

  @Id({ isPrimaryKey: true, autoincrement: false })
  @Columns({ columnName: 'KEY', types: ColumnType.str })
  @NotNull()
  @Unique()
  key: string;

  @Columns({ columnName: 'ARGUMENT_JSON', types: ColumnType.str })
  argumentJson?: string;

  @NotNull()
  @Index()
  @Columns({ columnName: 'COMPANY_ID', types: ColumnType.str })
  companyId: string;

  @Columns({ columnName: 'CREATE_TIME', types: ColumnType.num })
  createTime: number;

  @Columns({ columnName: 'UPDATE_TIME', types: ColumnType.num })
  updateTime: number;

  constructor(
    key: string,
    toolId?: string,
    name?: string,
    icon?: string,
    darkModeIcon?: string,
    badge?: number,
    webUrl?: string,
    type?: string,
    argumentJson?: string,
    companyId?: string,
    createTime?: number,
    updateTime?: number
  ) {
    this.key = key;
    this.toolId = toolId || '';
    this.name = name || '';
    this.icon = icon || '';
    this.darkModeIcon = darkModeIcon;
    this.badge = badge;
    this.webUrl = webUrl || '';
    this.type = type || 'tool';
    this.argumentJson = argumentJson;
    this.companyId = companyId || '';
    this.createTime = createTime || Math.floor(Date.now() / 1000);
    this.updateTime = updateTime || Math.floor(Date.now() / 1000);
  }

  // Getter和Setter方法

  getToolId(): string {
    return this.toolId;
  }

  setToolId(toolId: string): void {
    this.toolId = toolId;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getName(): string {
    return this.name;
  }

  setName(name: string): void {
    this.name = name;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getIcon(): string {
    return this.icon;
  }

  setIcon(icon: string): void {
    this.icon = icon;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getDarkModeIcon(): string | undefined {
    return this.darkModeIcon;
  }

  setDarkModeIcon(darkModeIcon: string | undefined): void {
    this.darkModeIcon = darkModeIcon;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getBadge(): number | undefined {
    return this.badge;
  }

  setBadge(badge: number | undefined): void {
    this.badge = badge;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getWebUrl(): string {
    return this.webUrl;
  }

  setWebUrl(webUrl: string): void {
    this.webUrl = webUrl;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getType(): string {
    return this.type;
  }

  setType(type: string): void {
    this.type = type;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getKey(): string {
    return this.key;
  }

  setKey(key: string): void {
    this.key = key;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getArgumentJson(): string | undefined {
    return this.argumentJson;
  }

  setArgumentJson(argumentJson: string | undefined): void {
    this.argumentJson = argumentJson;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getCompanyId(): string {
    return this.companyId;
  }

  setCompanyId(companyId: string): void {
    this.companyId = companyId;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getCreateTime(): number {
    return this.createTime;
  }

  setCreateTime(createTime: number): void {
    this.createTime = createTime;
  }

  getUpdateTime(): number {
    return this.updateTime;
  }

  setUpdateTime(updateTime: number): void {
    this.updateTime = updateTime;
  }

  /**
   * 转换为ToolGridItem
   */
  toToolGridItem(): ToolGridItem {
    let argument: ToolArgument | undefined;
    if (this.argumentJson) {
      try {
        argument = JSON.parse(this.argumentJson) as ToolArgument;
      } catch (error) {
        console.error('ToolCacheEntity: 解析argument失败', error);
      }
    }

    return {
      id: this.toolId,
      name: this.name,
      icon: this.icon,
      darkModeIcon: this.darkModeIcon,
      badge: this.badge,
      webUrl: this.webUrl,
      type: this.type as 'tool' | 'management',
      key: this.key,
      argument
    };
  }

  /**
   * 从ToolGridItem创建实体
   */
  static fromToolGridItem(gridItem: ToolGridItem, companyId: string): ToolCacheEntity {
    const argumentJson = gridItem.argument ? JSON.stringify(gridItem.argument) : undefined;

    return new ToolCacheEntity(
      gridItem.key,
      gridItem.id,
      gridItem.name,
      gridItem.icon,
      gridItem.darkModeIcon,
      gridItem.badge,
      gridItem.webUrl,
      gridItem.type,
      argumentJson,
      companyId,
      Math.floor(Date.now() / 1000),
      Math.floor(Date.now() / 1000)
    );
  }
}
