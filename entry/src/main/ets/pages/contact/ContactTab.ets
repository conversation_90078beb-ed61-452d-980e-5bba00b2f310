import { ContactViewModel } from './ContactViewModel';

@Component
export struct ContactTab {
  private viewModel: ContactViewModel = new ContactViewModel();

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Text('通讯录')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
        
        Blank()
        
        // 搜索图标
        Image($r('app.media.ic_public_search'))
          .width(24)
          .height(24)
          .margin({ right: 16 })
          
        // 添加联系人图标
        Image($r('app.media.ic_public_add'))
          .width(24)
          .height(24)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 12 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor('#FFFFFF')

      // 主要内容区域
      Scroll() {
        Column() {
          // 新的申请
          ContactItem({
            title: '新的申请',
            icon: $r('app.media.ic_public_person_add'),
            badge: '1',
            onItemClick: () => this.viewModel.onItemClick('新的申请')
          })
          
          // 担当好友
          ContactItem({
            title: '担当好友',
            icon: $r('app.media.ic_public_person'),
            onItemClick: () => this.viewModel.onItemClick('担当好友')
          })
          
          // 我的群组
          ContactItem({
            title: '我的群组',
            icon: $r('app.media.ic_public_group'),
            onItemClick: () => this.viewModel.onItemClick('我的群组')
          })
          
          // 分割线
          Divider()
            .strokeWidth(0.5)
            .color('#F0F0F0')
            .margin({ left: 16 })
          
          // 公司信息卡片
          Column() {
            // 公司名称行
            Row() {
              Image($r('app.media.ic_public_company'))
                .width(24)
                .height(24)
                .margin({ right: 12 })
                
              Text('加优科技有限公司')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .layoutWeight(1)
                
              // 邀请按钮
              Button('邀请', { type: ButtonType.Normal, stateEffect: true })
                .fontSize(12)
                .fontColor('#1976D2')
                .backgroundColor('#E6F0FF')
                .borderRadius(4)
                .width(56)
                .height(24)
                .margin({ right: 8 })
                .onClick(() => this.viewModel.onInviteClick())
                
              Image($r('app.media.ic_public_arrow_right'))
                .width(16)
                .height(16)
                .opacity(0.5)
            }
            .width('100%')
            .height(56)
            .padding({ left: 16, right: 12 })
            .alignItems(VerticalAlign.Center)
            .onClick(() => this.viewModel.onCompanyClick())
            
            // 组织架构
            ContactItem({
              title: '组织架构',
              icon: $r('app.media.ic_public_organization'),
              indent: true,
              onItemClick: () => this.viewModel.onItemClick('组织架构')
            })
            
            // 外部联系人
            ContactItem({
              title: '外部联系人',
              icon: $r('app.media.ic_public_contacts'),
              indent: true,
              onItemClick: () => this.viewModel.onItemClick('外部联系人')
            })
          }
          .width('100%')
          .backgroundColor('#FFFFFF')
          .borderRadius(12)
          .margin({ top: 8, left: 12, right: 12 })
          .padding({ top: 4, bottom: 4 })
          
          // 我的其他企业
          ContactItem({
            title: '我的其他企业',
            icon: $r('app.media.ic_public_contacts'),
            onItemClick: () => this.viewModel.onItemClick('我的其他企业')
          })
        }
        .width('100%')
      }
      .width('100%')
      .scrollBar(BarState.Off)
      .scrollable(ScrollDirection.Vertical)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}

@Component
struct ContactItem {
  private title: string = '';
  private icon: Resource | undefined;
  private badge?: string;
  private indent: boolean = false;
  private onItemClick: () => void = () => {};

  build() {
    Row() {
      // 图标
      if (this.icon) {
        Image(this.icon)
          .width(24)
          .height(24)
          .margin({ right: 12 })
      } else {
        // 如果没有图标资源，显示一个占位符
        Text('')
          .width(24)
          .height(24)
          .margin({ right: 12 })
      }
      
      // 标题
      Text(this.title)
        .fontSize(16)
        .fontWeight(FontWeight.Normal)
        .layoutWeight(1)
      
      // 角标
      if (this.badge) {
        Text(this.badge)
          .fontSize(12)
          .fontColor('#FFFFFF')
          .backgroundColor('#FF3B30')
          .borderRadius(8)
          .padding({ left: 6, right: 6, top: 1, bottom: 1 })
          .margin({ right: 8 })
      }
      
      // 右侧箭头
      Image($r('app.media.ic_public_arrow_right'))
        .width(16)
        .height(16)
        .opacity(0.5)
    }
    .width('100%')
    .height(56)
    .padding({ left: this.indent ? 48 : 28, right: 16 })
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .margin({ top: 8, left: 12, right: 12 })
    .onClick(() => this.onItemClick())
  }
}
