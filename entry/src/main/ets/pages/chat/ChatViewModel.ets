import { Session } from "../../data_source/SessionBody";
import { imApiService } from "../../services/IMApiService";
import { log } from "../../util/Log";
import { MessageEntity } from './MessageEntity';
// import { IMWebSocket } from "../../im/IMWebSocket";
import { DeviceUtils } from "../../util/DeviceUtil";
import { print } from "@kit.BasicServicesKit";
import IMWebSocket from "../../im/IMWebSocket";
import WebSocketManager from "../../im/IMWebSocket";
// import { Scroller } from '@kit.ArkUI';

// import {  } from 'proto.d.ts';


@ObservedV2
export class ChatViewModel {
  @Trace session?: Session
  @Trace messages: MessageEntity[] = [
    new MessageEntity('other', '方法'),
    new MessageEntity('me', '女和'),
    new MessageEntity('other', '热拌粉'),
    new MessageEntity('other', '好好过'),
    new MessageEntity('me', '吃'),
  ];
  @Trace input: string = '';

  // 列表滚动控制器
  listScroller: Scroller = new Scroller();

  init() {
    DeviceUtils.getDeviceAAID().then((aaid) => {
        console.log(`aaid = ${aaid}`)
    })

    let uuid = DeviceUtils.getUUID();
    console.log(`uuid = ${uuid}`)

    this.onMessage()
  }

  private handleNewMessage = (newMessage: MessageEntity) => {
    console.log(`页面收到了新消息: ${newMessage.content}`);
    // 更新UI：将新消息添加到列表的末尾

  };

  onMessage() {
    WebSocketManager.getInstance().on("message" , this.handleNewMessage)
  }

  async fetchIMConfig() {
     // await this.fetchIMToken();
     this.fetchSocketUrl();
  }

  private async fetchSocketUrl() {
    let r = await imApiService.getSocketUrl()
    log(`获取到了SocketUrl = ${r.url}`)
    let socketUrl = r.url || '';
    // WebSocket 连接管理不应该在 ViewModel 中进行，应该在应用启动或用户登录后统一处理
    WebSocketManager.getInstance().connect(socketUrl)
  }

  async fetchIMToken() {
    let r = await imApiService.getImToken()
  }

  sendMsg() {
    if (!this.input.trim()) return;

    const messageContent = this.input.trim();
    this.input = '';

    // 添加消息到列表
    this.messages.push(new MessageEntity('me', messageContent));

    // 滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);

    // TODO: 发送到服务器
    this.sendToServer(messageContent);
  }

  // 滚动到底部
  scrollToBottom() {
    try {
      this.listScroller.scrollToIndex(this.messages.length - 1, true);
    } catch (error) {
      log(`滚动到底部失败: ${error}`);
    }
  }

  // 发送消息到服务器
  private sendToServer(content: string) {
    // TODO: 实现发送到WebSocket服务器的逻辑
    log(`发送消息到服务器: ${content}`);
  }

  // 接收新消息
  receiveMessage(content: string, sender: 'me' | 'other' = 'other') {
    this.messages.push(new MessageEntity(sender, content));
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }


  sendLoginSocket() {

  }


}