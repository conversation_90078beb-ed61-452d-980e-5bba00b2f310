import util from '@ohos.util';
import fs from '@ohos.file.fs';
import image from '@ohos.multimedia.image';


export class BufferUtils {
  // 任意 Protobuf 对象 → ArrayBuffer
  static objectToArrayBuffer(obj: object): ArrayBuffer {
    const jsonStr: string = JSON.stringify(obj);
    console.info(`objectToArrayBuffer ==> ${obj}`)
    const textBytes: Uint8Array = new util.TextEncoder().encode(jsonStr);

    const base64Helper = new util.Base64Helper();
    const base64Str: string = base64Helper.encodeToStringSync(textBytes);

    const finalBytes: Uint8Array = new util.TextEncoder().encode(base64Str);
    return finalBytes.buffer;
  }

  // ArrayBuffer → 任意对象（由调用方反序列化）
  static arrayBufferToObject<T>(buffer: ArrayBuffer): T {
    const base64Bytes = new Uint8Array(buffer);
    const base64Str = new util.TextDecoder().decode(base64Bytes);

    const base64Helper = new util.Base64Helper();
    const decodedBytes: Uint8Array = base64Helper.decodeSync(base64Str);

    const jsonStr = new util.TextDecoder().decode(decodedBytes);
    return JSON.parse(jsonStr) as T;
  }

  static stringToArrayBuffer(str: string): ArrayBuffer {
    const encoder = new util.TextEncoder();
    // encode() 方法返回一个 Uint8Array
    const encodedUint8Array = encoder.encode(str);

    // Uint8Array 的 buffer 属性就是其底层的 ArrayBuffer
    return encodedUint8Array.buffer;
  }

  async  readImageAsUint8Array(path: string): Promise<Uint8Array> {
    const file = fs.openSync(path, fs.OpenMode.READ_ONLY);
    const stat = fs.statSync(path);
    const buffer = new ArrayBuffer(stat.size);
    fs.readSync(file.fd, buffer);
    fs.closeSync(file);
    return new Uint8Array(buffer);
  }

  static async imagePathToBase64(path: string): Promise<string> {
    const file = fs.openSync(path, fs.OpenMode.READ_ONLY);
    const stat = fs.statSync(path);
    const buffer = new ArrayBuffer(stat.size);
    fs.readSync(file.fd, buffer);
    fs.closeSync(file);

    const uint8Data = new Uint8Array(buffer);
    const base64Helper = new util.Base64Helper();  // ArkTS 中需要实例化
    const base64 = base64Helper.encodeToString(uint8Data);
    return base64;
  }


  /**
   * 将多个路径并行转换为 base64
   */
 static async convertMultiplePathsToBase64(paths: string[]): Promise<string[]> {
    const tasks = paths.map(path => BufferUtils.imageFileToBase64(path)); // 每个 path 都是一个 promise
    const base64List = await Promise.all(tasks); // 并行等待所有任务完成
    return base64List;
  }



  static imageFileToBase64(filePath:string) : string{
    let file = fs.openSync(filePath, fs.OpenMode.READ_ONLY);
    let stats = fs.statSync(file.fd)
    let buffer = new ArrayBuffer(stats.size)
    let readLen = fs.readSync(file.fd, buffer);
    console.info('readSync data to file succeed and buffer size is:' + readLen);
    fs.closeSync(file);
    let that = new util.Base64Helper();
    let array = new Uint8Array(buffer);
    const type = 'jpg';
    let base64 = `data:image/${type};base64,` + that.encodeToStringSync(array,util.Type.BASIC);
    return base64
  }

}
