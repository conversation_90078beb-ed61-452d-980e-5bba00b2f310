# HarmonyOS WebView JavaScript 调用集成指南

## 问题分析

您遇到的问题是 HarmonyOS WebView 中 JavaScript 无法调用原生方法 `client_isAlready`，而同样的代码在 Android/iOS 上工作正常。这是由于 HarmonyOS WebView 的 JavaScript 桥接机制与其他平台存在差异导致的。

## 主要问题

1. **对象注入时机**：HarmonyOS 需要确保页面完全加载后 JavaScript 对象才能正确注入
2. **方法调用时机**：页面的 JavaScript 可能在原生对象注入之前就尝试调用方法
3. **生命周期管理**：缺少页面准备就绪的通知机制

## 解决方案

### 1. 优化 WebView 页面 (WebViewPage.ets)

#### 1.1 添加页面加载完成处理
```typescript
.onPageEnd((e) => {
  log(`WebView页面加载完成: ${e?.url}`);
  // 页面加载完成后，确保JavaScript对象已注入
  this.checkAndInjectJavaScript();
})
```

#### 1.2 检查和注入 JavaScript 对象
```typescript
private checkAndInjectJavaScript() {
  try {
    // 延迟一小段时间确保页面完全加载
    setTimeout(() => {
      // 检查JavaScript对象是否存在
      const checkScript = `
        (function() {
          if (typeof window.${BASE_WIN_OBJ} !== 'undefined') {
            console.log('${BASE_WIN_OBJ} 对象已存在');
            return true;
          } else {
            console.log('${BASE_WIN_OBJ} 对象不存在');
            return false;
          }
        })();
      `;
      
      this.controller.runJavaScript(checkScript)
        .then((result) => {
          log(`JavaScript对象检查结果: ${result}`);
          this.triggerPageReady();
        })
        .catch((error) => {
          log(`JavaScript对象检查失败: ${error}`);
          this.triggerPageReady();
        });
    }, 500);
  } catch (error) {
    log(`注入JavaScript对象失败: ${error}`);
  }
}
```

#### 1.3 触发页面准备就绪事件
```typescript
private triggerPageReady() {
  try {
    const readyScript = `
      (function() {
        // 触发自定义事件通知页面原生对象已准备就绪
        if (typeof window.dispatchEvent === 'function') {
          var event = new CustomEvent('nativeReady', {
            detail: { platform: 'harmonyos', object: '${BASE_WIN_OBJ}' }
          });
          window.dispatchEvent(event);
          console.log('已触发 nativeReady 事件');
        }
        
        // 如果页面有特定的初始化方法，也可以直接调用
        if (typeof window.onNativeReady === 'function') {
          window.onNativeReady('${BASE_WIN_OBJ}');
          console.log('已调用 onNativeReady 方法');
        }
        
        return true;
      })();
    `;
    
    this.controller.runJavaScript(readyScript);
  } catch (error) {
    log(`触发页面准备就绪事件失败: ${error}`);
  }
}
```

### 2. 优化 JavaScript 代理类 (WebJS.ets)

#### 2.1 增强 client_isAlready 方法
```typescript
public client_isAlready(message: string) {
  log(`=== client_isAlready 方法被调用 ===`);
  log(`接收到的消息: ${message}`);
  
  try {
    let o = JSON.parse(message) as ClientIsAlreadyBody;
    log(`解析后的数据: ${JSON.stringify(o)}`);
    
    if (o) {
      // 处理接收到的数据
      if (o.title) log(`页面标题: ${o.title}`);
      if (o.subTitle) log(`页面副标题: ${o.subTitle}`);
      if (o.objName) log(`对象名称: ${o.objName}`);
      if (o.useTitleBar !== undefined) log(`使用标题栏: ${o.useTitleBar}`);
      
      // 向JavaScript返回成功响应
      this.sendResponseToJS('client_isAlready', { success: true, message: '数据接收成功' });
    }
  } catch (error) {
    log(`client_isAlready 解析消息失败: ${error}`);
    this.sendResponseToJS('client_isAlready', { success: false, message: '数据解析失败' });
  }
}
```

#### 2.2 添加响应发送机制
```typescript
private sendResponseToJS(methodName: string, response: any) {
  try {
    if (this.controller) {
      const script = `
        (function() {
          // 如果页面定义了回调函数，则调用
          if (typeof window.${this.currentObj}_callback === 'function') {
            window.${this.currentObj}_callback('${methodName}', ${JSON.stringify(response)});
          }
          
          // 触发自定义事件
          if (typeof window.dispatchEvent === 'function') {
            var event = new CustomEvent('${this.currentObj}_${methodName}_response', {
              detail: ${JSON.stringify(response)}
            });
            window.dispatchEvent(event);
          }
          
          console.log('${methodName} 响应已发送:', ${JSON.stringify(response)});
        })();
      `;
      
      this.controller.runJavaScript(script);
    }
  } catch (error) {
    log(`发送响应到JavaScript失败: ${error}`);
  }
}
```

### 3. 前端页面适配

#### 3.1 监听原生对象准备就绪事件
```javascript
// 监听原生对象准备就绪事件
window.addEventListener('nativeReady', function(event) {
  console.log('收到 nativeReady 事件:', event.detail);
  
  // 自动调用 client_isAlready
  setTimeout(() => {
    if (typeof window.DDBESOFFICE !== 'undefined') {
      const params = {
        title: '页面标题',
        subTitle: '页面副标题',
        objName: 'DDBESOFFICE',
        useTitleBar: true
      };
      window.DDBESOFFICE.client_isAlready(JSON.stringify(params));
    }
  }, 100);
});
```

#### 3.2 安全的方法调用
```javascript
function callNativeMethod(methodName, params = {}) {
  try {
    if (typeof window.DDBESOFFICE !== 'undefined') {
      const nativeObj = window.DDBESOFFICE;
      if (typeof nativeObj[methodName] === 'function') {
        console.log(`调用原生方法: ${methodName}`);
        nativeObj[methodName](JSON.stringify(params));
        return true;
      } else {
        console.error(`方法 ${methodName} 不存在`);
        return false;
      }
    } else {
      console.error('原生对象 DDBESOFFICE 未找到');
      return false;
    }
  } catch (error) {
    console.error(`调用 ${methodName} 失败:`, error);
    return false;
  }
}
```

#### 3.3 监听方法响应
```javascript
// 监听原生方法响应
window.addEventListener('DDBESOFFICE_client_isAlready_response', function(event) {
  console.log('收到 client_isAlready 响应:', event.detail);
});

// 全局回调函数（备用方案）
window.DDBESOFFICE_callback = function(methodName, response) {
  console.log(`收到回调 ${methodName}:`, response);
};
```

## 测试方法

### 1. 使用测试页面
项目中已包含测试页面 `entry/src/main/resources/rawfile/test_webview.html`，可以用来验证 JavaScript 调用是否正常工作。

### 2. 测试步骤
1. 在 WebView 中加载测试页面
2. 观察控制台日志，确认原生对象是否正确注入
3. 点击测试按钮，验证各个方法是否能正常调用
4. 检查响应是否正确返回

### 3. 调试技巧
```typescript
// 在 HarmonyOS 端添加详细日志
log(`JavaScript对象检查结果: ${result}`);
log(`=== client_isAlready 方法被调用 ===`);
log(`接收到的消息: ${message}`);
```

```javascript
// 在前端页面添加详细日志
console.log('原生对象状态:', typeof window.DDBESOFFICE);
console.log('调用方法:', methodName, '参数:', params);
```

## 常见问题和解决方案

### 1. 原生对象未找到
**问题**: `typeof window.DDBESOFFICE === 'undefined'`
**解决**: 确保页面完全加载后再调用，监听 `nativeReady` 事件

### 2. 方法调用无响应
**问题**: 调用方法后没有任何反应
**解决**: 检查方法名是否在 `JSMethodArray` 中，确认参数格式正确

### 3. 参数解析失败
**问题**: `JSON.parse` 失败
**解决**: 确保传递的参数是有效的 JSON 字符串

### 4. 时机问题
**问题**: 页面加载时立即调用方法失败
**解决**: 使用事件监听或延迟调用机制

## 最佳实践

1. **延迟调用**: 页面加载完成后延迟 500ms 再调用原生方法
2. **错误处理**: 所有调用都要包含 try-catch 错误处理
3. **状态检查**: 调用前检查原生对象和方法是否存在
4. **日志记录**: 添加详细的日志记录便于调试
5. **响应机制**: 实现双向通信，原生方法执行后返回结果

通过以上优化，HarmonyOS WebView 中的 JavaScript 调用应该能够正常工作，与 Android/iOS 保持一致的行为。
