import { getMainSessionUnreadCount } from '../data_source/SessionDataSource';
import { SessionTabView } from './session/SessionTab';
import { WorkbenchView } from './workbench/WorkbenchView';
import { ContactTab } from './contact/ContactTab';
import { MineTab } from './mine/MineTab';


@Entry
@Component
struct DDMainPage {
  // 当前选中的Tab索引
  @State currentTabIndex: number = 0;
  // 消息分类Tab的当前索引
  @State currentMessageTabIndex: number = 0;
  /// todo 状态不能参数传递？？
  @State unreadCount: number = 0;

  // 组件生命周期 - 页面即将出现
  aboutToAppear(): void {
    this.loadData();
  }

  // 组件生命周期 - 页面即将消失
  aboutToDisappear(): void {
  }

  // 刷新未读数
  private async refreshUnreadCount(): Promise<void> {
    try {
      console.info('DDMainPage: 开始刷新未读数，当前值:', this.unreadCount);
      const newUnreadCount = await getMainSessionUnreadCount();
      console.info('DDMainPage: 从数据库获取到未读数:', newUnreadCount);

      if (newUnreadCount !== this.unreadCount) {
        console.info(`DDMainPage: 未读数更新 ${this.unreadCount} → ${newUnreadCount}`);
        this.unreadCount = newUnreadCount;
        console.info('DDMainPage: 未读数状态已更新为:', this.unreadCount);
      } else {
        console.info('DDMainPage: 未读数无变化，保持:', this.unreadCount);
      }
    } catch (error) {
      console.error('DDMainPage: 刷新未读数失败', error);
    }
  }

  // 处理SessionTab的未读数变化通知
  private handleUnreadCountChanged = (newCount: number): void => {
    console.info(`DDMainPage: 收到SessionTab未读数变化通知: ${this.unreadCount} → ${newCount}`);
    this.unreadCount = newCount;
  }

  // 加载数据
  private async loadData(): Promise<void> {
    try {
      // 添加延迟，确保数据库完全初始化
      await new Promise<void>(resolve => setTimeout(resolve, 500));

      // 加载未读数
      this.unreadCount = await getMainSessionUnreadCount();
      console.info('DDMainPage: 未读数加载成功:', this.unreadCount);

      console.info('DDMainPage: 已设置unreadCount状态为:', this.unreadCount);


    } catch (error) {
      console.error('DDMainPage: 加载数据失败', error);
    }
  }

  build() {
    Column() {

      // Tab组件
      Tabs({ index: $$this.currentTabIndex }) {
        // 消息Tab
        TabContent() {
          SessionTabView({ onUnreadCountChanged: this.handleUnreadCountChanged })
          // SessionTabView()
        }
        .tabBar(this.TabBarBuilder(0, '消息', '/resources/base/media/home_news.png',
          '/resources/base/media/home_news_selected.png'))


        // 工作台Tab
        TabContent() {
          WorkbenchView()
        }
        .tabBar(this.TabBarBuilder(1, '工作台', '/resources/base/media/home_news.png'))

        // 通讯录Tab
        TabContent() {
          this.ContactTabContent()
        }
        .tabBar(this.TabBarBuilder(2, '通讯录', '/resources/base/media/home_news.png'))

        // 我的Tab
        TabContent() {
          this.ProfileTabContent()
        }
        .tabBar(this.TabBarBuilder(3, '我的', '/resources/base/media/home_news.png'))
      }
      .layoutWeight(1)
      .vertical(false)
      .scrollable(false)
      .barMode(BarMode.Fixed)
      .barPosition(BarPosition.End)
      .onChange((index: number) => {
        this.currentTabIndex = index;
      })
    }
    .backgroundColor('#fff')
    .width('100%')
    .height('100%')
  }

  // 自定义TabBar构建器
  @Builder
  TabBarBuilder(index: number, title: string, icon: string, selectIcon?: string) {
    Column() {
      Stack() {
        Image(this.currentTabIndex == index ? selectIcon : icon)
          .width(28)
          .height(28)
          .fillColor(this.currentTabIndex === index ? '#1976D2' : '#888')

        if (index == 0 && this.unreadCount && this.unreadCount > 0) {
          Text(this.unreadCount > 99 ? '99+' : this.unreadCount.toString())
            .fontSize(10)
            .fontColor('#fff')
            .backgroundColor('#FF3B30')
            .borderRadius(8)
            .padding({
              left: 4,
              right: 4,
              top: 1,
              bottom: 1
            })
            .position({ x: 16, y: -6 })
        }
      }

      Text(title)
        .fontSize(12)
        .fontColor(this.currentTabIndex === index ? '#1976D2' : '#888')
        .margin({ top: 4 })
    }
    .justifyContent(FlexAlign.Center)
    .width('100%')
    .height(56)
  }

  // 我的Tab内容
  @Builder
  ProfileTabContent() {
    MineTab()
  }

  // 通讯录Tab内容
  @Builder
  ContactTabContent() {
    ContactTab()
  }
}