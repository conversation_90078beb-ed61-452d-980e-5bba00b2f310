import { router } from '@kit.ArkUI';
import { ImageViewerParam } from '../pages/image/ImageViewerParam';
import { log } from './Log';
import { routeTo } from './RouteHelper';

/**
 * 图片浏览器工具类
 */
export class ImageViewerUtil {
  
  /**
   * 打开图片浏览器
   * @param index 当前显示的图片索引
   * @param list 图片链接列表
   */
  static openImageViewer(context: UIContext , index: number, list: string[]) {
    try {
      const param = new ImageViewerParam(index, list);
      
      if (!param.isValid()) {
        log('图片浏览器参数无效');
        return;
      }

      log(`打开图片浏览器: 索引=${index}, 总数=${list.length}`);

      routeTo( context, 'pages/image/ImageViewerPage',param)

    } catch (error) {
      log(`打开图片浏览器异常: ${error}`);
    }
  }

  /**
   * 预览图片列表（从第一张开始）
   * @param list 图片链接列表
   */

  /**
   * 验证图片URL是否有效
   * @param url 图片URL
   * @returns 是否有效
   */
  static isValidImageUrl(url: string): boolean {
    if (!url || url.trim() === '') {
      return false;
    }

    // 检查是否为有效的URL格式
    try {
      // 支持http、https、file、resource等协议
      const validProtocols = ['http://', 'https://', 'file://', 'resource://'];
      const lowerUrl = url.toLowerCase();
      
      return validProtocols.some(protocol => lowerUrl.startsWith(protocol));
    } catch (error) {
      return false;
    }
  }


  /**
   * 创建图片浏览器参数
   * @param index 索引
   * @param list 图片列表
   * @returns 参数对象
   */
  static createImageViewerParam(index: number, list: string[]): ImageViewerParam {
    return new ImageViewerParam(index, list);
  }
}
