import { Columns, ColumnType, <PERSON><PERSON><PERSON>, Id, NotNull, Unique } from "@ohos/dataorm";

@Entity('USERS')
export class UserEntity {

  @Id({ isPrimaryKey: true, autoincrement: false })
  @Columns({ columnName: 'UID', types: ColumnType.str })
  @Unique()
  @NotNull()
  uid: string;

  @Columns({ columnName: 'NAME', types: ColumnType.str })
  name?: string;

  @Columns({ columnName: 'TOKEN', types: ColumnType.str })
  token?: string;

  @Columns({ columnName: 'CREATE_TIME', types: ColumnType.num })
  createTime: number;

  @Columns({ columnName: 'UPDATE_TIME', types: ColumnType.num })
  updateTime: number;

  constructor(uid?: string, name?: string, token?: string, createTime?: number, updateTime?: number) {
    this.uid = uid || '';
    this.name = name;
    this.token = token;
    this.createTime = createTime || Math.floor(Date.now() / 1000);
    this.updateTime = updateTime || Math.floor(Date.now() / 1000);
  }

  // Getter和Setter方法
  getUid(): string {
    return this.uid;
  }

  setUid(uid: string): void {
    this.uid = uid;
  }

  getName(): string | undefined {
    return this.name;
  }

  setName(name: string | undefined): void {
    this.name = name;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getToken(): string | undefined {
    return this.token;
  }

  setToken(token: string | undefined): void {
    this.token = token;
    this.updateTime = Math.floor(Date.now() / 1000);
  }

  getCreateTime(): number {
    return this.createTime;
  }

  setCreateTime(createTime: number): void {
    this.createTime = createTime;
  }

  getUpdateTime(): number {
    return this.updateTime;
  }

  setUpdateTime(updateTime: number): void {
    this.updateTime = updateTime;
  }


}