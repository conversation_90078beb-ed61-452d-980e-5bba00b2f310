export class MessageEntity {
  sender: 'me' | 'other';
  content: string;
  timestamp: number;
  id: string;
  type: 'text' | 'image' | 'voice' | 'video';
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

  constructor(sender: 'me' | 'other', content: string, type: 'text' | 'image' | 'voice' | 'video' = 'text') {
    this.sender = sender;
    this.content = content;
    this.type = type;
    this.timestamp = Date.now();
    this.id = this.generateId();
    this.status = sender === 'me' ? 'sending' : 'delivered';
  }

  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 格式化时间显示
  getFormattedTime(): string {
    const date = new Date(this.timestamp);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (messageDate.getTime() === today.getTime()) {
      // 今天的消息只显示时间
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else {
      // 其他日期显示日期和时间
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
}
