import { log } from '../../util/Log';
import { toast } from '../../util/Toast';
import { router } from '@kit.ArkUI';
import web_webview from '@ohos.web.webview'
import { JSON } from '@kit.ArkTS';
import { ClientIsAlreadyBody } from './WebParamEntity';

export const BASE_WIN_OBJ = 'DDBESOFFICE';

export class JsProxy {

  // 可变
  currentObj:string = BASE_WIN_OBJ

  controller?: web_webview.WebviewController

  constructor(controller: web_webview.WebviewController) {
    log(`wwwwww ${controller}`);
    this.controller = controller;
  }

  public client_isAlready(message: string) {
    let o = JSON.parse(message) as ClientIsAlreadyBody
    if (o) {

    }
    console.info(`JsProxy.client_isAlready 被调用: ${message}`);
  }

  public client_getToken(message: string) {
    console.info(`JsProxy.client_getToken 被调用: ${message}`);
  }

  public client_getUserInfo(message: string) {
    console.info(`JsProxy.client_getUserInfo 被调用: ${message}`);
  }

  public client_goBack(message: string) {
    console.info(`JsProxy.client_goBack 被调用: ${message}`);
    router.back();
  }

  public client_popBack(message: string) {
    console.info(`JsProxy.client_popBack 被调用: ${message}`);
    if (this.controller) {
      if (this.controller.accessBackward()) {
        this.controller?.backward()
      }else {
        router.back();
      }
    }
  }

}

export function callJS(controller: web_webview.WebviewController , obj:string ,method: string) {
  controller.runJavaScript(`javascript:window.${obj}.${method}()`);

}


export let JSMethodArray = [
  "client_isAlready",
  "client_getToken",
  "client_getUserInfo",
  "client_goBack",
  "client_popBack",
]

