/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
"use strict";

var $protobuf = require("protobufjs/light");

var $root = ($protobuf.roots["default"] || ($protobuf.roots["default"] = new $protobuf.Root()))
  .setOptions({
    optimize_for: "CODE_SIZE",
  })
  .addJSON({
    com: {
      nested: {
        joinu: {
          nested: {
            im: {
              nested: {
                protobuf: {
                  options: {
                    java_outer_classname: "MsgBean",
                  },
                  nested: {
                    ImMsg: {
                      oneofs: {
                        content: {
                          oneof: [
                            "exception",
                            "loginRequest",
                            "loginResult",
                            "c2cMsg",
                            "c2cMsgRequest",
                            "c2cMsgResponse",
                            "groupMsg",
                            "groupMsgRequest",
                            "groupMsgResponse",
                            "pushMsg",
                            "serverMsg",
                            "noticeMsg",
                            "ext",
                            "offline",
                            "offlineMsgRequest",
                            "kick",
                            "imPushMsg",
                          ],
                        },
                      },
                      fields: {
                        msgId: {
                          type: "string",
                          id: 1,
                        },
                        cmdId: {
                          type: "string",
                          id: 2,
                        },
                        type: {
                          type: "int32",
                          id: 16,
                        },
                        senderInfo: {
                          type: "UserInfo",
                          id: 17,
                        },
                        msgTime: {
                          type: "int64",
                          id: 18,
                        },
                        recordIgnore: {
                          type: "bool",
                          id: 21,
                        },
                        msgContent: {
                          type: "string",
                          id: 22,
                        },
                        selfMsg: {
                          type: "bool",
                          id: 25,
                        },
                        customMessage: {
                          type: "string",
                          id: 26,
                        },
                        exception: {
                          type: "Exception",
                          id: 3,
                        },
                        loginRequest: {
                          type: "LoginRequest",
                          id: 4,
                        },
                        loginResult: {
                          type: "LoginResult",
                          id: 5,
                        },
                        c2cMsg: {
                          type: "C2CMsg",
                          id: 6,
                        },
                        c2cMsgRequest: {
                          type: "C2CMsgRequest",
                          id: 7,
                        },
                        c2cMsgResponse: {
                          type: "C2CMsgResponse",
                          id: 8,
                        },
                        groupMsg: {
                          type: "GroupMsg",
                          id: 9,
                        },
                        groupMsgRequest: {
                          type: "GroupMsgRequest",
                          id: 10,
                        },
                        groupMsgResponse: {
                          type: "GroupMsgResponse",
                          id: 11,
                        },
                        pushMsg: {
                          type: "PushMsg",
                          id: 12,
                        },
                        serverMsg: {
                          type: "ServerMsg",
                          id: 13,
                        },
                        noticeMsg: {
                          type: "NoticeMsg",
                          id: 14,
                        },
                        ext: {
                          type: "ExtMsg",
                          id: 15,
                        },
                        offline: {
                          type: "MultipleMsg",
                          id: 19,
                        },
                        offlineMsgRequest: {
                          type: "OfflineMsgRequest",
                          id: 20,
                        },
                        kick: {
                          type: "KickMsg",
                          id: 23,
                        },
                        imPushMsg: {
                          type: "ImPushMsg",
                          id: 24,
                        },
                      },
                    },
                    ImPushMsg: {
                      oneofs: {
                        content: {
                          oneof: [
                            "blacklistAdd",
                            "blacklistRemove",
                            "singleMuteListAdd",
                            "singleMuteListRemove",
                            "groupMuteListAdd",
                            "groupMuteListRemove",
                            "warnMessage",
                          ],
                        },
                      },
                      fields: {
                        blacklistAdd: {
                          type: "string",
                          id: 1,
                        },
                        blacklistRemove: {
                          type: "string",
                          id: 2,
                        },
                        singleMuteListAdd: {
                          type: "string",
                          id: 3,
                        },
                        singleMuteListRemove: {
                          type: "string",
                          id: 4,
                        },
                        groupMuteListAdd: {
                          type: "string",
                          id: 5,
                        },
                        groupMuteListRemove: {
                          type: "string",
                          id: 6,
                        },
                        warnMessage: {
                          type: "WarnMessage",
                          id: 7,
                        },
                      },
                    },
                    WarnMessage: {
                      fields: {
                        msg: {
                          type: "string",
                          id: 1,
                        },
                        sessionId: {
                          type: "string",
                          id: 2,
                        },
                        sessionType: {
                          type: "SessionType",
                          id: 3,
                        },
                      },
                      nested: {
                        SessionType: {
                          values: {
                            NULL: 0,
                            C2C: 1,
                            GROUP: 2,
                          },
                        },
                      },
                    },
                    KickMsg: {
                      fields: {
                        kickType: {
                          type: "KickType",
                          id: 1,
                        },
                      },
                      nested: {
                        KickType: {
                          values: {
                            NULL: 0,
                            OtherDeviceLogin: 1,
                          },
                        },
                      },
                    },
                    OfflineMsgRequest: {
                      fields: {
                        offlineTime: {
                          type: "int64",
                          id: 1,
                        },
                        targetUserId: {
                          rule: "repeated",
                          type: "string",
                          id: 2,
                        },
                        groupId: {
                          rule: "repeated",
                          type: "string",
                          id: 3,
                        },
                      },
                    },
                    MultipleMsg: {
                      fields: {
                        msg: {
                          rule: "repeated",
                          type: "ImMsg",
                          id: 1,
                        },
                      },
                    },
                    NoticeMsg: {
                      fields: {
                        title: {
                          type: "string",
                          id: 1,
                        },
                        subtitle: {
                          type: "string",
                          id: 2,
                        },
                        context: {
                          type: "string",
                          id: 3,
                        },
                        data: {
                          type: "string",
                          id: 4,
                        },
                        ext: {
                          type: "string",
                          id: 5,
                        },
                        tempStatus: {
                          type: "int32",
                          id: 6,
                        },
                      },
                    },
                    ServerMsg: {
                      fields: {
                        noticeMsgUpdated: {
                          type: "ImMsg",
                          id: 1,
                        },
                      },
                    },
                    Exception: {
                      fields: {
                        msg: {
                          type: "string",
                          id: 1,
                        },
                        requestName: {
                          type: "int32",
                          id: 2,
                        },
                      },
                    },
                    LoginRequest: {
                      fields: {
                        token: {
                          type: "string",
                          id: 1,
                        },
                        clientType: {
                          type: "ClientTypeEnum",
                          id: 2,
                        },
                      },
                      nested: {
                        ClientTypeEnum: {
                          values: {
                            NULL: 0,
                            IOS: 1,
                            ANDROID: 2,
                            WEB: 3,
                            PC: 4,
                            OTHER: 5,
                            MOBILE: 6,
                          },
                        },
                      },
                    },
                    LoginResult: {
                      fields: {
                        success: {
                          type: "bool",
                          id: 1,
                        },
                        imUserId: {
                          type: "string",
                          id: 2,
                        },
                      },
                    },
                    ImgMsg: {
                      oneofs: {
                        image: {
                          oneof: ["url", "imageId"],
                        },
                      },
                      fields: {
                        thumbnail: {
                          type: "bytes",
                          id: 1,
                        },
                        url: {
                          type: "string",
                          id: 2,
                        },
                        imageId: {
                          type: "string",
                          id: 3,
                        },
                        thumbnailUrl: {
                          type: "string",
                          id: 4,
                        },
                        width: {
                          type: "float",
                          id: 5,
                        },
                        height: {
                          type: "float",
                          id: 6,
                        },
                      },
                    },
                    Voice: {
                      oneofs: {
                        voice: {
                          oneof: ["url", "voiceId"],
                        },
                      },
                      fields: {
                        url: {
                          type: "string",
                          id: 1,
                        },
                        voiceId: {
                          type: "string",
                          id: 3,
                        },
                        duration: {
                          type: "uint32",
                          id: 2,
                        },
                      },
                    },
                    Location: {
                      fields: {
                        title: {
                          type: "string",
                          id: 1,
                        },
                        address: {
                          type: "string",
                          id: 2,
                        },
                        latitude: {
                          type: "double",
                          id: 3,
                        },
                        longitude: {
                          type: "double",
                          id: 4,
                        },
                        uri: {
                          type: "string",
                          id: 5,
                        },
                      },
                    },
                    VideoMsg: {
                      oneofs: {
                        file: {
                          oneof: ["url", "fileId"],
                        },
                      },
                      fields: {
                        cover: {
                          type: "ImgMsg",
                          id: 1,
                        },
                        fileSize: {
                          type: "uint64",
                          id: 4,
                        },
                        duration: {
                          type: "uint32",
                          id: 5,
                        },
                        url: {
                          type: "string",
                          id: 2,
                        },
                        fileId: {
                          type: "string",
                          id: 3,
                        },
                      },
                    },
                    FileMsg: {
                      oneofs: {
                        file: {
                          oneof: ["url", "fileId"],
                        },
                      },
                      fields: {
                        name: {
                          type: "string",
                          id: 1,
                        },
                        fileSize: {
                          type: "uint64",
                          id: 4,
                        },
                        url: {
                          type: "string",
                          id: 2,
                        },
                        fileId: {
                          type: "string",
                          id: 3,
                        },
                      },
                    },
                    UserInfo: {
                      fields: {
                        nickname: {
                          type: "string",
                          id: 1,
                        },
                        userId: {
                          type: "string",
                          id: 2,
                        },
                        avatar: {
                          type: "string",
                          id: 3,
                        },
                        imUserId: {
                          type: "string",
                          id: 4,
                        },
                      },
                    },
                    AudioAndVideoCall: {
                      fields: {
                        durationType: {
                          type: "DurationType",
                          id: 1,
                        },
                        content: {
                          type: "string",
                          id: 2,
                        },
                        metingId: {
                          type: "string",
                          id: 3,
                        },
                      },
                      nested: {
                        DurationType: {
                          values: {
                            NULL: 0,
                            VOICE: 1,
                            VIDEO: 2,
                          },
                        },
                      },
                    },
                    Withdraw: {
                      fields: {
                        msgId: {
                          type: "string",
                          id: 1,
                        },
                      },
                    },
                    ReadMsg: {
                      fields: {
                        msgId: {
                          rule: "repeated",
                          type: "string",
                          id: 1,
                        },
                      },
                    },
                    MsgFailedType: {
                      values: {
                        NULL: 0,
                      },
                    },
                    C2CMsg: {
                      oneofs: {
                        Context: {
                          oneof: [
                            "msg",
                            "image",
                            "voice",
                            "location",
                            "video",
                            "file",
                            "withdraw",
                            "read",
                            "ext",
                            "call",
                          ],
                        },
                      },
                      fields: {
                        from: {
                          type: "string",
                          id: 1,
                        },
                        conversationId: {
                          type: "uint32",
                          id: 4,
                        },
                        receiver: {
                          type: "string",
                          id: 16,
                        },
                        msg: {
                          type: "string",
                          id: 6,
                        },
                        image: {
                          type: "ImgMsg",
                          id: 7,
                        },
                        voice: {
                          type: "Voice",
                          id: 8,
                        },
                        location: {
                          type: "Location",
                          id: 9,
                        },
                        video: {
                          type: "VideoMsg",
                          id: 10,
                        },
                        file: {
                          type: "FileMsg",
                          id: 11,
                        },
                        withdraw: {
                          type: "Withdraw",
                          id: 12,
                        },
                        read: {
                          type: "ReadMsg",
                          id: 13,
                        },
                        ext: {
                          type: "ExtMsg",
                          id: 14,
                        },
                        call: {
                          type: "AudioAndVideoCall",
                          id: 15,
                        },
                      },
                    },
                    C2CMsgRequest: {
                      oneofs: {
                        Context: {
                          oneof: [
                            "msg",
                            "image",
                            "voice",
                            "location",
                            "withdraw",
                            "read",
                            "ext",
                            "video",
                            "file",
                            "call",
                          ],
                        },
                      },
                      fields: {
                        receiver: {
                          type: "string",
                          id: 1,
                        },
                        msg: {
                          type: "string",
                          id: 3,
                        },
                        image: {
                          type: "ImgMsg",
                          id: 4,
                        },
                        voice: {
                          type: "Voice",
                          id: 5,
                        },
                        location: {
                          type: "Location",
                          id: 6,
                        },
                        withdraw: {
                          type: "Withdraw",
                          id: 7,
                        },
                        read: {
                          type: "ReadMsg",
                          id: 8,
                        },
                        ext: {
                          type: "ExtMsg",
                          id: 9,
                        },
                        video: {
                          type: "VideoMsg",
                          id: 10,
                        },
                        file: {
                          type: "FileMsg",
                          id: 11,
                        },
                        call: {
                          type: "AudioAndVideoCall",
                          id: 12,
                        },
                      },
                    },
                    FailType: {
                      values: {
                        UNKNOWN: 0,
                        IN_BLACKLIST: 1,
                        TARGET_NOT_LOGIN: 2,
                        MSG_SAVE_FAILURE: 3,
                        TARGET_IS_EMPTY: 4,
                        GROUP_NOT_EXIST: 5,
                        GROUP_MUTED: 6,
                        GROUP_USER_MUTED: 7,
                        USER_NOT_IN_GROUP: 8,
                      },
                    },
                    C2CMsgResponse: {
                      fields: {
                        receiver: {
                          type: "string",
                          id: 1,
                        },
                        success: {
                          type: "bool",
                          id: 2,
                        },
                        failedType: {
                          type: "MsgFailedType",
                          id: 3,
                        },
                        failType: {
                          type: "FailType",
                          id: 4,
                        },
                      },
                    },
                    PushMsg: {
                      fields: {
                        msg: {
                          type: "string",
                          id: 1,
                        },
                        type: {
                          type: "int32",
                          id: 2,
                        },
                      },
                    },
                    GroupMsg: {
                      oneofs: {
                        Context: {
                          oneof: [
                            "msg",
                            "image",
                            "voice",
                            "location",
                            "ext",
                            "withdraw",
                            "video",
                            "file",
                            "call",
                            "noticeMsg",
                          ],
                        },
                      },
                      fields: {
                        appId: {
                          type: "string",
                          id: 14,
                        },
                        groupId: {
                          type: "string",
                          id: 5,
                        },
                        groupName: {
                          type: "string",
                          id: 12,
                        },
                        groupLogo: {
                          type: "string",
                          id: 13,
                        },
                        msg: {
                          type: "string",
                          id: 1,
                        },
                        image: {
                          type: "ImgMsg",
                          id: 2,
                        },
                        voice: {
                          type: "Voice",
                          id: 3,
                        },
                        location: {
                          type: "Location",
                          id: 4,
                        },
                        ext: {
                          type: "ExtMsg",
                          id: 6,
                        },
                        withdraw: {
                          type: "Withdraw",
                          id: 7,
                        },
                        video: {
                          type: "VideoMsg",
                          id: 8,
                        },
                        file: {
                          type: "FileMsg",
                          id: 9,
                        },
                        call: {
                          type: "AudioAndVideoCall",
                          id: 10,
                        },
                        noticeMsg: {
                          type: "NoticeMsg",
                          id: 11,
                        },
                      },
                    },
                    ExtMsg: {
                      fields: {
                        ext: {
                          type: "google.protobuf.Any",
                          id: 1,
                        },
                        ext1: {
                          type: "string",
                          id: 2,
                        },
                        ext2: {
                          type: "string",
                          id: 3,
                        },
                        ext3: {
                          type: "string",
                          id: 4,
                        },
                      },
                    },
                    GroupMsgRequest: {
                      oneofs: {
                        Context: {
                          oneof: [
                            "msg",
                            "image",
                            "voice",
                            "location",
                            "ext",
                            "withdraw",
                            "video",
                            "file",
                            "call",
                          ],
                        },
                      },
                      fields: {
                        groupId: {
                          type: "string",
                          id: 5,
                        },
                        groupName: {
                          type: "string",
                          id: 11,
                        },
                        groupLogo: {
                          type: "string",
                          id: 12,
                        },
                        msg: {
                          type: "string",
                          id: 1,
                        },
                        image: {
                          type: "ImgMsg",
                          id: 2,
                        },
                        voice: {
                          type: "Voice",
                          id: 3,
                        },
                        location: {
                          type: "Location",
                          id: 4,
                        },
                        ext: {
                          type: "ExtMsg",
                          id: 6,
                        },
                        withdraw: {
                          type: "Withdraw",
                          id: 7,
                        },
                        video: {
                          type: "VideoMsg",
                          id: 8,
                        },
                        file: {
                          type: "FileMsg",
                          id: 9,
                        },
                        call: {
                          type: "AudioAndVideoCall",
                          id: 10,
                        },
                      },
                    },
                    GroupMsgResponse: {
                      fields: {
                        groupId: {
                          type: "string",
                          id: 1,
                        },
                        success: {
                          type: "bool",
                          id: 2,
                        },
                        failedType: {
                          type: "MsgFailedType",
                          id: 3,
                        },
                        failType: {
                          type: "FailType",
                          id: 4,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    google: {
      nested: {
        protobuf: {
          nested: {
            Any: {
              fields: {
                type_url: {
                  type: "string",
                  id: 1,
                },
                value: {
                  type: "bytes",
                  id: 2,
                },
              },
            },
          },
        },
      },
    },
  });

module.exports = $root;
