import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from '@ohos/axios';
import { NetworkConfig, ApiResponse, NetworkError, NetworkErrorType, RequestOptions } from './NetworkConfig';
import { HttpLogger } from './HttpLogger';
import { APIConfig } from './API';

/**
 * HTTP客户端封装
 */
export class HttpClient {
  private static instance: HttpClient;
  private axiosInstance: AxiosInstance;

  private constructor( mBaseUrl?:string) {
    this.axiosInstance = axios.create({
      baseURL: mBaseUrl ?? APIConfig.BASE_URL,
      timeout: NetworkConfig.TIMEOUT,
      headers: {
        'Content-Type': NetworkConfig.CONTENT_TYPE,
        'Accept': NetworkConfig.ACCEPT
      }
    });

    this.setupInterceptors(mBaseUrl);
  }

  /**
   * 获取单例实例
   */
  public static getInstance(baseUrl?:string): HttpClient {
    if (!HttpClient.instance) {
      HttpClient.instance = new HttpClient(baseUrl);
    }

    else if (baseUrl && HttpClient.instance.axiosInstance.defaults.baseURL !== baseUrl) {
      // 如果实例已存在，但传入了不同的 baseURL，则更新 baseURL。
      // 注意：这会改变所有后续请求的 baseURL，请根据实际业务需求决定是否允许。
      HttpClient.instance.axiosInstance.defaults.baseURL = baseUrl;
      console.warn(`HttpClient: 单例实例的 baseURL 已更新为: ${baseUrl}`);
    }
    return HttpClient.instance;
  }


  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(baseUrl?: string): void {
    // 简化拦截器，避免复杂的类型转换
    console.info('HttpClient: 拦截器已设置，将在请求方法中记录日志');

  }



  /**
   * 记录请求日志
   */
  private logRequest(config: AxiosRequestConfig, requestId: string): void {
    if (!NetworkConfig.ENABLE_REQUEST_LOG) {
      return;
    }

    const timestamp = new Date().toISOString();
    const method = config.method?.toUpperCase() || 'GET';
    const url = config.url || '';

    console.info('='.repeat(80));
    console.info(`🚀 [REQUEST ${requestId}] ${timestamp}`);
    console.info(`📍 ${method} ${config.baseURL || ''}${url}`);

    // 打印请求头
    if (config.headers) {
      console.info('📋 Headers:');
      const headers = config.headers as Record<string, string>;
      Object.keys(headers).forEach(key => {
        const value = headers[key];
        if (key.toLowerCase() === 'authorization') {
          console.info(`  ${key}: ${this.maskToken(value)}`);
        } else {
          console.info(`  ${key}: ${value}`);
        }
      });
    }

    // 打印查询参数
    if (config.params) {
      console.info('🔍 Query Params:');
      console.info(JSON.stringify(config.params, null, 2));
    }

    // 打印请求体
    if (config.data) {
      console.info('📦 Request Body:');
      console.info(JSON.stringify(config.data, null, 2));
    }

    console.info('='.repeat(80));
  }

  /**
   * 记录响应日志
   */
  private logResponse(response: AxiosResponse, requestId: string, duration: number): void {
    if (!NetworkConfig.ENABLE_RESPONSE_LOG) {
      return;
    }

    const timestamp = new Date().toISOString();
    const method = response.config?.method?.toUpperCase() || 'GET';
    const url = response.config?.url || '';

    console.info('='.repeat(80));
    console.info(`✅ [RESPONSE ${requestId}] ${timestamp} (${duration}ms)`);
    console.info(`📍 ${method} ${url}`);
    console.info(`📊 Status: ${response.status} ${response.statusText}`);

    // 打印响应数据
    console.info('📦 Response Data:');
    if (response.data) {
      const dataStr = JSON.stringify(response.data, null, 2);
      if (dataStr.length > NetworkConfig.LOG_MAX_BODY_SIZE) {
        console.info(dataStr.substring(0, NetworkConfig.LOG_MAX_BODY_SIZE) + '... (truncated)');
        console.info(`📏 Full response size: ${dataStr.length} characters`);
      } else {
        console.info(dataStr);
      }
    } else {
      console.info('  (empty response)');
    }

    console.info('='.repeat(80));
  }

  /**
   * 记录错误日志
   */
  private logError(error: Object, requestId: string, duration: number): void {
    const timestamp = new Date().toISOString();

    console.error('='.repeat(80));
    console.error(`❌ [ERROR ${requestId}] ${timestamp} (${duration}ms)`);
    console.error('🔍 Error Details:', error);
    console.error('='.repeat(80));
  }

  /**
   * 隐藏Token敏感信息
   */
  private maskToken(token: string): string {
    if (!token || typeof token !== 'string') {
      return '****';
    }

    if (token.startsWith('Bearer ')) {
      const actualToken = token.substring(7);
      if (actualToken.length > 10) {
        return `Bearer ${actualToken.substring(0, 6)}****${actualToken.substring(actualToken.length - 4)}`;
      }
      return 'Bearer ****';
    }

    if (token.length > 10) {
      return `${token.substring(0, 4)}****${token.substring(token.length - 4)}`;
    }

    return '****';
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): NetworkError {
    console.error('HttpClient: 处理错误', error);
    return new NetworkError(NetworkErrorType.NETWORK_ERROR, undefined, error.message || '网络请求失败');
  }

  /**
   * 通用请求方法
   */
  public async request<T = Object>(options: RequestOptions): Promise<ApiResponse<T>> {
    // const requestId = HttpLogger.generateRequestId();
    const requestId = Math.random().toString(36).substring(2, 8).toUpperCase();;
    const startTime = Date.now();

    try {
      const headers: Record<string, string> = {
        'Content-Type': NetworkConfig.CONTENT_TYPE,
        'Accept': NetworkConfig.ACCEPT
      };

      // 添加自定义头部
      if (options.headers) {
        Object.keys(options.headers).forEach(key => {
          headers[key] = options.headers![key];
        });
      }

      // 添加Authorization头
      if (options.token) {
        headers['Authorization'] = `Bearer ${options.token}`;
      }

      if (options.imToken) {
        headers['ImToken'] = `${options.imToken}`;
      }


      const config: AxiosRequestConfig = {
        url: options.url,
        method: options.method || 'GET',
        params: options.params,
        data: options.data,
        timeout: options.timeout || NetworkConfig.TIMEOUT,
        headers: headers
      };

      // 记录请求日志
      this.logRequest(config, requestId);

      const response: AxiosResponse<ApiResponse<T>> = await this.axiosInstance.request<ApiResponse<T>>(config);

      // 记录响应日志
      this.logResponse(response, requestId, Date.now() - startTime);

      // 检查业务状态码
      if (response.data.code !== 1) {
        throw new NetworkError(
          NetworkErrorType.SERVER_ERROR,
          response.data.code,
          response.data.msg || '业务处理失败'
        );
      }

      return response.data;
    } catch (error) {
      // 记录错误日志
      this.logError(error, requestId, Date.now() - startTime);

      if (error instanceof NetworkError) {
        throw error;
      }
      throw this.handleError(error as Error);
    }
  }

  /**
   * GET请求
   */
  public async get<T = Object>(
    url: string,
    params?: Record<string, Object>,
    token?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'GET',
      params,
      token: token || NetworkConfig.DEFAULT_TOKEN,
      imToken: NetworkConfig.IM_TOKEN

    });
  }

  /**
   * POST请求
   */
  public async post<T = Object>(
    url: string,
    data?: Object,
    token?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      token: token || NetworkConfig.DEFAULT_TOKEN,
      imToken: NetworkConfig.IM_TOKEN
    });
  }

  /**
   * PUT请求
   */
  public async put<T = Object>(
    url: string,
    data?: Object,
    token?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      token: token || NetworkConfig.DEFAULT_TOKEN,
      imToken: NetworkConfig.IM_TOKEN
    });
  }

  /**
   * DELETE请求
   */
  public async delete<T = Object>(
    url: string,
    params?: Record<string, Object>,
    token?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      params,
      token: token || NetworkConfig.DEFAULT_TOKEN,
      imToken: NetworkConfig.IM_TOKEN
    });
  }
}

// 导出单例实例
export const httpClient = HttpClient.getInstance();
