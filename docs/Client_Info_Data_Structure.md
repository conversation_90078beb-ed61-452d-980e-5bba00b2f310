# 客户端信息数据结构

## 📋 **实际数据结构分析**

根据您提供的真实数据，我重新组织了数据结构：

### 原始JSON数据
```json
{
  "statusHeight": 31,
  "company": {
    "companyId": "2603725684413039614",
    "content": "去",
    "deptId": "0",
    "haveApprove": 0,
    "isOuter": false,
    "isSelected": false,
    "logo": "https://cdn.ddbes.com/LOGO/1701321231053.jpg",
    "name": "ljt企业",
    "noticeId": "2603778546870518782",
    "power": "123456",
    "rejectInvitation": 0,
    "rejectJoin": 0
  },
  "user": {
    "address": "北京市北京市朝阳区",
    "avatar": "https://cdn.ddbes.com/HEAD/2596847832233149437_I7CqmAMmsNNIHQUvcw_1738715060045.png",
    "birthday": "2006-12-23",
    "companyId": "805",
    "email": "<EMAIL>",
    "gender": "1",
    "intro": "",
    "mobile": "18600244913",
    "name": "梁江涛",
    "nickName": "Sprit^Moon",
    "openId": "oRiTw02SL08CnSJckEPGbSDSIdKY",
    "profession": "外汇经纪人",
    "userId": "2596847832233149437"
  },
  "token": "Bearer 185406c3-9188-4afe-84f2-6f7aa60cd640"
}
```

## 🏗️ **TypeScript接口定义**

### 1. **用户信息接口**
```typescript
interface UserInfo {
  address: string;        // 地址
  avatar: string;         // 头像URL
  birthday: string;       // 生日
  companyId: string;      // 公司ID
  email: string;          // 邮箱
  gender: string;         // 性别 (1=男, 0=女)
  intro: string;          // 个人介绍
  mobile: string;         // 手机号
  name: string;           // 真实姓名
  nickName: string;       // 昵称
  openId: string;         // 微信OpenID
  profession: string;     // 职业
  userId: string;         // 用户ID
}
```

### 2. **公司信息接口**
```typescript
interface CompanyInfo {
  companyId: string;      // 公司ID
  content: string;        // 内容描述
  deptId: string;         // 部门ID
  haveApprove: number;    // 是否有审批权限
  isOuter: boolean;       // 是否外部用户
  isSelected: boolean;    // 是否选中
  logo: string;           // 公司Logo URL
  name: string;           // 公司名称
  noticeId: string;       // 通知ID
  power: string;          // 权限码
  rejectInvitation: number; // 拒绝邀请状态
  rejectJoin: number;     // 拒绝加入状态
}
```

### 3. **客户端信息接口**
```typescript
interface ClientInfo {
  statusHeight: number;   // 状态栏高度
  company: CompanyInfo;   // 公司信息
  user: UserInfo;         // 用户信息
  token: string;          // 认证Token
}
```

## 🔄 **数据流程**

### 1. **接收流程**
```
JavaScript调用 → client_isAlready → 解析参数 → 触发updateClientInfo
```

### 2. **回调流程**
```
构建ClientInfo → JSON序列化 → 调用JS的updateClientInfo方法
```

### 3. **JavaScript调用**
```javascript
// HarmonyOS调用JavaScript
window.ddbes_web.updateClientInfo('{"statusHeight":31,"company":{...},"user":{...},"token":"..."}')
```

## 📱 **实际使用示例**

### HarmonyOS端代码
```typescript
// 构建真实数据
const clientInfo: ClientInfo = {
  statusHeight: 31,
  company: {
    companyId: "2603725684413039614",
    name: "ljt企业",
    logo: "https://cdn.ddbes.com/LOGO/1701321231053.jpg",
    // ... 其他字段
  },
  user: {
    userId: "2596847832233149437",
    name: "梁江涛",
    nickName: "Sprit^Moon",
    avatar: "https://cdn.ddbes.com/HEAD/...",
    mobile: "18600244913",
    // ... 其他字段
  },
  token: "Bearer 185406c3-9188-4afe-84f2-6f7aa60cd640"
};

// 调用JavaScript
this.callJavaScriptMethod('updateClientInfo', JSON.stringify(clientInfo));
```

### JavaScript端接收
```javascript
window.ddbes_web = {
  updateClientInfo: function(clientInfoJson) {
    const clientInfo = JSON.parse(clientInfoJson);
    console.log('用户信息:', clientInfo.user);
    console.log('公司信息:', clientInfo.company);
    console.log('Token:', clientInfo.token);
    console.log('状态栏高度:', clientInfo.statusHeight);
    
    // 使用数据更新页面
    updateUserProfile(clientInfo.user);
    updateCompanyInfo(clientInfo.company);
    setAuthToken(clientInfo.token);
  }
};
```

## 🎯 **关键特点**

### 1. **完整性**
- 包含用户的完整个人信息
- 包含公司的详细信息
- 包含认证和权限信息

### 2. **实用性**
- 状态栏高度用于UI适配
- Token用于API认证
- 用户信息用于个性化显示

### 3. **扩展性**
- 接口设计支持添加新字段
- 数据结构清晰，易于维护

## 🚀 **测试验证**

### 预期日志输出
```
🎉🎉🎉 === client_isAlready 方法被调用 === 🎉🎉🎉
📤 开始向Web页面回调客户端信息...
📋 客户端信息: {"statusHeight":31,"company":{...},"user":{...},"token":"..."}
📞 调用JavaScript: javascript:window.ddbes_web.updateClientInfo('...')
```

### JavaScript端验证
在浏览器控制台应该能看到：
- 用户信息正确显示
- 公司信息正确显示
- Token正确设置
- 状态栏高度正确应用

这个数据结构完全匹配您提供的实际数据，确保了与现有系统的完美兼容！
