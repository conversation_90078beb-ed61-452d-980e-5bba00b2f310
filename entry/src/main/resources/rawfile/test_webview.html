<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HarmonyOS WebView 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px 4px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #0056CC;
        }
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HarmonyOS WebView JavaScript 调用测试</h1>
        
        <div id="status" class="status info">
            等待原生对象初始化...
        </div>
        
        <div>
            <h3>测试按钮</h3>
            <button class="button" onclick="testClientIsAlready()">测试 client_isAlready</button>
            <button class="button" onclick="testGetToken()">测试 client_getToken</button>
            <button class="button" onclick="testGetUserInfo()">测试 client_getUserInfo</button>
            <button class="button" onclick="testGoBack()">测试 client_goBack</button>
            <button class="button" onclick="testPopBack()">测试 client_popBack</button>
        </div>
        
        <div>
            <h3>自动测试</h3>
            <button class="button" onclick="runAutoTest()">运行自动测试</button>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div>
            <h3>调用日志</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        const NATIVE_OBJECT = 'DDBESOFFICE';
        let logContainer = document.getElementById('log');
        let statusContainer = document.getElementById('status');
        
        // 日志记录函数
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // 更新状态
        function updateStatus(message, type = 'info') {
            statusContainer.textContent = message;
            statusContainer.className = `status ${type}`;
        }
        
        // 检查原生对象是否存在
        function checkNativeObject() {
            if (typeof window[NATIVE_OBJECT] !== 'undefined') {
                addLog(`✅ 原生对象 ${NATIVE_OBJECT} 已找到`, 'success');
                updateStatus('原生对象已准备就绪', 'success');
                return true;
            } else {
                addLog(`❌ 原生对象 ${NATIVE_OBJECT} 未找到`, 'error');
                updateStatus('原生对象未找到', 'error');
                return false;
            }
        }
        
        // 安全调用原生方法
        function callNativeMethod(methodName, params = {}) {
            try {
                if (!checkNativeObject()) {
                    return false;
                }
                
                const nativeObj = window[NATIVE_OBJECT];
                if (typeof nativeObj[methodName] === 'function') {
                    addLog(`📞 调用原生方法: ${methodName}`, 'info');
                    addLog(`📤 发送参数: ${JSON.stringify(params)}`, 'info');
                    nativeObj[methodName](JSON.stringify(params));
                    return true;
                } else {
                    addLog(`❌ 方法 ${methodName} 不存在`, 'error');
                    return false;
                }
            } catch (error) {
                addLog(`❌ 调用 ${methodName} 失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        // 测试方法
        function testClientIsAlready() {
            const params = {
                title: '测试页面标题',
                subTitle: '测试副标题',
                objName: NATIVE_OBJECT,
                useTitleBar: true
            };
            callNativeMethod('client_isAlready', params);
        }
        
        function testGetToken() {
            callNativeMethod('client_getToken', { requestId: Date.now() });
        }
        
        function testGetUserInfo() {
            callNativeMethod('client_getUserInfo', { requestId: Date.now() });
        }
        
        function testGoBack() {
            callNativeMethod('client_goBack', { reason: '用户点击返回' });
        }
        
        function testPopBack() {
            callNativeMethod('client_popBack', { reason: '用户点击弹出返回' });
        }
        
        // 自动测试
        function runAutoTest() {
            addLog('🚀 开始自动测试...', 'info');
            
            setTimeout(() => testClientIsAlready(), 500);
            setTimeout(() => testGetToken(), 1000);
            setTimeout(() => testGetUserInfo(), 1500);
            
            addLog('✅ 自动测试完成', 'success');
        }
        
        // 清空日志
        function clearLog() {
            logContainer.innerHTML = '';
        }
        
        // 监听原生对象准备就绪事件
        window.addEventListener('nativeReady', function(event) {
            addLog(`🎉 收到 nativeReady 事件: ${JSON.stringify(event.detail)}`, 'success');
            updateStatus('原生对象已准备就绪', 'success');
            
            // 自动调用 client_isAlready
            setTimeout(() => {
                addLog('🔄 自动调用 client_isAlready...', 'info');
                testClientIsAlready();
            }, 100);
        });
        
        // 监听原生方法响应
        window.addEventListener('DDBESOFFICE_client_isAlready_response', function(event) {
            addLog(`📥 收到 client_isAlready 响应: ${JSON.stringify(event.detail)}`, 'success');
        });
        
        window.addEventListener('DDBESOFFICE_client_getToken_response', function(event) {
            addLog(`📥 收到 client_getToken 响应: ${JSON.stringify(event.detail)}`, 'success');
        });
        
        window.addEventListener('DDBESOFFICE_client_getUserInfo_response', function(event) {
            addLog(`📥 收到 client_getUserInfo 响应: ${JSON.stringify(event.detail)}`, 'success');
        });
        
        // 全局回调函数（备用方案）
        window.DDBESOFFICE_callback = function(methodName, response) {
            addLog(`📥 收到回调 ${methodName}: ${JSON.stringify(response)}`, 'success');
        };
        
        // 页面加载完成后的初始化
        window.addEventListener('DOMContentLoaded', function() {
            addLog('📄 页面加载完成', 'info');

            // 立即检查一次
            checkNativeObject();

            // 延迟检查原生对象
            setTimeout(() => {
                addLog('🔍 1秒后检查原生对象...', 'info');
                if (checkNativeObject()) {
                    // 如果原生对象已存在，自动调用 client_isAlready
                    setTimeout(() => {
                        addLog('🔄 页面加载后自动调用 client_isAlready...', 'info');
                        testClientIsAlready();
                    }, 500);
                }
            }, 1000);

            // 再次延迟检查
            setTimeout(() => {
                addLog('🔍 3秒后再次检查原生对象...', 'info');
                if (checkNativeObject()) {
                    addLog('🔄 3秒后自动调用 client_isAlready...', 'info');
                    testClientIsAlready();
                }
            }, 3000);
        });
        
        // 定期检查原生对象
        let checkInterval = setInterval(() => {
            if (checkNativeObject()) {
                clearInterval(checkInterval);
            }
        }, 1000);
        
        addLog('🌐 测试页面初始化完成', 'info');
    </script>
</body>
</html>
